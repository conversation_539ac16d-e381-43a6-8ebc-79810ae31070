package util

import (
	"reflect"
	"testing"
)

func TestStringToSlice(t *testing.T) {
	type args struct {
		str string
		sep string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "",
			args: args{
				str: "we2we",
				sep: "2",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StringToSlice(tt.args.str, tt.args.sep); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StringToSlice() = %v, want %v", got, tt.want)
			}
		})
	}
}
