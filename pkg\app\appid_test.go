package app

import (
	"reflect"
	"testing"
)

func TestGetStoreMasterId(t *testing.T) {
	type args struct {
		appId     string
		channelId int32
	}
	tests := []struct {
		name              string
		args              args
		wantStoreMasterId int32
		wantRetCode       int
	}{
		{
			name: "",
			args: args{
				appId:     "33",
				channelId: Channel_JDDJ,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotStoreMasterId, gotRetCode := GetStoreMasterId(tt.args.appId, tt.args.channelId)
			if gotStoreMasterId != tt.wantStoreMasterId {
				t.Errorf("GetStoreMasterId() gotStoreMasterId = %v, want %v", gotStoreMasterId, tt.wantStoreMasterId)
			}
			if gotRetCode != tt.wantRetCode {
				t.Errorf("GetStoreMasterId() gotRetCode = %v, want %v", gotRetCode, tt.wantRetCode)
			}
		})
	}
}

func TestGetStoreMasterChannelAppConfig(t *testing.T) {
	type args struct {
		id        int32
		channelId int32
	}
	tests := []struct {
		name          string
		args          args
		wantAppConfig *ChannelAppConfig
		wantRetCode   int
	}{
		{
			name: "",
			args: args{
				id:        33,
				channelId: Channel_JDDJ,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotAppConfig, gotRetCode := GetStoreMasterChannelAppConfig(tt.args.id, tt.args.channelId)
			if !reflect.DeepEqual(gotAppConfig, tt.wantAppConfig) {
				t.Errorf("GetStoreMasterChannelAppConfig() gotAppConfig = %v, want %v", gotAppConfig, tt.wantAppConfig)
			}
			if gotRetCode != tt.wantRetCode {
				t.Errorf("GetStoreMasterChannelAppConfig() gotRetCode = %v, want %v", gotRetCode, tt.wantRetCode)
			}
		})
	}
}
