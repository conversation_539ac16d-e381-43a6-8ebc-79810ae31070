package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/is"
	"external-ui/utils"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"io/ioutil"
	"runtime"
)

// 保险业务的 的grpc 连接方法
func GetInsuranceCenterGrpcConn() *grpc.ClientConn {
	GrpcAddress := config.GetString("grpc.insurance-center")
	if GrpcAddress == "" || runtime.GOOS == "windows" {
		GrpcAddress = "localhost:7054"
	}
	conn, err := grpc.Dial(GrpcAddress, grpc.WithInsecure())
	if err != nil {
		//glog.Fatalf("did not connect: %v", err)
	}
	return conn
}

func GetSecret() string {
	params := config.GetString("insurance.secret")
	return params

}

// 保险数据线上订单回调信息
func InsuranceOrderOnLine(ctx echo.Context) error {
	out := dto.InsuranceBaseResponse{}
	body, _ := ioutil.ReadAll(ctx.Request().Body)
	//验签
	timestamp := ctx.Request().Header.Get("timestamp")
	signStr := ctx.Request().Header.Get("signStr")
	model := new(dto.InsuranceOnLineRequest)

	err := json.Unmarshal(body, &model)
	if err != nil {
		glog.Error("解析参数错误")
		out.ErrCode = 400
		out.ErrMsg = err.Error()
		return ctx.JSON(400, out)
	}
	arrReceive := make(map[string]string)
	arrReceive["timestamp"] = timestamp
	arrReceive["secret"] = GetSecret()
	arrReceive["body"] = string(body)
	signReceive := utils.ElmSign(arrReceive)
	if signStr != signReceive {
		//errno = -1
		glog.Error("验签不通过！！______", signReceive)
		out.ErrCode = 400
		out.ErrMsg = "验签不通过！！______" + signReceive
		return ctx.JSON(400, out)
	}

	out.BusinessNo = model.BusinessNo
	conn := GetInsuranceCenterGrpcConn()
	defer conn.Close()
	client := is.NewYangZiJiangServicesClient(conn)

	modelpar := new(is.InsuranceOnLineRequest)
	modelpar.BusinessNo = model.BusinessNo
	modelpar.PolicyNo = model.PolicyNo
	modelpar.PayApplyNo = model.PayApplyNo
	modelpar.PayAmount = int32(model.PayAmount)
	modelpar.PayTime = model.PayTime
	modelpar.ElecPolicyUrl = model.ElecPolicyUrl
	modelpar.TotalInsuredAmt = int32(model.TotalInsuredAmt)
	modelpar.ActualPremiumAmt = int32(model.ActualPremiumAmt)
	modelpar.InsuredEndTime = model.InsuredEndTime
	modelpar.InsuredBgnTime = model.InsuredBgnTime

	//投保人
	modelpar.Holder = new(is.Holder)
	modelpar.Holder.HolderName = model.Holder.HolderName
	modelpar.Holder.IdcartType = model.Holder.IdcartType
	modelpar.Holder.IdcartNo = model.Holder.IdcartNo
	modelpar.Holder.HolderType = model.Holder.HolderType
	modelpar.Holder.Mobile = model.Holder.Mobile
	modelpar.Holder.Telphone = model.Holder.Telphone
	modelpar.Holder.Mail = model.Holder.Mail
	modelpar.Holder.BornDate = model.Holder.BornDate
	modelpar.Holder.Sex = model.Holder.Sex

	//被保人
	for _, x := range model.Insureds {
		item := is.Insureds{}
		item.InsuredName = x.InsuredName
		item.IdcartType = x.IdcartType
		item.IdcartNo = x.IdcartNo
		//item.InsuredType = x.
		item.BornDate = x.BornDate
		item.Sex = x.Sex
		item.Mobile = x.Mobile
		item.RelationWithHolder = x.RelationWithHolder
		modelpar.Insureds = append(modelpar.Insureds, &item)
	}
	modelpar.Target = new(is.Target)
	//宠物信息
	modelpar.Target.DogLicenseCode = model.Target.DogLicenseCode
	modelpar.Target.ImmunityCertifiCode = model.Target.ImmunityCertifiCode
	modelpar.Target.PetDogBreed = model.Target.PetDogBreed
	modelpar.Target.HouseCity = model.Target.HouseCity
	modelpar.Target.HouseAddress = model.Target.HouseAddress
	modelpar.Target.PetName = model.Target.PetName
	modelpar.Target.Category = model.Target.Category
	modelpar.Target.CategoryName = model.Target.CategoryName
	modelpar.Target.Birthday = model.Target.Birthday
	modelpar.Target.Gender = model.Target.Gender
	modelpar.Target.Sterilization = model.Target.Sterilization
	modelpar.Target.Immune = model.Target.Immune
	modelpar.Target.Base64Str = model.Target.Base64Str

	grpcRes, err := client.InsuranceOrderOnLine(context.Background(), modelpar)
	if err != nil || grpcRes.Code != 200 {
		return ctx.JSON(400, grpcRes)
	}

	out.ErrCode = 200
	return ctx.JSON(200, out)
}
