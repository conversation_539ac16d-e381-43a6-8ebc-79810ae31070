package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"io/ioutil"
)

func FnCallback(c echo.Context) error {
	model1 := dto.FnCallBackMode{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	err := json.Unmarshal(body, &model1)
	if err != nil {
		return c.JSON(500, "绑定参数出错")
	}
	glog.Info("FnCallback-蜂鸟配送回调参数：", model1)

	BusinessData := dto.BusinessDataMode{}
	json.Unmarshal([]byte(cast.ToString(model1.BusinessData)), &BusinessData)

	if BusinessData.CallbackBusinessType != "orderStatusNotify" {
		return c.JSON(200, "ok")
	}

	model := BusinessData.Param
	//json.Unmarshal([]byte(cast.ToString(BusinessData.Param)), &model)

	//等于0是还在创建订单，直接返回成功
	if model.OrderStatus == 0 {
		return c.JSON(200, "ok")
	}

	var params oc.DeliveryNodeRequest
	//3达达
	params.DeliveryType = 4
	params.OrderSn = model.PartnerOrderCode
	params.CourierName = model.CarrierDriverName
	params.CourierPhone = model.CarrierDriverPhone
	params.CancelReason = model.Description
	params.CreateTime = kit.GetTimeNow()

	//第三方的配送订单号，因为复用美配的处理逻辑，
	//美配 0 派单中 15骑手已到店 20骑手已接单 30骑手已取货 50已送达 99骑手已取消
	//闪送状态 20：派单中 30：取货中 40：闪送中 50：已完成 60：已取消
	params.MtPeisongId = cast.ToString(model.OrderID) //配送id的适配
	//将闪送的状态对应到美团的状态中
	switch model.OrderStatus {
	case 1:
		params.Status = 0
		break
	case 80:
		params.Status = 15
		break
	case 20:
		params.Status = 20
		break
	case 2:
		params.Status = 30
		break
	case 3:
		params.Status = 50
		break
	case 4:
		params.Status = 99
		break
	case 5: //异常在我们这也属于订单取消
		params.Status = 99
		break
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.DeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("FnCallback 调DeliveryNode,蜂鸟订单："+model.PartnerOrderCode, r)
	if err != nil || r.Code != 200 {
		return c.JSON(500, err.Error())
	}

	return c.JSON(200, params)
}

//授权回调
func FnCallbackAuth(c echo.Context) error {
	model := et.TokenRequst{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	err := json.Unmarshal(body, &model)
	if err != nil {
		return c.JSON(500, "绑定参数出错")
	}
	glog.Info("FnCallback-蜂鸟授权回调参数：", model)

	//拿到了code，通过code去获取token
	etClient := et.GetExternalClient()
	defer etClient.Close()

	_, err = etClient.Fn.GetToken(context.Background(), &model)
	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, "ok")
}
