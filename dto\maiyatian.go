package dto

// MytBaseResponse 麦芽田基础响应
type MytBaseResponse struct {
	Code    int32  `json:"code"`    // 状态码 200成功
	Message string `json:"message"` // 错误信息
}

type MytDeliveryStatus struct {
	OrderID     string `json:"order_id"`
	ShopID      string `json:"shop_id"`
	Status      string `json:"status"`
	RiderName   string `json:"rider_name"`
	RiderPhone  string `json:"rider_phone"`
	LogisticTag string `json:"logistic_tag"`
	Longitude   string `json:"longitude"`
	Latitude    string `json:"latitude"`
	LogisticNo  string `json:"logistic_no"`
	IsTransship bool   `json:"is_transship"`
	UpdateTime  int64  `json:"update_time"`
}

type MytData struct {
	Token     string `json:"token"`
	Timestamp int64  `json:"timestamp"`
	Data      string `json:"data"`
	Command   string `json:"command"`
	RequestId string `json:"request_id"`
	Signature string `json:"signature"`
}

type MytDataOrderList struct {
	Token     string        `json:"token"`
	Timestamp int64         `json:"timestamp"`
	Data      DataOrderList `json:"data"`
	Command   string        `json:"command"`
	RequestId string        `json:"request_id"`
	Signature string        `json:"signature"`
}

type DataOrderList struct {
	ShopID    string `json:"shop_id"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type MytDataOrderDetail struct {
	Token     string          `json:"token"`
	Timestamp int64           `json:"timestamp"`
	Data      DataOrderDetail `json:"data"`
	Command   string          `json:"command"`
	RequestId string          `json:"request_id"`
	Signature string          `json:"signature"`
}

type DataOrderDetail struct {
	ShopID  string `json:"shop_id"`
	OrderId string `json:"order_id"`
}
