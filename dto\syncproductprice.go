package dto

type SyncProductPrice struct {
	//财务编码
	StructCode []string `json:"struct_code"`
	//商品信息
	ProductInfo []ProductInfo `json:"product_info"`
}

type ProductInfo struct {
	//第三方货号
	ProductCode string `json:"product_code"`
	//以分为单位
	SellPrice string `json:"sell_price"`
}

type SyncProductPriceResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
}
