package controller

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"external-ui/dto"
	"external-ui/pkg/code"
	"external-ui/proto/dac"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"external-ui/proto/pc"
	"external-ui/utils"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"google.golang.org/grpc/metadata"
)

const (
	OrderAppChannelRedisKey = "order-center:order:app-channel:"
)
const (
	AppChannelUndefined = iota
	AppChannelRP        // 瑞鹏自有
	AppChannelTP        // 代运营
)

var (
	// ElmSource 瑞鹏自有 ElmSource 饿了么瑞鹏自有Source
	ElmSource = config.GetString("ElmSource")
	// TPElmSource TP代运营 TPElmSource 饿了么TP代运营Source
	TPElmSource = config.GetString("tp-elm-app-id")
)

// 订单是否已存在
func oldOrderSnIsExist(oldOrderSn string) bool {
	client := oc.GetOrderServiceClient()

	res, err := client.RPC.OrderIsExist(client.Ctx, &oc.OrderIsExistRequest{OrderId: oldOrderSn})
	if err != nil {
		glog.Error("oldOrderSnIsExist fail:", err, ";old_order_sn:", oldOrderSn)
	}
	return res.IsExist
}

func pushOrderInitialData(oldOrderSn string, channelId int32, bodyStr string) bool {
	client := oc.GetOrderServiceClient()

	_, err := client.RPC.SaveOrderOriginData(client.Ctx, &oc.SaveOrderOriginDataRequest{OldOrderSn: oldOrderSn, ChannelId: channelId, BodyJson: bodyStr})
	if err != nil {
		return false
	}
	return true
}

// 获取订单详情
func geteJddjOrderDetailModel(orderId string, storeMasterId int32) dto.JddjData {
	etClient := et.GetExternalClient()
	defer etClient.Close()
	var jddjOrderDetailModel dto.JddjData
	orderDetaiModel := new(et.JddjOrderDetailRequest)
	orderDetaiModel.OrderId = orderId
	orderDetaiModel.StoreMasterId = storeMasterId

	logFixStr := fmt.Sprintf("京东获取订单详情接口(geteJddjOrderDetailModel)========%s", kit.JsonEncode(orderDetaiModel))
	glog.Info(logFixStr, orderId, ",", storeMasterId, ", 推送京东到家订单到订单中心，查询详情参数, ")
	grpcResEt, err := etClient.JddjOrder.GetJddjOrderDetail(etClient.Ctx, orderDetaiModel)
	if err != nil {
		glog.Error(logFixStr, orderId, ", 推送京东到家订单到订单中心，查询京东到家详情参数结果1, ", err)
		return jddjOrderDetailModel
	}
	if grpcResEt.Code != 200 {
		glog.Error(logFixStr, orderId, ", 推送京东到家订单到订单中心，查询京东到家详情参数结果2, ", grpcResEt.Error)
		return jddjOrderDetailModel
	}
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.JddjData), &jddjOrderDetailModel)
	if err != nil {
		glog.Error(logFixStr, orderId, ", 推送京东到家订单到订单中心，查询京东到家详情参数结果3, ", err)
	}
	return jddjOrderDetailModel
}

// 利用查询订单详情接口推送订单到订单中心
func JddjOrderPush(billId string, storeMasterId int32, isAdjust int32) string {
	jddjOrderDetailModel := geteJddjOrderDetailModel(billId, storeMasterId)

	if jddjOrderDetailModel.Code != "0" {
		return "fail"
	}

	paramJson := kit.JsonEncode(jddjOrderDetailModel)
	glog.Info("推送京东到家订单收到参数  ", paramJson)

	detailModel := dto.JddjResultList{}
	if len(jddjOrderDetailModel.Result.ResultList) > 0 {
		detailModel = jddjOrderDetailModel.Result.ResultList[0]
	}
	if cast.ToString(jddjOrderDetailModel.Code) == "0" {
		model := new(oc.MtAddOrderRequest)
		orderId := fmt.Sprintf("%d", detailModel.OrderID)
		model.OrderSn = orderId

		pushOrderInitialData(orderId, 4, paramJson)

		etClient := et.GetExternalClient()
		defer etClient.Close()

		//查询平台服务费
		orderDetaiModel := new(et.JddjOrderSerllerDeliveryRequest)
		orderDetaiModel.OrderId = orderId
		orderDetaiModel.StoreMasterId = storeMasterId
		glog.Info(orderId, ", 推送京东到家订单到订单中心，查询平台服务费参数")

		grpcResEt, err := etClient.JddjOrder.JddjOrderShoudSettlementService(etClient.Ctx, orderDetaiModel)

		if err != nil {
			glog.Error(orderId, ", 推送京东到家订单到订单中心，查询平台服务费结果 ", err)
			return "fail"
		}
		if grpcResEt.Code != 200 {
			glog.Error(orderId, ", 推送京东到家订单到订单中心，查询平台服务费结果 ", grpcResEt.Error)
			return "fail"
		}
		if grpcResEt.OrderShoudSettlement != nil {
			var jddjSettlementResultModel dto.SettlementResult
			err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.OrderShoudSettlement), &jddjSettlementResultModel)
			if err != nil {
				glog.Error(orderId, ", 推送京东到家订单到订单中心，查询平台服务费参数结果, ", err)
				return "fail"
			}
			//平台服务费
			model.ServiceCharge = int32(jddjSettlementResultModel.PckageCommission + jddjSettlementResultModel.GoodsCommission + jddjSettlementResultModel.FreightCommission) //jddjSettlementResultModel.GuaranteedCommission
		}

		model.OrderStatus = 20
		model.OrderStatusChild = 20101
		model.ShopId = detailModel.DeliveryStationNo
		model.ShopName = detailModel.DeliveryStationName
		model.ReceiverName = detailModel.BuyerFullName
		if detailModel.BuyerFullAddress != "订单完结5小时后，隐藏顾客地址。" {
			recipient_address1 := strings.Split(detailModel.BuyerFullAddress, "@#")[0]
			r, err := regexp.Compile(".+?(省|市|自治区|自治州|县|区)")
			if err != nil {
				glog.Error(orderId, ", 推送京东到家已支付订单从地址转换省市县失败, ", err, ", ", detailModel.BuyerFullAddress)
				return "fail"
			}
			array := r.FindAllString(recipient_address1, -1)
			for _, v := range array {
				if strings.Contains(v, "省") ||
					strings.Contains(v, "自治区") {
					model.ReceiverState = v
				}
				if strings.Contains(v, "市") ||
					strings.Contains(v, "自治州") {
					model.ReceiverCity = v
				}
				if strings.Contains(v, "区") ||
					strings.Contains(v, "街道") ||
					strings.Contains(v, "县") {
					model.ReceiverDistrict = v
				}
			}
		}

		model.ReceiverCity = detailModel.BuyerCityName
		model.ReceiverDistrict = detailModel.BuyerCountryName
		model.ReceiverAddress = detailModel.BuyerFullAddress
		model.ReceiverPhone = detailModel.BuyerMobile
		if len(model.ReceiverPhone) <= 0 {
			model.ReceiverPhone = detailModel.BuyerTelephone
		}
		intPayType := detailModel.OrderPayType
		//支付类型：1-货到付款，4-在线支付。
		if intPayType == 4 {
			model.PayType = "Cod"
		} else {
			model.PayType = "NoCod"
		}
		model.ReceiverMobile = detailModel.BuyerMobile
		model.GjpStatus = "Payed"
		model.Total = detailModel.OrderBuyerPayableMoney   //用户实付金额，单位：分
		model.PackingCost = detailModel.PackagingMoney     //包装费，单位：分
		model.Freight = detailModel.OrderReceivableFreight //配送费 ，单位：分
		model.IsPay = 1
		model.CreateTime = detailModel.OrderStartTime
		model.Longitude = detailModel.BuyerLng //经度
		model.Latitude = detailModel.BuyerLat  //纬度
		//承运商编号(9966:京东众包;2938:商家自送;1130:达达同城送;9999:到店自提)
		model.LogisticsCode = detailModel.DeliveryCarrierNo
		model.ExpectedTime = detailModel.OrderPreEndDeliveryTime //预计送达时间
		businessTags := strings.Split(detailModel.BusinessTag, ";")

		//订单类型1普通订单(默认),2预订订单,3门店自提,4拼团订单,5门店配送
		if utils.IsValueInListStr("dj_prepare_sale_order", businessTags) || utils.IsValueInListStr("one_dingshida", businessTags) || utils.IsValueInListStr("dj_aging_nextday", businessTags) || detailModel.DeliveryCarrierNo == "9999" {
			model.OrderType = 2
		} else {
			model.OrderType = 1
		}

		invoiceData := make(map[string]string)
		invoiceData["发票抬头"] = detailModel.OrderInvoice.InvoiceTitle
		invoiceData["纳税人识别号"] = detailModel.OrderInvoice.InvoiceDutyNo
		model.Invoice = kit.JsonEncode(invoiceData)
		model.PayTime = kit.GetTimeNow()
		model.BuyerMemo = detailModel.OrderBuyerRemark
		//1快递 2外卖 3自提
		if detailModel.DeliveryCarrierNo == "9999" {
			model.DeliveryType = 3
		} else {
			model.DeliveryType = 2
		}

		//是否是物竞天择来源
		if detailModel.SrcOrderType == 20 {
			model.OrderChannelSrcType = &oc.OrderChannelSrcTypeModel{
				SrcType:     "420",
				SrcTypeName: "物竞天择",
			}
		}

		model.IsAdjust = isAdjust

		//优惠信息
		if len(detailModel.Discount) > 0 {
			for _, item := range detailModel.Discount {
				//优惠信息新表
				orderPromotionItem := oc.OrderPromotionModel{
					PromotionType: 100000,
					PoiCharge:     item.VenderPayMoney,
					PtCharge:      item.PlatPayMoney,
					PromotionFee:  item.DiscountPrice,
				}

				switch item.DiscountType {
				case 8:
					orderPromotionItem.PromotionType = 508
					orderPromotionItem.PromotionTitle = "商家满免运费"

					if item.DiscountDetailType == 1 {
						orderPromotionItem.PoiCharge = item.DiscountPrice
					} else if item.DiscountDetailType == 0 {
						orderPromotionItem.PtCharge = item.DiscountPrice
					} else {
						orderPromotionItem.PoiCharge = item.DiscountPrice
					}
				case 7:
					orderPromotionItem.PromotionType = 507
					orderPromotionItem.PromotionTitle = "VIP免运费"
					//orderPromotionItem.PtCharge = item.DiscountPrice
				case 12:
					orderPromotionItem.PromotionType = 512
					orderPromotionItem.PromotionTitle = "首单地推满免运费"
					orderPromotionItem.PtCharge = item.DiscountPrice
				case 15:
					orderPromotionItem.PromotionType = 515
					orderPromotionItem.PromotionTitle = "运费券"
				case 16:
					orderPromotionItem.PromotionType = 516
					orderPromotionItem.PromotionTitle = "单品免运"
				case 18:
					orderPromotionItem.PromotionType = 528
					orderPromotionItem.PromotionTitle = "满减基础运费"
					orderPromotionItem.PtCharge = item.DiscountPrice
				}

				if orderPromotionItem.PromotionType == 100000 {
					continue
				}

				model.Privilege += item.DiscountPrice
				model.OrderPromotion = append(model.OrderPromotion, &orderPromotionItem)
			}
		}

		//用户积分抵扣金额>0插入优惠信息表 积分抵扣放入平台优惠
		if detailModel.PlatformPointsDeductionMoney > 0 {
			model.Privilege += detailModel.PlatformPointsDeductionMoney

			orderPromotionItem := &oc.OrderPromotionModel{
				PromotionType:  517,
				PromotionTitle: "用户积分抵扣金额",
				PtCharge:       detailModel.PlatformPointsDeductionMoney,
				PromotionFee:   detailModel.PlatformPointsDeductionMoney,
			}
			model.OrderPromotion = append(model.OrderPromotion, orderPromotionItem)
		}

		reqOrder := et.JddjQueryOassBussMoneyRequest{
			OrderId:       detailModel.OrderID,
			StoreMasterId: storeMasterId,
		}
		glog.Info(orderId, ", 推送京东到家订单到订单中心，查询订单拆分优惠参数")

		//订单金额拆分 均摊明细查询
		BussMoneyResEt, err := etClient.JddjBuss.JddjQueryOassBussMoney(etClient.Ctx, &reqOrder)
		if err != nil {
			glog.Error(orderId, ", 推送京东到家订单到订单中心，查询订单拆分优惠结果, ", err)
			return "fail"
		}
		if BussMoneyResEt.Code != 200 {
			glog.Error(orderId, ", 推送京东到家订单到订单中心，查询订单拆分优惠结果, ", BussMoneyResEt.Error)
			return "fail"
		}
		glog.Info(orderId, ", 推送京东到家订单到订单中心，查询订单拆分优惠参数, 返回结果, "+kit.JsonEncode(BussMoneyResEt))
		model.TotalWeight = int32(detailModel.DeliveryPackageWeight * 1000)

		//订单商品
		var skus []int32
		SkuPayTotalMap := make(map[string]int32)
		BussSkuPayTotalMap := make(map[int64]int32)
		goodsTotal := int32(0)
		if len(detailModel.Product) > 0 {
			for _, item := range detailModel.Product {
				//计算sku总金额（商家商品编码）
				SkuPayTotalMap[item.SkuIDIsv] += item.SkuCount * item.SkuStorePrice
			}
			for _, bussMoney := range BussMoneyResEt.Data {
				var ptCost int64 //统计平台承担的优惠
				for _, dis := range bussMoney.Discountlist {
					ptCost += dis.CostMoney
				}
				//计算sku总金额
				BussSkuPayTotalMap[bussMoney.SkuId] += int32(bussMoney.SkuPayMoney + ptCost) //平台承担的优惠也要算的sku金额
			}
			for index, item := range detailModel.Product {
				goodsTotal += item.SkuStorePrice * item.SkuCount
				var product = oc.OrderProductModel{
					Sku:           item.SkuIDIsv,
					ProductId:     item.UpcCode,
					SubBizOrderId: cast.ToString(item.SkuID),
					ProductName:   item.SkuName,
					Specs:         item.SkuCostumeProperty,
					PromotionType: item.PromotionType,
					SkuPayTotal:   SkuPayTotalMap[item.SkuIDIsv], //sku实付金额
					ProductType:   1,
				}

				//京东的优惠金额分为商品优惠金额和订单优惠金额 ，需要分开计算。
				for _, bussMoney := range BussMoneyResEt.Data {
					if item.SkuID == bussMoney.SkuId && item.PromotionType == bussMoney.PromotionType {
						product.Price = bussMoney.PromotionPrice
						product.MarkingPrice = bussMoney.PdjPrice
						product.Number += bussMoney.SkuCount
						product.PaymentTotal += int32(bussMoney.SkuPayMoney)

						//订单优惠金额
						if len(detailModel.Discount) > 0 {
							for _, discount := range bussMoney.Discountlist {
								product.Privilege += int32(discount.SaleMoney)
								product.PrivilegePt += int32(discount.CostMoney)
								product.PrivilegeTotal += int32(discount.SkuDiscountMoney)

								activeType := int32(100000)
								promotionTitle := ""
								//优惠类型(1:优惠码;3:优惠劵;4:满减;5:满折;6:首单优惠;7:VIP免运费;8:商家满免运费;10:满件减;11:满件折;12:首单地推满免运费;15:运费券;16:单品免运)
								if discount.PromotionType == 1 {
									activeType = 501
									promotionTitle = "优惠码"
								} else if discount.PromotionType == 3 {
									activeType = 503
									promotionTitle = "优惠劵"
								} else if discount.PromotionType == 4 {
									activeType = 504
									promotionTitle = "满减"
								} else if discount.PromotionType == 5 {
									activeType = 505
									promotionTitle = "满折"
								} else if discount.PromotionType == 6 {
									activeType = 506
									promotionTitle = "首单优惠"
								} else if discount.PromotionType == 10 {
									activeType = 510
									promotionTitle = "满件减"
								} else if discount.PromotionType == 11 {
									activeType = 511
									promotionTitle = "满件折"
								}
								isExist := false

								for _, promotion := range model.OrderPromotion {
									if activeType == promotion.PromotionType {
										isExist = true
										promotion.PtCharge += cast.ToInt32(discount.CostMoney)
										promotion.PoiCharge += cast.ToInt32(discount.SaleMoney)
										promotion.PromotionFee = promotion.PromotionFee + cast.ToInt32(discount.SkuDiscountMoney)
									}
								}

								if !isExist {
									model.OrderPromotion = append(model.OrderPromotion, &oc.OrderPromotionModel{
										PromotionId:    0,
										PromotionType:  activeType,
										PromotionTitle: promotionTitle,
										PtCharge:       int32(discount.CostMoney),
										PoiCharge:      int32(discount.SaleMoney),
										PromotionFee:   int32(discount.CostMoney + discount.SaleMoney),
									})
								}
							}
						}
						//商品优惠金额
						if bussMoney.PromotionType > 1 {
							privilegeValue := cast.ToInt32(bussMoney.SaleMoney) * bussMoney.SkuCount
							privilegePtValue := cast.ToInt32(bussMoney.CostMoney) * bussMoney.SkuCount
							product.Privilege += privilegeValue
							product.PrivilegePt += privilegePtValue
							product.PrivilegeTotal += privilegeValue + privilegePtValue

							//v6.3.3 新增
							//之前商品优惠的平台优惠并没有加到对应sku上 导致推送第三方时金额对不上
							BussSkuPayTotalMap[bussMoney.SkuId] += privilegePtValue

							activeType := int32(100000)
							promotionTitle := ""
							//商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;
							//9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,
							//8001:商家会员价,8:第二件N折,9:拼团促销)
							if bussMoney.PromotionType == 3 {
								activeType = 409
								promotionTitle = "商品直降"
							} else if bussMoney.PromotionType == 4 {
								activeType = 520
								promotionTitle = "限时抢购"
							} else if bussMoney.PromotionType == 1202 {
								activeType = 40
								promotionTitle = "加价购"
							} else if bussMoney.PromotionType == 8 {
								activeType = 31
								promotionTitle = "满N件折"
							} else if bussMoney.PromotionType == 9 {
								activeType = 518
								promotionTitle = "拼团促销"
							} else {
								activeType = 520
								promotionTitle = "捆绑促销"
							}

							isExist := false

							for _, promotion := range model.OrderPromotion {
								if activeType == promotion.PromotionType {
									isExist = true
									promotion.PtCharge += privilegePtValue
									promotion.PoiCharge += privilegeValue
									promotion.PromotionFee = promotion.PromotionFee + privilegePtValue + privilegeValue
								}
							}

							if !isExist {
								orderPromotionItem := oc.OrderPromotionModel{}
								orderPromotionItem.PromotionId = 0
								orderPromotionItem.PromotionType = activeType
								orderPromotionItem.PromotionTitle = promotionTitle

								orderPromotionItem.PtCharge = privilegePtValue
								orderPromotionItem.PoiCharge = privilegeValue
								orderPromotionItem.PromotionFee = orderPromotionItem.PtCharge + orderPromotionItem.PoiCharge
								model.OrderPromotion = append(model.OrderPromotion, &orderPromotionItem)
							}
						}
					}
				}

				//product.SkuPayTotal = product.PaymentTotal
				product.SkuPayTotal = BussSkuPayTotalMap[item.SkuID]
				//如果是第一个商品并且积分抵扣大于0就把积分抵扣的钱加在第一个商品上
				if index == 0 && detailModel.PlatformPointsDeductionMoney > 0 {
					product.SkuPayTotal = BussSkuPayTotalMap[item.SkuID] + detailModel.PlatformPointsDeductionMoney
					product.PrivilegePt += detailModel.PlatformPointsDeductionMoney
					product.PrivilegeTotal += detailModel.PlatformPointsDeductionMoney
				}
				//product.PayPrice = product.PaymentTotal / product.Number
				//京东到家测试数据问题，
				if product.Number == 0 {
					product.PayPrice = 0
				} else {
					product.PayPrice = product.PaymentTotal / product.Number
				}
				model.OrderProductModel = append(model.OrderProductModel, &product)

				skus = append(skus, cast.ToInt32(item.SkuIDIsv))
				model.Privilege += product.PrivilegeTotal
			}
		}
		//model.GoodsTotal = detailModel.OrderTotalMoney //商品总金额（未加运费，不加包装费）
		model.GoodsTotal = goodsTotal //门店价格来做订单商品总额计算。

		productIdMap := make(map[int32]int32)
		if len(skus) > 0 {
			client := pc.GetDcChannelProductClient()
			defer client.Close()

			outProduct, err := client.RPC.GetProductIdBySkuId(client.Ctx, &pc.GetProductIdBySkuIdRequest{
				SkuId: skus,
			})
			if err != nil {
				glog.Error(orderId, ", 查询GetProductIdBySkuId失败, ", err)
				return "fail"
			}
			productIdMap = outProduct.Data
		}

		if len(model.OrderProductModel) > 0 && len(productIdMap) > 0 {
			for _, item := range model.OrderProductModel {
				if val, ok := productIdMap[cast.ToInt32(item.Sku)]; ok {
					item.ProductId = strconv.Itoa(int(val))
				}
			}
		}

		//支付信息
		payInfo := oc.OrderPayInfoModel{
			OrderSn:   model.OrderSn,
			PayAmount: model.Total,
			PayTime:   model.PayTime,
			PayMode:   6, //1支付宝 2微信 3美团 4其他 5饿了么 6京东支付
		}
		//订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：
		//京东支付（无法判断具体京东支付子类型）；9002：京东银行卡支付；9004：京东白条支付；9012：京东余额支付；9022：京东小金库支付
		if detailModel.PayChannel == 8001 || detailModel.PayChannel == 8002 || detailModel.PayChannel == 8003 {
			payInfo.PayMode = 2
		}
		model.PayInfo = append(model.PayInfo, &payInfo)

		glog.Info("推送京东到家订单到订单中心，推送参数 " + kit.JsonEncode(model))

		//加入context渠道信息
		grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 4, UserAgent: 6}}
		ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

		ocClient := oc.GetOrderServiceClient()
		var grpcRes *oc.MtAddOrderResponse
		//todo tp jd
		grpcRes, err = ocClient.Cart.MtSubmitOrder(ctx, model)
		if err != nil {
			glog.Error(orderId, ", 推送京东到家订单到订单中心调用接口, ", err)
			return "fail"
		}
		if grpcRes.Code != 200 {
			glog.Error(orderId, ", 推送京东到家订单到订单中心调用接口返回:, ", grpcRes.Error, grpcRes.Message) // 记录日志
			return "fail"
		}

		glog.Info(orderId, ", 推送京东到家订单到订单中心结果成功, ", grpcRes) // 记录日志
		return "ok"
	} else {
		glog.Error(detailModel.OrderID, ", 推送京东到家订单到订单中心结果失败")
		return "fail"
	}
}

var (
	storeExternalRedisKey = "datacenter:store:external"
	orderSnRedisKey       = "datacenter:store:ordersn:"
)

// 根据渠道订单号(ordersn) 获取门店的storeMasterId(appChannel)
func GetAppChannelByOrderSn(ordersn string) (int32, error) {
	if len(ordersn) <= 0 {
		return 0, errors.New("ordersn 不合法")
	}
	redisConn := utils.GetRedisConn()
	key := OrderAppChannelRedisKey + ordersn
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去orderCenter查询db获取
	if len(appChannel) <= 0 {
		clientOrderCenter := oc.GetOrderServiceClient()
		defer clientOrderCenter.Close()
		request := &oc.QueryAppChannelByOrderSnReq{OrderSn: ordersn}
		outQuery, err := clientOrderCenter.RPC.QueryAppChannelByOrderSn(clientOrderCenter.Ctx, request)
		if err != nil {
			glog.Error("QueryAppChannelByOrderSn rpc err", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}
		if outQuery.Code != 200 {
			glog.Error("QueryAppChannelByOrderSn rpc failed", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}

		return outQuery.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

func getOrderAppChannel(orderSn string) int32 {
	dacClient := oc.GetOrderServiceClient()
	grpcRes, err := dacClient.RPC.GetOrderAppChannel(context.Background(), &oc.GetOrderAppChannelReq{OrderSn: orderSn})
	if err != nil {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", err)
		return 0
	}
	if grpcRes.Code != 200 {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", kit.JsonEncode(grpcRes))
		return 0
	}
	return grpcRes.Data.AppChannel
}

func setRedisOne(channelStoreId string) (storeMasterId int32, retCode int) {
	data := getStoreChannelExternalData(channelStoreId)
	if len(data) > 0 {
		redisConn := utils.GetRedisConn()
		isOk := redisConn.HSet("datacenter:store:external", data[0].ChannelStoreId, kit.JsonEncode(data[0])).Val()
		if !isOk {
			glog.Error("set redis,datacenter:store:external:err", kit.JsonEncode(data))
			retCode = code.ErrorCommon
			return
		}
		return data[0].AppChannel, code.Success
	}
	retCode = code.ErrorCommon
	return
}

func setRedisData() {
	data := getStoreChannelExternalData("")
	if len(data) > 0 {
		redisValue := make(map[string]interface{}, 0)
		for _, v := range data {
			if len(v.ChannelStoreId) > 0 {
				redisValue[v.ChannelStoreId] = kit.JsonEncode(v)
			}
		}
		redisConn := utils.GetRedisConn()
		val := redisConn.HMSet("datacenter:store:external", redisValue)
		if val.Val() != "Ok" {
			glog.Error(val)
		}
	}
}

func getStoreChannelExternalData(channelStoreId string) []*dac.StoreChannelExternalData {
	data := make([]*dac.StoreChannelExternalData, 0)
	dacClient := dac.GetDataCenterClient()
	grpcRes, err := dacClient.RPC.GetStoreChannelExternal(context.Background(), &dac.GetStoreChannelExternalReq{ChannelId: 2, ChannelStoreId: channelStoreId})
	if err != nil {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", err)
		return data
	}
	if grpcRes.Code != 200 {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", kit.JsonEncode(grpcRes))
		return data
	}
	return grpcRes.Data
}

// GetAppChannelBySource
// 根据饿了么回调数据中‘source’ 来判断appChannel
func GetAppChannelBySource(source string) (int32, error) {
	switch source {
	case ElmSource:
		return AppChannelRP, nil
	case TPElmSource:
		return AppChannelTP, nil
	default:
		glog.Info("GetAppChannelBySource error", "，请求参数：", source, ",调用方法：", kit.RunFuncName(2))
		return AppChannelUndefined, errors.New("非法的source")
	}
}

// MiniProgramCallbackBBC 小程序回调通知电商
func MiniProgramCallbackBBC(dataJson string) {
	url := config.GetString("mall_api") + "/mobile/index.php?act=openapi&op=miniProgramMessagePush"

	stringA := kit.GetMd5("data=" + dataJson)
	signsignValue := strings.ToUpper(kit.GetMd5(stringA + config.GetString("express_key")))

	dsparam := make(map[string]interface{})
	dsparam["data"] = dataJson
	dsparam["sign"] = signsignValue

	dataByte, _ := json.Marshal(dsparam)
	req, err := http.NewRequest("POST", url, bytes.NewReader(dataByte))
	if err != nil {
		glog.Error("MiniProgramCallbackBBC 回调电商失败：", err)
		return
	}
	req.Header.Add("Content-Type", "application-json")
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := client.Do(req)
	if err != nil {
		glog.Error("MiniProgramCallbackBBC 回调电商失败：", err)
		return
	}
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)

	glog.Info("MiniProgramCallbackBBC 微信视频号审核回调电商结果：", string(body))
	return
}
