package controller

import (
	"context"
	"external-ui/proto/ap"
	"external-ui/proto/oc"
	"external-ui/utils"
	"fmt"
	"time"

	"github.com/spf13/cast"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

var resultJson = `{"result": "success"}`

// @Summary 申请售后单
// @Tags 售后单
// @Accept json
// @Produce json
// @Param apply body RefundOrderPayData true " "
// @Success 200 {object} string
// @Failure 400 {object} string
// @Router /external/refund-order/pay [post]
func RefundOrderPay(c echo.Context) error {
	out := new(oc.BaseResponse)
	model := new(RefundOrderPayData)

	if err := c.Bind(model); err != nil {
		return c.JSON(400, err)
	}

	if model.RspCode != "1" {
		return c.JSON(400, "")
	}

	m := map[string]string{}
	if model.BackParam == "isPin" {
		apClient := ap.GetPinGroupServiceClient()
		defer apClient.Close()
		glog.Info("拼团订单退款回调接口参数:"+model.RefundId, kit.JsonEncode(model))
		params := new(ap.RefundNotifyRequest)
		params.PinOrderSn = model.ExtendInfo
		params.RefundAmount = cast.ToInt32(model.RefundAmt)
		params.TransactionNo = model.TransactionNo
		params.ResCode = model.RspCode
		params.ResMessage = model.RspMessage
		params.RefundId = model.RefundId

		glog.Info(model.RefundId, kit.JsonEncode(params))

		grpcRes, err := apClient.RPC.PinRefundNotify(kit.SetTimeoutCtx(context.Background()), params)

		if err != nil {
			out.Code = 400
			out.Error = err.Error()
			out.Message = grpcRes.Message
			glog.Error("external_ui拼团单退款回调:", err)
			return c.JSON(200, out)
		}

		m["result"] = "success"
		out.Code = grpcRes.Code
		out.Message = grpcRes.Message
		out.Error = grpcRes.Error

	} else {
		glog.Info("申请售后单支付回调接口参数:"+model.RefundId, kit.JsonEncode(model))
		// 防止重复提交处理
		lockSubmit := "lock:callback:refund-order-pay:" + model.RefundId
		redisConn := utils.GetRedisConn()
		lockResp := redisConn.SetNX(lockSubmit, time.Now().Unix(), 3*time.Second).Val()
		if !lockResp {
			out.Code = 400
			out.Message = fmt.Sprintf("退款单(%s)已存在，请勿重复提交", model.RefundId)
			glog.Errorf("RefundOrderPay 重复回调通知:%s", kit.JsonEncode(model))
			return c.JSON(200, out)
		}

		ocClient := oc.GetOrderServiceClient()

		params := new(oc.RefundOrderPayCallBackRequest)
		params.RefundOrderSn = model.ExtendInfo
		params.RspCode = model.RspCode
		params.RspMessage = model.RspMessage

		glog.Info(model.RefundId, kit.JsonEncode(params))

		grpcRes, err := ocClient.ROC.RefundOrderPayCallBack(kit.SetTimeoutCtx(context.Background()), params)
		// 删除重复提交锁
		_ = redisConn.Del(lockSubmit).Val()

		if err != nil {
			out.Code = 400
			out.Error = err.Error()
			if grpcRes != nil {
				out.Message = grpcRes.Message
			}
			glog.Error("external_ui售后单支付中心回调:", err)
			return c.JSON(200, out)
		}
		if grpcRes.Code == 400 {
			out.Error = grpcRes.Error
			out.Message = grpcRes.Message
			glog.Error("external_ui售后单支付中心回调:", grpcRes.Error)
			return c.JSON(200, out)
		}

		m["result"] = "success"
		out.Code = grpcRes.Code
		out.Message = grpcRes.Message
		out.Error = grpcRes.Error
	}
	return c.JSON(200, m)
}

type RefundOrderPayData struct {
	//商户私有域：交易返回时原样返回给商户网站，给商户备用
	BackParam string `json:"backParam,omitempty"`
	//后台回调地址
	CallbackUrl string `json:"callbackUrl,omitempty"`
	//客户端 IP ：如 127.0.0.1
	ClientIP string `json:"clientIP,omitempty"`
	//扩展信息：预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo,omitempty"`
	//退款金额，以分为单位
	RefundAmt string `json:"refundAmt,omitempty"`
	//退款订单号
	RefundId string `json:"refundId,omitempty"`
	//返回状态码 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	RspCode string `json:"rspCode,omitempty"`
	//返回信息
	RspMessage string `json:"rspMessage,omitempty"`
	//交易流水号
	TransactionNo string `json:"transactionNo,omitempty"`
	//签名
	Sign string `json:"sign,omitempty"`
}
