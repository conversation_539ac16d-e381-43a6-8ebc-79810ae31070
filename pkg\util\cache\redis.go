package cache

import (
	"github.com/go-redis/redis"
	//_ "github.com/go-sql-driver/mysql"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
)

var (
	redisHandle *redis.Client
)

func SetupDB() {
	redisHandle = GetRedisConn()
}

func CloseDB() {
	redisHandle.Close()
}

//获取redis集群客户端
func GetRedisConn() *redis.Client {
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		if err == nil {
			return redisHandle
		}
	}

	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	redisHandle = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 10,
		IdleTimeout:  60,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}
