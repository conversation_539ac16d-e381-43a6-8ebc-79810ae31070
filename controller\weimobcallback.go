package controller

import (
	"encoding/json"
	"errors"
	"external-ui/dto"
	"external-ui/oms-proto/order"
	"external-ui/pkg/util/cache"
	"external-ui/proto/et"
	"external-ui/services"
	"github.com/labstack/echo/v4"
	"github.com/labstack/gommon/log"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"io/ioutil"
	"net/http"
	"time"
)

const (
	WeiMengAckOk       = "0"
	WeiMengAckErr      = "1"
	WeiMengAckOkMsg    = "success"
	OrderSourceWeiMeng = 8
)

//微盟回调
//微盟所有回调消息走该接口 通过判断微盟的消息数据 判断属于什么业务回调从而调用相关的业务逻辑代码
//微盟回调可能有重试的可能  所以所有的逻辑需要支持重试
func WeiMengCallback(c echo.Context) error {
	ack := &dto.WeiMengAckResponse{
		Code: &dto.WeiMengAck{
			ErrCode: WeiMengAckErr,
		},
	}
	body := c.Request().Body
	byteBody, err := ioutil.ReadAll(body)
	if err != nil {
		//此处报错 则无解 只能从微盟端获取参数然后重试 下面的失败理论上都可以从我们这端获取数据并重试
		glog.Error("微盟回调请求数据读取失败", err.Error())
		ack.Code.ErrMsg = "数据读取失败：" + err.Error()
		return c.JSON(http.StatusOK, ack)
	}

	strBody := string(byteBody)
	glog.Info("收到微盟回调:", strBody)

	callBackData := new(dto.WeiMengCallBackMsg)
	err = json.Unmarshal(byteBody, &callBackData)
	if err != nil {
		log.Error("微盟回调参数解码出错,", err.Error(), ",", strBody)
		ack.Code.ErrMsg = "数据解码失败：" + err.Error()
		return c.JSON(http.StatusOK, ack)
	}
	//如果是测试直接返回成功
	if callBackData.Id == "test_id" {
		ack.Code.ErrCode = WeiMengAckOk
		ack.Code.ErrMsg = WeiMengAckOkMsg
		return c.JSON(http.StatusOK, ack)
	}
	//通过回调消息 判断是什么业务
	topicEvent := callBackData.Topic + "/" + callBackData.Event
	switch topicEvent {
	case "weimob_shop.order/update": //订单更新
	//	err = OrderUpdate(callBackData)
	case "weimob_shop.order/create": //订单创建
		err = OrderCreat(callBackData, strBody)
	case "weimob_shop.order/statusUpdate": //订单状态更新
		err = OrderStatusUpdate(callBackData, strBody)
	case "weimob_shop.rights/create": //售后单创建
		err = RefundCreate(callBackData, strBody)
	case "weimob_shop.rights/statusUpdate": //售后单状态更新
		err = RefundStatusUpdate(callBackData, strBody)

	case "weimob_shop.goods/update": // 商品修改回调
		err = services.WmProductUpdateCallBack(callBackData, strBody)
	case "weimob_shop.goods/create": // 商品新增回调
		err = services.WmProductCreatCallBack(callBackData, strBody)
	default:
		err = errors.New("暂未实现对该消息的订阅")
	}
	if err != nil {
		ack.Code.ErrMsg = "消息处理失败:" + err.Error()
		return c.JSON(http.StatusOK, ack)
	}

	ack.Code.ErrCode = WeiMengAckOk
	ack.Code.ErrMsg = WeiMengAckOkMsg
	return c.JSON(http.StatusOK, ack)
}

//创建订单 需要支持重试 当请求到oms去失败之后 需要使用相同的参数再跑一遍
//文档地址 https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=1857&id=2909&isold=2
func OrderCreat(callBackData *dto.WeiMengCallBackMsg, strBody string) error {

	//创建订单消息不做任何处理，只记录一下，打印一个日志就返回成功，
	//在订单变成支付状态后，记录数据存入到数据库，因为我们需要记录支付成功后的订单详情数据，然后根据这个详情数据来创建正向订单
	//不然如果以后添加订单失败再去查询的时候，订单详情的信息可能已经发生变化了
	msgBodyData := new(dto.WeiMengOrderSaveMsgBody)
	err := json.Unmarshal([]byte(callBackData.MsgBody), &msgBodyData)
	if err != nil {
		log.Error("微盟回调参数解码出错,", err.Error(), ",", strBody)
		return errors.New("微盟回调参数解码出错")
	}
	log.Info("微盟回调参数创建订单参数：", strBody)
	return nil

}

//订单更新
//文档地址 https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=1857&id=2910&isold=2
/*func OrderUpdate(callBackData *dto.WeiMengCallBackMsg) error {
	ack := &dto.WeiMengAck{
		ErrCode: WeiMengAckErr,
	}
	data := new(dto.WeiMengOrderSaveRequest)
	if err := c.Bind(data); err != nil {
		glog.Error("微盟订单创建数绑定失败", err.Error())
		ack.ErrMsg = "数据解析失败：" + err.Error()
		return c.JSON(http.StatusOK, ack)
	}

	ack.ErrCode = WeiMengAckOk
	ack.ErrMsg = WeiMengAckOkMsg
	return c.JSON(http.StatusOK, ack)
}*/

//订单状态更新 我们只需要接订单取消的状态更新
//文档地址 https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=1857&id=2908&isold=2
func OrderStatusUpdate(callBackData *dto.WeiMengCallBackMsg, strBody string) error {
	//非支付状态记录一下日志直接应答成功
	msgBodyData := new(dto.WeiMengOrderSaveMsgBody)
	err := json.Unmarshal([]byte(callBackData.MsgBody), &msgBodyData)
	if err != nil {
		log.Error("微盟回调msg参数解码出错,", err.Error(), ",", strBody)
		return errors.New("微盟回调参数解码出错")
	}
	log.Error("微盟回调订单状态变更消息:", strBody)
	//接受到订单状态变成已支付，先记录数据到数据库，然后执行添加正向订单操作
	if msgBodyData.OrderStatus == 2 {

		//后面的流程在异步流程里执行 因为只要保存了原始订单数据 既可进行重试 我们就可以直接告诉微盟我们处理成功了
		//获取订单详情
		etClient := et.GetExternalClient()
		defer etClient.Close()

		//首先保存原始数据 保存原始数据之后
		originOrder := new(order.OriginOrderSaveRequest)
		originOrder.OriginOrderSn = cast.ToString(msgBodyData.OrderNo)
		originOrder.OrderTime = time.UnixMilli(msgBodyData.CreateTime).Format(kit.DATETIME_LAYOUT)

		originOrder.OrderJson = strBody
		originOrder.OrderSource = OrderSourceWeiMeng
		omsOrderClient := order.GetOmsOrderRpcClient()

		//组装为oms订单数据 写入表中
		resp, err := omsOrderClient.Order.OriginOrderSave(omsOrderClient.Ctx, originOrder)
		if err != nil {
			glog.Error(msgBodyData.OrderNo, "-保存原始订单出错,", err.Error(), ",", kit.JsonEncode(originOrder))
			return errors.New("保存原始订单出错")
		}
		if resp.Code != 200 {
			glog.Error(msgBodyData.OrderNo, "-保存原始订单失败,", kit.JsonEncode(originOrder))
			return errors.New("保存原始订单失败:" + resp.Message)
		}

		go func(msgBodyData *dto.WeiMengOrderSaveMsgBody) {
			redisConn := cache.GetRedisConn()
			lockCard := "weimob.order.lock." + cast.ToString(msgBodyData.OrderNo)
			lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
			if !lockRes {
				glog.Info(msgBodyData.OrderNo, "-微盟订单创没有拿到锁，正在处理")
				return
			}
			defer redisConn.Del(lockCard)

			detailRequestParam := et.WeiMengOrderDetailRequest{
				OrderDomains: []int32{1},
				OrderNo:      msgBodyData.OrderNo,
			}
			grpcRes, err := etClient.WeimobCloud.GetWeiMengOrderDetail(etClient.Ctx, &detailRequestParam)
			if err != nil {
				glog.Error(msgBodyData.OrderNo, "-微盟订单创建获取订单信息出错:", err.Error())
				//return nil
			}

			if grpcRes == nil || grpcRes.Code == 400 {
				log.Error(msgBodyData.OrderNo, "-微盟订单创建获取订单详细信息失败,无返回数据")
				//return nil
			}
			//读取data数据
			orderData := new(dto.WeiMengOrderDetail1Response)
			if err = json.Unmarshal(grpcRes.Data, orderData); err != nil {
				glog.Error(msgBodyData.OrderNo, "-微盟订单详细信息解码失败：", err.Error())
				//return nil
			}

			//if orderData.Code.Errcode != WeiMengAckOk {
			//	log.Error(msgBodyData.OrderNo, "-微盟订单获取获取详细数据微盟返回失败：", orderData.Code.Errmsg)
			//	return nil
			//}

			//转换相应的数据
			orderInfo := orderData.OrderInfo
			request := new(order.WeiMengAddOrderRequest)
			//用于区分是哪个门店的字段，2B还是2C的订单
			request.Vid = cast.ToString(msgBodyData.Vid)
			request.ChannelOrderSn = cast.ToString(orderInfo.OrderBaseInfo.OrderNo)
			request.TradeOrderSn = orderInfo.OrderBaseInfo.ThirdOrderNo //第三方的订单号没什么用
			//order_source SaleChannelId Income GoodsTotal OrderType payType
			request.OrderStatus = orderInfo.OrderBaseInfo.OrderStatus //在oms里做状态的映射
			request.ShopId = cast.ToString(orderInfo.MerchantInfo.BosId)
			request.ShopName = orderInfo.MerchantInfo.BosName
			request.DeliveryType = orderInfo.OrderBaseInfo.DeliveryType
			request.MemberId = cast.ToString(orderInfo.BuyerInfo.Wid)
			request.MemberName = orderInfo.BuyerInfo.UserNickName
			//实际支付金额
			request.PayTotal = int32(kit.YuanToFen(orderInfo.PayInfo.PayAmount))
			request.Privilege = int32(kit.YuanToFen(orderInfo.PayInfo.TotalDiscountAmount))
			//运费 包装费 在循环商品时处理
			request.IsVirtual = orderInfo.OrderBaseInfo.OrderType
			//单据日期取订单支付时间 ???
			//request.OrderTime = time.UnixMilli(orderInfo.OrderBaseInfo.PayTime).Format(kit.DATETIME_LAYOUT)
			//收件人信息
			//包裹信息  给过来是个数组 暂时只读第一个

			fulfillInfo := orderInfo.OrderFulfill.ReceiverInfo
			request.ReceiverName = fulfillInfo.Name
			request.ReceiverPhone = fulfillInfo.Phone
			request.Latitude = cast.ToFloat32(fulfillInfo.Latitude)
			request.Longitude = cast.ToFloat32(fulfillInfo.Longitude)

			request.ReceiverProvince = fulfillInfo.Province
			request.ReceiverCity = fulfillInfo.City
			request.ReceiverDistrict = fulfillInfo.Area
			request.ReceiverAddress = fulfillInfo.Address

			request.UserSubmitTime = time.UnixMilli(orderInfo.OrderBaseInfo.CreateTime).Format(kit.DATETIME_LAYOUT)
			request.BuyerMemo = orderInfo.BuyerInfo.BuyerRemark
			//总运费，总包装费，运费优惠，包装费优惠
			var sumFreight, sumPackingFee, sumFreightPrivilege int
			var sumGoodsTotal int32
			sumGivePrivilege := int32(0)

			//订单商品
			var orderProduct []*order.OrderProduct
			for _, product := range orderInfo.Items {
				item := new(order.OrderProduct)
				item.ChannelProductId = int64(product.ItemId)
				item.ItemNum = product.GoodsCode
				item.PaymentTotal = int32(kit.YuanToFen(product.PayInfo.PayAmount))
				item.Number = int32(product.SkuNum)
				item.PrivilegeTotal = int32(kit.YuanToFen(product.PayInfo.TotalDiscountAmount))
				//单个SKU商品实付金额等于总支付金额除以数量
				item.PayPrice = int32(kit.YuanToFen(product.PayInfo.PayAmount / product.SkuNum))
				item.IsFree = 0
				for _, price := range product.PriceInfos {
					//原价
					if price.Type == 3 {
						item.MarkingPrice = int32(kit.YuanToFen(price.Amount))
						break
					}
				}
				//如果是赠品的话，实付，优惠，原价，都要改成0
				for _, disItem := range product.ItemBizExt.BizInfos {
					if disItem.SubBizType == 1 && disItem.BizType == 32 {
						item.IsFree = 1
						sumGivePrivilege += item.PrivilegeTotal
						item.PayPrice = 0
						item.PaymentTotal = 0
						item.PrivilegeTotal = 0
						item.MarkingPrice = 0
						break
					}
				}
				//计算商品总支付价格
				sumGoodsTotal += item.PaymentTotal

				item.Total = item.MarkingPrice * item.Number
				orderProduct = append(orderProduct, item)
				//原价
			}
			//商品总支付价格
			request.GoodsTotal = sumGoodsTotal

			//重新计算总优惠金额 =总优惠金额-赠品的优惠金额
			request.Privilege = request.Privilege - sumGivePrivilege
			for _, pay := range orderInfo.PayInfo.AmountInfos {
				if pay.Type == 250 { //运费
					sumFreight += kit.YuanToFen(pay.ShouldPayAmount)
					//总金额减实付等于优惠金额
					sumFreightPrivilege += kit.YuanToFen(pay.Amount - pay.ShouldPayAmount)
				} else if pay.Type == 271 { //包装费
					sumPackingFee += kit.YuanToFen(pay.PayAmount)
				}
			}

			request.OrderProduct = orderProduct

			request.Freight = int32(sumFreight)
			request.PackingFee = int32(sumPackingFee)
			request.FreightPrivilege = int32(sumFreightPrivilege)

			//支付信息 给的数据是数组怎么处理 ？？？
			var OrderPayInfo []*order.OrderPay
			for _, payinfo := range orderInfo.PayInfo.PayItems {
				item := new(order.OrderPay)
				item.PayTime = payinfo.PayTime
				//默认付款发货，和业务商量不要做货到付款
				item.PayType = 1
				item.PayStatus = 2
				//默认人民币支付   微盟没有显示币种的字段
				item.Currency = 1
				item.PaySn = cast.ToString(payinfo.PayId)
				item.PayAmount = int32(kit.YuanToFen(payinfo.PayItemExtInfo.Amount))
				//只开通了微信支付，默认写死微信支付，支付项里面也是数据类型，只能写死一个
				item.PayMode = 2
				OrderPayInfo = append(OrderPayInfo, item)
			}
			request.OrderPay = OrderPayInfo

			var orderPromotion []*order.OrderPromotion
			for _, promotion := range orderInfo.DiscountInfos {
				//优惠不要算满赠的。赠品不计算优惠金额
				if promotion.DiscountType != 32 {
					item := new(order.OrderPromotion)
					item.PromotionFee = int32(kit.YuanToFen(promotion.DiscountAmount))
					item.PromotionCode = promotion.DiscountId
					item.PromotionType = promotion.DiscountType
					item.PromotionTitle = promotion.Name
					//区分商家优惠与平台优惠
					orderPromotion = append(orderPromotion, item)
				}

			}
			request.OrderPromotion = orderPromotion
			log.Info(msgBodyData.OrderNo, "-保存订单请求参数,", kit.JsonEncode(request))

			addRes, err := omsOrderClient.WeiMeng.OrderAdd(omsOrderClient.Ctx, request)
			if err != nil {
				log.Error(msgBodyData.OrderNo, "-保存订单出错,", err.Error(), ",", kit.JsonEncode(request))
				//return errors.New("保存订单出错")
			}
			if addRes.Code != 200 {
				log.Error(msgBodyData.OrderNo, "-保存订单失败,", kit.JsonEncode(request))
				//return errors.New("保存订单失败:" + addRes.Message)
			}
			//return nil
		}(msgBodyData)
	}

	return nil
	//处理订单取消
}

//退款单创建
//文档地址 https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=1858&id=2915&isold=2
func RefundCreate(callBackData *dto.WeiMengCallBackMsg, strBody string) error {
	msgBodyData := new(dto.WeiMengRefundSaveMsgBody)
	err := json.Unmarshal([]byte(callBackData.MsgBody), &msgBodyData)
	if err != nil {
		glog.Error("微盟回调msg参数解码出错,", err.Error(), ",", strBody)
		return errors.New("微盟回调参数解码出错")
	}

	glog.Info(msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-"+" 处理售后完成状态的售后单,", kit.JsonEncode(msgBodyData), " str: ", strBody)

	//售后状态。支持的类型包括：1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；
	//6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中；30-商家拒绝_待平台介入；31-待平台介入_仲裁中。
	if msgBodyData.RightsInfo.RightsStatus != 6 {
		//只有售后完成的售后单才推送到oms
		glog.Info("退款单创建接口：", msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-微盟退款单不是售后完成状态暂时不对接：", kit.JsonEncode(msgBodyData))
		return nil
	}
	request := &order.WeiMOrderRefundAddRequest{
		RightsId: msgBodyData.RightsId,
		OrderNo:  cast.ToInt64(msgBodyData.OrderNo),
		RightsInfo: &order.RightsInfo{
			Vid:             cast.ToInt64(msgBodyData.RightsInfo.Vid),
			ProcessVid:      cast.ToInt64(msgBodyData.RightsInfo.ProcessVid),
			RightsType:      cast.ToInt64(msgBodyData.RightsInfo.RightsType),
			RightsStatus:    cast.ToInt64(msgBodyData.RightsInfo.RightsStatus),
			RightsCauseType: cast.ToInt64(msgBodyData.RightsInfo.RightsCauseType),
			RightsSource:    cast.ToInt64(msgBodyData.RightsInfo.RightsSource),
			RefundType:      cast.ToInt64(msgBodyData.RightsInfo.RefundType),
		},
		PushMsh: strBody,
	}
	omsOrderClient := order.GetOmsOrderRpcClient()
	glog.Info(msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-"+"保存退款单单请求参数,", kit.JsonEncode(request))
	addRes, err := omsOrderClient.WeiMeng.OrderRefundAdd(omsOrderClient.Ctx, request)
	if err != nil {
		glog.Error(msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-保存退款订单出错,", err.Error(), ",", kit.JsonEncode(request))
		return errors.New("保存退款订单出错")
	}
	if addRes.Code != 200 {
		glog.Error(msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-保存退款订单失败,", kit.JsonEncode(request))
		return errors.New("保存退款订单失败:" + addRes.Message)
	}
	return nil
}

//退款单更新 仅更新售后完成状态的售后单
//文档地址 https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=1858&id=2916&isold=2
func RefundStatusUpdate(callBackData *dto.WeiMengCallBackMsg, strBody string) error {

	msgBodyData := new(dto.WeiMengRefundSaveMsgBody)
	err := json.Unmarshal([]byte(callBackData.MsgBody), &msgBodyData)
	if err != nil {
		glog.Error("微盟回调msg参数解码出错,", err.Error(), ",", strBody)
		return errors.New("微盟回调参数解码出错")
	}

	glog.Info("退款单更新接口： ", msgBodyData.OrderNo, "-", msgBodyData.RightsId, " 处理售后完成状态的售后单：", kit.JsonEncode(msgBodyData))
	//售后状态。支持的类型包括：1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；
	//6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中；30-商家拒绝_待平台介入；31-待平台介入_仲裁中。
	if msgBodyData.RightsInfo.RightsStatus != 6 {
		//只有售后完成的售后单才更新到oms
		glog.Info("退款单更新接口： ", msgBodyData.OrderNo, "-", msgBodyData.RightsId, "-微盟退款单不是售后完成状态暂时不对接：", kit.JsonEncode(msgBodyData))
		return nil
	}

	request := &order.WeiMOrderRefundAddRequest{
		RightsId: msgBodyData.RightsId,
		OrderNo:  cast.ToInt64(msgBodyData.OrderNo),
		RightsInfo: &order.RightsInfo{
			Vid:             cast.ToInt64(msgBodyData.RightsInfo.Vid),
			ProcessVid:      cast.ToInt64(msgBodyData.RightsInfo.ProcessVid),
			RightsType:      cast.ToInt64(msgBodyData.RightsInfo.RightsType),
			RightsStatus:    cast.ToInt64(msgBodyData.RightsInfo.RightsStatus),
			RightsCauseType: cast.ToInt64(msgBodyData.RightsInfo.RightsCauseType),
			RightsSource:    cast.ToInt64(msgBodyData.RightsInfo.RightsSource),
			RefundType:      cast.ToInt64(msgBodyData.RightsInfo.RefundType),
		},
		PushMsh: strBody,
	}
	omsOrderClient := order.GetOmsOrderRpcClient()
	addRes, err := omsOrderClient.WeiMeng.OrderRefundUpdate(omsOrderClient.Ctx, request)
	if err != nil {
		glog.Error(msgBodyData.OrderNo, "-退款单更新,", err.Error(), ",", kit.JsonEncode(msgBodyData))
		return errors.New("退款单更新")
	}
	if addRes.Code != 200 {
		glog.Error(msgBodyData.OrderNo, "-退款单更新,", kit.JsonEncode(msgBodyData))
		return errors.New("退款单更新:" + addRes.Message)
	}
	return nil
}
