package dto

type MpOederRefund struct {
	//外部订单号，最长不超过32个字符
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//原因
	Reason string `json:"reason" form:"reason" query:"reason"`
}

type CancelParam struct {
	OrderId    int64  `json:"order_id" query:"order_id" form:"order_id"`
	Reason     string `json:"reason" query:"reason" form:"reason"`
	ReasonCode int    `json:"reason_code" query:"reason_code" form:"reason_code"`
}
