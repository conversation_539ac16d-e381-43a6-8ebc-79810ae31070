package main

import (
	"flag"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"strings"
	"time"

	"external-ui/route"
	"external-ui/utils"

	"github.com/maybgit/glog"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh
}

// @title 第三方系统对接项目接口文档
// @version 1.0
// @description 这里是描述
// @host localhost:7033
func main() {
	//日志命令行参数化处理，可以启用禁用控制台日志等，defer确认在程序退出时将所有缓冲日志写入es
	defer glog.Flush()
	flag.Parse()

	//创建数据库连接
	utils.SetupDbConn()
	defer utils.CloseDbConn()

	//初始化路由
	e := route.InitRoute()

	addr := ":7033" //默认启动端口
	baseURL, err := config.Get("BaseUrl")
	if err == nil && len(baseURL) > 0 {
		items := strings.Split(baseURL, ":")
		addr = fmt.Sprintf(":%v", items[len(items)-1])
	}

	glog.Info("external-ui:启动端口是 v20230440：" + addr)
	e.Logger.Fatal(e.Start(addr))

}
