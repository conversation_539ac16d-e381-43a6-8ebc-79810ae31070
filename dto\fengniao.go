package dto

type FnCallBackMode struct {
	//对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
	AppId string `json:"app_id"`
	//达达物流订单号，默认为空
	Timestamp string `json:"timestamp"`
	//业务数据
	BusinessData interface{} `json:"business_data"`
}

//蜂鸟授权mode
type AuthMode struct {
	Code       string `json:"code"`
	Scope      string `json:"scope"`
	MerchantID string `json:"merchant_id"`
	State      string `json:"state"`
}

//type BusinessDataMode struct {
//	//通知类型
//	CallbackBusinessType string `json:"callback_business_type"`
//	//通知参数
//	Param interface{} `json:"param"`
//}

type BusinessDataMode struct {
	CallbackBusinessType string `json:"callback_business_type"`
	Param                struct {
		CarrierDriverID    interface{} `json:"carrier_driver_id"`
		CarrierDriverName  string      `json:"carrier_driver_name"`
		CarrierLng         interface{} `json:"carrier_lng"`
		PartnerOrderCode   string      `json:"partner_order_code"`
		RetryFlag          int         `json:"retry_flag"`
		ErrorScene         string      `json:"error_scene"`
		CarrierLat         interface{} `json:"carrier_lat"`
		Description        string      `json:"description"`
		APICode            string      `json:"api_code"`
		OrderStatus        int         `json:"order_status"`
		DetailDescription  string      `json:"detail_description"`
		APIMsg             string      `json:"api_msg"`
		CarrierDriverPhone string      `json:"carrier_driver_phone"`
		ErrorCode          string      `json:"error_code"`
		AppID              string      `json:"app_id"`
		OrderID            int64       `json:"order_id"`
		PushTime           int64       `json:"push_time"`
	} `json:"param"`
}

type BusinessDataModeDetail struct {
	//订单号
	OrderId int64 `json:"order_id"`
	//应用id
	AppId string `json:"app_id"`
	//外部订单号
	PartnerOrderCode string `json:"partner_order_code"`
	//订单生成0，运单生成成功1，20：骑手接单，80：骑手到
	//店，2：配送中，3：已完成，4：已取消，5：配送异常
	OrderStatus int `json:"order_status"`
	//骑手id
	CarrierDriverId int64 `json:"carrier_driver_id"`
	//坐标高德
	CarrierLat string `json:"carrier_lat"`
	//坐标高德
	CarrierLng string `json:"carrier_lng"`
	//骑手姓名
	CarrierDriverName string `json:"carrier_driver_name"`
	//骑手电话
	CarrierDriverPhone string `json:"carrier_driver_phone"`
	//描述
	Description string `json:"description"`
	//异常code
	ErrorCode int64 `json:"error_code"`
	//异常描述
	ErrorScene int `json:"error_scene"`
	//详情描述
	DetailDescription string `json:"detail_description"`
	//订单状态回调错误码
	apiCode int64 `json:"api_code"`
	//订单状态回调错误信息描述
	ApiMsg int64 `json:"api_msg"`
}
