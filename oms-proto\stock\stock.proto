syntax = "proto3";

package stock;

option go_package = "./oms-proto/stock";

service StockService {
  // 冻结占用库存
  rpc LockTakeStock(LockTakeStockRequest) returns (LockTakeStockResponse) {}
  // 解冻占用库存
  rpc UnlockTakeStock(UnlockTakeStockRequest) returns (CommonResponse) {}
  // 取消占用占用库存
  rpc CancelTakeStock(CancelTakeStockRequest) returns (CommonResponse) {}
  // 冻结在途库存
  rpc LockCommingStock(LockCommingStockRequest) returns (CommonResponse) {}
  // 解冻在途库存
  rpc UnlockCommingStock(UnlockCommingStockRequest) returns (CommonResponse) {}
  // 取消占在途库存
  rpc CancelCommingStock(CancelCommingStockRequest) returns (CommonResponse) {}
}

message LockTakeStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 订单号
  string OrderSn = 2;
  // 订单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;
  // 需要冻结货号及库存大小
  repeated SkuStock List = 4;
  // 是否为手动订单
  bool IsManual = 5;
  // 检查库存是否充足
  bool IsEnough = 6;

  int32 BillType = 7;
}

message LockTakeStockResponse {
  // 状态码，200：库存充足并冻结成功，201：库存不足并冻结成功，400：冻结失败，业务处理异常
  int32 Code = 1;
  // 业务错误信息
  string Message = 2;
}

message UnlockTakeStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 销售订单号
  string OrderSn = 2;
  // 销售订单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;

  int32 BillType = 4;
}

message CommonResponse {
  // 状态码，200：业务处理成功，400：业务处理失败
  int32 Code = 1;
  // 业务错误信息
  string Message = 2;
}

message SkuStock {
  // oms系统SkuId
  int64 SkuId = 1;
  // 期望库存大小
  int32 Stock = 2;
}

message LockCommingStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 退款单号
  string OrderSn = 2;
  // 退款单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;
  // 在途仓库货号及库存大小
  repeated SkuStock List = 4;
  // 是否为手动订单
  bool IsManual = 5;
  // 单据类型
  int32 BillType = 6;
}
message UnlockCommingStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 退款单号
  string OrderSn = 2;
  // 退款单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;
  // 是否推送库存给阿闻
  bool IsPushStockToAwen = 4;
  // 单据类型
  int32 BillType = 5;
}

message CancelTakeStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 销售订单号
  string OrderSn = 2;
  // 销售订单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;
}

message CancelCommingStockRequest {
  // 仓库编码
  string WarehouseCode = 1;
  // 退款单号
  string OrderSn = 2;
  // 退款单创建时间，格式：2006-01-02 15:04:05
  string OrderCreateTime = 3;
  // 单据类型
  int32 BillType = 4;
}