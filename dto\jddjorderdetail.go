package dto

type JddjData struct {
	//状态码，0为成功，非0均为失败，其中（1:参数错误，-4为订单中心底层ES中间件服务抖动异常，请重试
	Code   interface{} `json:"code"`
	Msg    string      `json:"msg"`
	Result JddjResult  `json:"JddjResult"`
}

type JddjResult struct {
	PageNo      int32            `json:"pageNo"`
	PageSize    int32            `json:"pageSize"`
	MaxPageSize int32            `json:"maxPageSize"`
	TotalCount  int32            `json:"totalCount"`
	ResultList  []JddjResultList `json:"JddjResultList"`
}

type JddjResultList struct {
	//订单号
	OrderID int64 `json:"orderId"`

	//SrcInnerType int `json:"srcInnerType"`
	//OrderType int `json:"orderType"`
	//订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，
	//31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成）
	OrderStatus int32 `json:"orderStatus"`

	//OrderStatusTime string `json:"orderStatusTime"`
	//OrderStartTime string `json:"orderStartTime"`
	//OrderPurchaseTime string `json:"orderPurchaseTime"`
	//OrderAgingType int `json:"orderAgingType"`
	//预计送达开始时间
	OrderPreStartDeliveryTime string `json:"orderPreStartDeliveryTime"`
	//预计送达结束时间
	OrderPreEndDeliveryTime string `json:"orderPreEndDeliveryTime"`

	//PickDeadline string `json:"pickDeadline"`
	//OrderCancelTime string `json:"orderCancelTime"`
	//OrderCancelRemark string `json:"orderCancelRemark"`
	//商家编码
	OrgCode string `json:"orgCode"`

	//BuyerPin string `json:"buyerPin"`
	//收货人名称
	BuyerFullName string `json:"buyerFullName"`
	//收货人地址
	BuyerFullAddress string `json:"buyerFullAddress"`
	//收货人电话
	BuyerTelephone string `json:"buyerTelephone"`
	//收货人手机号
	BuyerMobile string `json:"buyerMobile"`
	//LastFourDigitsOfBuyerMobile string `json:"lastFourDigitsOfBuyerMobile"`
	//到家配送门店编码
	DeliveryStationNo string `json:"deliveryStationNo"`
	//商家门店编码
	DeliveryStationNoIsv string `json:"deliveryStationNoIsv"`
	//配送门店名称
	DeliveryStationName string `json:"deliveryStationName"`
	//承运商编号(9966:京东众包;2938:商家自送;1130:达达同城送;9999:到店自提)
	DeliveryCarrierNo string `json:"deliveryCarrierNo"`
	//承运商名称
	DeliveryCarrierName string `json:"deliveryCarrierName"`

	//DeliveryBillNo string `json:"deliveryBillNo"`
	//包裹重量（单位：kg）
	DeliveryPackageWeight float64 `json:"deliveryPackageWeight"`
	//妥投时间
	DeliveryConfirmTime string `json:"deliveryConfirmTime"`
	//订单支付类型(1：货到付款，4:在线支付;)
	OrderPayType int32 `json:"orderPayType"`
	//订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：
	//京东支付（无法判断具体京东支付子类型）；9002：京东银行卡支付；9004：京东白条支付；9012：京东余额支付；9022：京东小金库支付
	PayChannel int32 `json:"payChannel"`
	//订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount）
	OrderTotalMoney int32 `json:"orderTotalMoney"`
	//订单级别优惠商品金额：(不含单品促销类优惠金额及运费相关优惠金额)，
	//等于OrderDiscountlist表中，除优惠类型7，8，12，15，16外的优惠金额discountPrice累加和
	OrderDiscountMoney int32 `json:"orderDiscountMoney"`
	//用户支付的实际订单运费：订单应收运费（orderReceivableFreight）-运费优惠（OrderDiscountlist表中，
	//优惠类型7，8，12，15的优惠金额。运费优惠大于应收运费时，实际支付为0
	OrderFreightMoney int32 `json:"orderFreightMoney"`
	//达达同城送运费(单位：分)
	LocalDeliveryMoney int32 `json:"localDeliveryMoney"`
	//商家支付远距离运费(单位：分)。达达配送默认只能服务半径2公里内的用户，
	//商家可与到家运营沟通开通远距离服务，超过2公里后每1公里加收2元运费。费用承担方为商家
	MerchantPaymentDistanceFreightMoney int32 `json:"merchantPaymentDistanceFreightMoney"`
	//订单应收运费：用户应该支付的订单运费，即未优惠前应付运费(不计满免运费，运费优惠券，VIP免基础运费等优惠)，
	//包含用户小费。订单对应门店配送方式为商家自送，则订单应收运费为设置的门店自送运费；订单对应门店配送方式为达达配送，则订单应收运费为用户支付给达达的配送费（平台规则统一设置，如基础运费、重量阶梯运费、距离阶梯运费、夜间或天气等因素的附加运费）
	OrderReceivableFreight int32 `json:"orderReceivableFreight"`
	//用户积分抵扣金额
	PlatformPointsDeductionMoney int32 `json:"platformPointsDeductionMoney"`
	//用户应付金额（单位为分）=商品销售价总金额orderTotalMoney -订单优惠总金额 orderDiscountMoney+
	//实际订单运费orderFreightMoney +包装金额packagingMoney -用户积分抵扣金额platformPointsDeductionMoney
	OrderBuyerPayableMoney int32 `json:"orderBuyerPayableMoney"`
	//包装金额
	PackagingMoney int32 `json:"packagingMoney"`

	//Tips int `json:"tips"`

	//AdjustIsExists bool `json:"adjustIsExists"`
	//AdjustID int `json:"adjustId"`
	//IsGroupon bool `json:"isGroupon"`
	//BuyerCoordType int `json:"buyerCoordType"`
	//收货人地址腾讯坐标经度
	BuyerLng float64 `json:"buyerLng"`
	//收货人地址腾讯坐标纬度
	BuyerLat float64 `json:"buyerLat"`

	//BuyerCity string `json:"buyerCity"`
	//收货人市名称
	BuyerCityName string `json:"buyerCityName"`
	//收货人县(区)ID
	//BuyerCountry string `json:"buyerCountry"`
	//收货人县(区)名称
	BuyerCountryName string `json:"buyerCountryName"`
	//订单买家备注
	OrderBuyerRemark string `json:"orderBuyerRemark"`

	BusinessTag string `json:"businessTag"`

	//EquipmentID string `json:"equipmentId"`
	//BuyerPoi string `json:"buyerPoi"`
	//OrdererName string `json:"ordererName"`
	//OrdererMobile string `json:"ordererMobile"`
	//OrderNum int `json:"orderNum"`
	//UserTip int `json:"userTip"`
	//MiddleNumBindingTime string `json:"middleNumBindingTime"`
	//DeliverInputTime string `json:"deliverInputTime"`
	//BusinessType int `json:"businessType"`
	//VenderVipCardID string `json:"venderVipCardId"`
	//订单开发票标识（1.开发票；2.不开发票）
	OrderInvoiceOpenMark int32 `json:"orderInvoiceOpenMark"`

	//SpecialServiceTag string `json:"specialServiceTag"`
	SrcOrderType int `json:"srcOrderType"`
	//SrcOrderID string `json:"srcOrderId"`
	//下单时间
	OrderStartTime string `json:"orderStartTime"`

	OrderInvoice JddjOrderInvoice  `json:"JddjOrderInvoice"`
	Product      []JddjProductList `json:"JddjProductList"`
	Discount     []JddjDiscount    `json:"JddjDiscount"`
}

type JddjOrderInvoice struct {
	//InvoiceFormType int `json:"invoiceFormType"`
	//发票抬头
	InvoiceTitle string `json:"invoiceTitle"`
	//发票税号
	InvoiceDutyNo string `json:"invoiceDutyNo"`
	//InvoiceMail string `json:"invoiceMail"`
	//InvoiceMoney int `json:"invoiceMoney"`
	//InvoiceType int `json:"invoiceType"`
	//InvoiceMoneyDetail string `json:"invoiceMoneyDetail"`
	//InvoiceAddress string `json:"invoiceAddress"`
	//InvoiceTelNo string `json:"invoiceTelNo"`
	//InvoiceBankName string `json:"invoiceBankName"`
	//InvoiceAccountNo string `json:"invoiceAccountNo"`
	//InvoiceContent string `json:"invoiceContent"`
}

type JddjProductList struct {
	//订单号
	OrderID int64 `json:"orderId"`
	//商品规格，多规格之间用英文分号;分隔
	SkuCostumeProperty string `json:"skuCostumeProperty"`
	//调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
	AdjustID int64 `json:"adjustId"`
	//到家商品编码
	SkuID int64 `json:"skuId"`
	//商品的名称
	SkuName string `json:"skuName"`
	//商家商品编码
	SkuIDIsv string `json:"skuIdIsv"`
	//到家商品销售价
	SkuJdPrice int32 `json:"skuJdPrice"`
	//下单数量
	SkuCount int32 `json:"skuCount"`
	//调整方式(0:默认值，没调整，原订单明细;1:新增;2:删除;3:修改数量)
	AdjustMode int32 `json:"adjustMode"`
	//商品upc码
	UpcCode string `json:"upcCode"`
	//到家商品门店价
	SkuStorePrice int32 `json:"skuStorePrice"`
	//到家商品成本价
	SkuCostPrice int32 `json:"skuCostPrice"`
	//商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、
	//加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，
	//设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、
	//表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,
	//8:第二件N折,9:拼团促销)
	PromotionType int32 `json:"promotionType"`
	//税率
	SkuTaxRate string `json:"skuTaxRate"`
	//促销活动编码
	PromotionID int32 `json:"promotionId"`
	//赠品关联的主商品信息，多个商品之间英文逗号分隔
	RelatedSkus string `json:"relatedSkus"`
	//商品维度的单个商品重量（千克）
	SkuWeight float32 `json:"skuWeight"`
	//餐盒费（商品的总餐盒费）
	CanteenMoney int32 `json:"canteenMoney"`
	//ProductExtendInfoMap string `json:"productExtendInfoMap"`
}

type JddjDiscount struct {
	//订单主表订单id
	OrderID int64 `json:"orderId"`
	//调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
	AdjustID int64 `json:"adjustId"`
	//记录参加活动的sku数组
	SkuIds string `json:"skuIds"`
	//优惠类型(1:优惠码;3:优惠劵;4:满减;5:满折;6:首单优惠;7:VIP免运费;
	//8:商家满免运费;10:满件减;11:满件折;12:首单地推满免运费;15:运费券;16:单品免运)
	DiscountType int32 `json:"discountType"`
	//小优惠类型(优惠码(1:满减;2:立减;3:满折);优惠券(1:满减;2:立减;5满折);
	//满减(1:满减);商家满免运费(null或0：平台承担。
	//1：商家全免运费（包括商家促销免运、商家会员免运），商家承担补贴。
	//2：商家免基础运费（包括商家促销免运），商家承担补贴。
	//3：商家会员免基础运费（包括商家会员免运），商家承担补贴。
	//4：拼团免运。);满件减(1206:满件减);满件折(1207:满件折);运费券（1：满减，2：立减）)
	DiscountDetailType int32 `json:"discountDetailType"`
	//用户领券编号，用户领取优惠券的标识，返回值中，-和#中间的字符串为对应优惠券实际编码，不同用户领券标识不同
	DiscountCode string `json:"discountCode"`
	//优惠金额
	DiscountPrice int32 `json:"discountPrice"`
	//商家承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
	VenderPayMoney int32 `json:"venderPayMoney"`
	//平台承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
	PlatPayMoney int32 `json:"platPayMoney"`
}
