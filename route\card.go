package route

import (
	"external-ui/controller"
	myMiddleware "external-ui/middleware"

	"github.com/labstack/echo/v4"
)

func cardGroup(e *echo.Group) {
	g := e.Group("/card", myMiddleware.AwenSignature("zl"))
	g.POST("/new-by-store", controller.CardNewByStore)
	g.POST("/check-card-id", controller.CardCheckCardId)
	g.GET("/card-value", controller.CardValue)
	g.POST("/return-by-store", controller.CardReturnByStore)
	g.POST("/card-order-value", controller.CardOrderValue)
}
