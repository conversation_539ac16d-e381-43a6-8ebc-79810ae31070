package dto

//订单优惠活动
type OrdersPrivilegeActive struct {
	//活动id
	Actdetailid int `json:"act_detail_id"`
	//活动优惠金额中美团承担成本
	Mtcharge float64 `json:"mt_charge"`
	//活动优惠金额中商家承担成本
	Poicharge float64 `json:"poi_charge"`
	//活动优惠金额
	Reducefee float64 `json:"reduce_fee"`
	//备注
	Remark string `json:"remark"`
	//活动类型
	Activetype int `json:"type"`
}

//推送订单信息修改消息
type OrdersUpdate struct {
	//订单号，数据库中请用bigint(20)存储此字段。
	OrderId int64 `json:"order_id"`
	//APP方门店id
	AppPoiCode float64 `json:"app_poi_code"`
	//信息变更操作人：1-用户；2-美团骑手。
	OpRole int `json:"op_role"`
	//操作员类型，参考值：1-用户；2-商家；3-客服人员；4-BD；5-系统；6-合作中心。
	OpType int `json:"op_type"`
}
