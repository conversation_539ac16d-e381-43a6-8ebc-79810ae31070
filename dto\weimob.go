package dto

//微盟回调响应
type WeiMengAckResponse struct {
	Code *WeiMengAck `json:"code"`
}
type WeiMengAck struct {
	//请求返回的状态码 0表示成功
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type WeiMengCallBackMsg struct {
	Id      string `json:"id"`
	Topic   string `json:"topic"`
	Event   string `json:"event"`
	Wid     string `json:"wid"`
	BosId   int64  `json:"bosId"`
	Sign    string `json:"sign"`
	MsgBody string `json:"msgBody"`
}

type WeiMengOrderSaveMsgBody struct {
	OrderType         int   `json:"orderType"`
	OrderSource       int   `json:"orderSource"`
	OrderNo           int64 `json:"orderNo"`
	ProductId         int   `json:"productId"`
	DeliveryType      int   `json:"deliveryType"`
	ProductInstanceId int   `json:"productInstanceId"`
	OrderStatus       int   `json:"orderStatus"`
	ChannelType       int   `json:"channelType"`
	UpdateTime        int64 `json:"updateTime"`
	ParentOrderNo     int64 `json:"parentOrderNo"`
	SaleChannelType   int   `json:"saleChannelType"`
	PaymentType       int   `json:"paymentType"`
	Vid               int64 `json:"vid"`
	Wid               int64 `json:"wid"`
	CreateTime        int64 `json:"createTime"`
	MerchantId        int64 `json:"merchantId"`
	BosId             int64 `json:"bosId"`
	BizSourceType     int   `json:"bizSourceType"`
	PaymentStatus     int   `json:"paymentStatus"`
	UpdateType        int   `json:"updateType"`
}

//微盟退款单创建/状态更新请求参数
type WeiMengRefundOrderSaveRequest struct {
	Id      string                    `json:"id"`
	Topic   string                    `json:"topic"`
	Event   string                    `json:"event"`
	Wid     string                    `json:"wid"`
	BosId   int64                     `json:"bosId"`
	Sign    string                    `json:"sign"`
	MsgBody *WeiMengRefundSaveMsgBody `json:"msgBody"`
}

type WeiMengRefundSaveMsgBody struct {
	OrderNo      int                      `json:"orderNo"`
	RightsId     int64                    `json:"rightsId"`
	StatusBefore int                      `json:"statusBefore"`
	Status       int                      `json:"status"`
	RightsInfo   *WeiMengRefundRightsInfo `json:"rightsInfo"`
}

type WeiMengRefundRightsInfo struct {
	Vid             int `json:"vid"`
	RefundType      int `json:"refundType"`
	RightsStatus    int `json:"rightsStatus"`
	RightsType      int `json:"rightsType"`
	ProcessVid      int `json:"processVid"`
	RightsCauseType int `json:"rightsCauseType"`
	RightsSource    int `json:"rightsSource"`
}

//微盟订单详情数据结构
type WeiMengOrderDetail1Response struct {
	OrderInfo struct {
		OrderBaseInfo struct {
			AutoCancelTime     int64  `json:"autoCancelTime"`
			OrderType          int32  `json:"orderType"`
			FinishTime         int64  `json:"finishTime"`
			OrderSource        int    `json:"orderSource"`
			OrderNo            int64  `json:"orderNo"`
			PayTime            int64  `json:"payTime"`
			DeliveryType       int32  `json:"deliveryType"`
			OrderStatus        int32  `json:"orderStatus"`
			ChannelType        int    `json:"channelType"`
			ConfirmTime        int64  `json:"confirmTime"`
			UpdateTime         int64  `json:"updateTime"`
			ParentOrderNo      int64  `json:"parentOrderNo"`
			FinishDeliveryTime int64  `json:"finishDeliveryTime"`
			PayType            int    `json:"payType"`
			ThirdOrderNo       string `json:"thirdOrderNo"`
			IsDeleted          int    `json:"isDeleted"`
			CreateTime         int64  `json:"createTime"`
			BizSourceType      int    `json:"bizSourceType"`
			PayStatus          int    `json:"payStatus"`
		} `json:"orderBaseInfo"`
		BuyerInfo struct {
			BuyerRemark    string `json:"buyerRemark"`
			Wid            int    `json:"wid"`
			UserNickName   string `json:"userNickName"`
			MemberBenefits []struct {
				BenefitType int `json:"benefitType"`
			} `json:"memberBenefits"`
		} `json:"buyerInfo"`
		OrderFulfill struct {
			DeliveryCoName  string `json:"deliveryCoName"`
			CustomFieldInfo []struct {
				ShowScene []struct {
				} `json:"showScene"`
				Name  string `json:"name"`
				Sort  int    `json:"sort"`
				Type  string `json:"type"`
				Value string `json:"value"`
				Key   string `json:"key"`
			} `json:"customFieldInfo"`
			ExpRcvStartTime int64 `json:"expRcvStartTime"`
			DeliveryTime    int64 `json:"deliveryTime"`
			ExpRcvType      int   `json:"expRcvType"`
			FulfillStatus   int   `json:"fulfillStatus"`
			ReceiverInfo    struct {
				Area            string `json:"area"`
				Zip             string `json:"zip"`
				Address         string `json:"address"`
				City            string `json:"city"`
				CityCode        string `json:"cityCode"`
				ProvinceCode    string `json:"provinceCode"`
				Latitude        string `json:"latitude"`
				County          string `json:"county"`
				CertificateInfo struct {
					BehindImg       string `json:"behindImg"`
					FrontImg        string `json:"frontImg"`
					UserName        string `json:"userName"`
					CertificateNo   string `json:"certificateNo"`
					CertificateType string `json:"certificateType"`
				} `json:"certificateInfo"`
				AreaCode   string `json:"areaCode"`
				CountyCode string `json:"countyCode"`
				Province   string `json:"province"`
				Phone      string `json:"phone"`
				Name       string `json:"name"`
				Longitude  string `json:"longitude"`
			} `json:"receiverInfo"`
			DeliveryType  int    `json:"deliveryType"`
			ExpRcvDate    int64  `json:"expRcvDate"`
			ExpRcvEndTime int64  `json:"expRcvEndTime"`
			DeliveryCode  string `json:"deliveryCode"`
		} `json:"orderFulfill"`
		CancelInfo struct {
			Reason        string `json:"reason"`
			CancelType    int    `json:"cancelType"`
			SpecialReason string `json:"specialReason"`
			Id            int    `json:"id"`
		} `json:"cancelInfo"`
		FlagInfo struct {
			FlagRank    int    `json:"flagRank"`
			FlagContent string `json:"flagContent"`
		} `json:"flagInfo"`
		TotalDiscounts []struct {
			DiscountAmount  float64 `json:"discountAmount"`
			DiscountType    int     `json:"discountType"`
			AttributionType int     `json:"attributionType"`
		} `json:"totalDiscounts"`
		OrderBizExt struct {
			OperatorInfo struct {
				OperatorPhone string `json:"operatorPhone"`
				OperatorId    string `json:"operatorId"`
				OperatorName  string `json:"operatorName"`
			} `json:"operatorInfo"`
			LabelInfos []struct {
				Attachment string `json:"attachment"`
				LabelType  string `json:"labelType"`
				AttachId   string `json:"attachId"`
			} `json:"labelInfos"`
			FeatureType int `json:"featureType"`
			FinishInfo  struct {
				FinishOrderType int `json:"finishOrderType"`
			} `json:"finishInfo"`
			SaleChannelType int `json:"saleChannelType"`
		} `json:"orderBizExt"`
		MerchantInfo struct {
			ProductId         int    `json:"productId"`
			ProductInstanceId int    `json:"productInstanceId"`
			ProcessVid        int64  `json:"processVid"`
			ProductName       string `json:"productName"`
			VidName           string `json:"vidName"`
			ProcessVidName    string `json:"processVidName"`
			Vid               int64  `json:"vid"`
			MerchantId        int64  `json:"merchantId"`
			BosName           string `json:"bosName"`
			BosId             int64  `json:"bosId"`
			MerchantExtInfo   struct {
				ProcessVidNumber string `json:"processVidNumber"`
				VidNumber        string `json:"vidNumber"`
				DeliveryVid      int64  `json:"deliveryVid"`
			} `json:"merchantExtInfo"`
			ProcessVidType int `json:"processVidType"`
			VidType        int `json:"vidType"`
		} `json:"merchantInfo"`
		Items []struct {
			SalePrice float64 `json:"salePrice"`
			GoodsExt  struct {
				OriginSkuNum  int    `json:"originSkuNum"`
				OriginSkuName string `json:"originSkuName"`
				GroupInfos    []struct {
					Grade   int `json:"grade"`
					GroupId int `json:"groupId"`
				} `json:"groupInfos"`
				ProductCategory string `json:"productCategory"`
			} `json:"goodsExt"`
			GoodsId       int64    `json:"goodsId"`
			CategoryTitle []string `json:"categoryTitle"`
			SkuBarCode    string   `json:"skuBarCode"`
			PriceInfos    []struct {
				Amount      float64 `json:"amount"`
				Description string  `json:"description"`
				Type        int     `json:"type"`
			} `json:"priceInfos"`
			SkuAttrInfo      string `json:"skuAttrInfo"`
			GoodsType        int    `json:"goodsType"`
			UnitType         int    `json:"unitType"`
			ItemId           int    `json:"itemId"`
			SubGoodsType     int    `json:"subGoodsType"`
			ActivityTypeList []int  `json:"activityTypeList"`
			ProductInfos     []struct {
				ItemSkuQuantity int    `json:"itemSkuQuantity"`
				Price           int    `json:"price"`
				CombSkuId       int64  `json:"combSkuId"`
				CombTitle       string `json:"combTitle"`
				Title           string `json:"title"`
				WarehouseInfos  []struct {
					Quantity      int    `json:"quantity"`
					WarehouseId   int    `json:"warehouseId"`
					WarehouseType string `json:"warehouseType"`
					WarehouseName string `json:"warehouseName"`
				} `json:"warehouseInfos"`
				ProductExt struct {
					Unit             string `json:"unit"`
					ProductCode      string `json:"productCode"`
					ImageUrl         string `json:"imageUrl"`
					ProductAttribute string `json:"productAttribute"`
				} `json:"productExt"`
				ProductType int `json:"productType"`
			} `json:"productInfos"`
			ImageUrl   string `json:"imageUrl"`
			GoodsTitle string `json:"goodsTitle"`
			GoodsCode  string `json:"goodsCode"`
			PayInfo    struct {
				ShouldPayAmount     float64 `json:"shouldPayAmount"`
				TotalAmount         float64 `json:"totalAmount"`
				PayAmount           float64 `json:"payAmount"`
				TotalDiscountAmount float64 `json:"totalDiscountAmount"`
				AmountInfos         []struct {
					ShouldPayAmount float64 `json:"shouldPayAmount"`
					Amount          float64 `json:"amount"`
					PayAmount       float64 `json:"payAmount"`
					Description     string  `json:"description"`
					Type            int     `json:"type"`
				} `json:"amountInfos"`
			} `json:"payInfo"`
			CategoryId int `json:"categoryId"`
			ItemBizExt struct {
				GoodsCustom struct {
					CustomFields []struct {
						Name  string `json:"name"`
						Sort  int    `json:"sort"`
						Type  string `json:"type"`
						Value string `json:"value"`
						Key   string `json:"key"`
					} `json:"customFields"`
					CloudCustom string `json:"cloudCustom"`
				} `json:"goodsCustom"`
				LabelInfos []struct {
					Attachment string `json:"attachment"`
					LabelType  string `json:"labelType"`
					AttachId   string `json:"attachId"`
				} `json:"labelInfos"`
				GoodsSellMode         int    `json:"goodsSellMode"`
				ExpandField           string `json:"expandField"`
				GoodsPromotionOrderId string `json:"goodsPromotionOrderId"`
				RightsServiceType     int    `json:"rightsServiceType"`
				AbilityCode           []struct {
				} `json:"abilityCode"`
				GoodsLimitSwitch   int    `json:"goodsLimitSwitch"`
				GoodsPromotionType int    `json:"goodsPromotionType"`
				ActivityStockType  int    `json:"activityStockType"`
				OuterMerchantId    string `json:"outerMerchantId"`
				GoodsGuideType     int    `json:"goodsGuideType"`
				OuterSkuId         string `json:"outerSkuId"`
				BizInfos           []struct {
					SubBizType int    `json:"subBizType"`
					BizType    int    `json:"bizType"`
					BizOrderId string `json:"bizOrderId"`
					BizId      int64  `json:"bizId"`
				} `json:"bizInfos"`
				GoodsSourceType int    `json:"goodsSourceType"`
				OuterGoodsId    string `json:"outerGoodsId"`
			} `json:"itemBizExt"`
			SkuCode       string  `json:"skuCode"`
			SkuId         int64   `json:"skuId"`
			SkuNum        float64 `json:"skuNum"`
			DiscountInfos []struct {
				DiscountLevel int `json:"discountLevel"`
				DiscountExt   struct {
					AttributionType int `json:"attributionType"`
				} `json:"discountExt"`
				Name           string  `json:"name"`
				CostAmount     float64 `json:"costAmount"`
				DiscountAmount float64 `json:"discountAmount"`
				DiscountType   int     `json:"discountType"`
				SubType        int     `json:"subType"`
				DiscountId     string  `json:"discountId"`
			} `json:"discountInfos"`
		} `json:"items"`
		PayInfo struct {
			ShouldPayAmount     float64 `json:"shouldPayAmount"`
			TotalAmount         float64 `json:"totalAmount"`
			PayAmount           float64 `json:"payAmount"`
			TotalDiscountAmount float64 `json:"totalDiscountAmount"`
			AmountInfos         []struct {
				ShouldPayAmount float64 `json:"shouldPayAmount"`
				Amount          float64 `json:"amount"`
				PayAmount       float64 `json:"payAmount"`
				Description     string  `json:"description"`
				Type            int     `json:"type"`
			} `json:"amountInfos"`
			PayItems []struct {
				PayTradeId     int    `json:"payTradeId"`
				Phase          int    `json:"phase"`
				PayType        int    `json:"payType"`
				PayTime        int64  `json:"payTime"`
				ChannelTrxNo   string `json:"channelTrxNo"`
				PayMethodIds   []int  `json:"payMethodIds"`
				PayId          int    `json:"payId"`
				TradeId        string `json:"tradeId"`
				PayItemExtInfo struct {
					Amount     float64 `json:"amount"`
					InteractId string  `json:"interactId"`
				} `json:"payItemExtInfo"`
			} `json:"payItems"`
		} `json:"payInfo"`
		GuideInfo struct {
			PrivateGuiderWid  int    `json:"privateGuiderWid"`
			GuiderName        string `json:"guiderName"`
			GuiderWid         int    `json:"guiderWid"`
			PrivateGuiderName string `json:"privateGuiderName"`
			GuiderNo          string `json:"guiderNo"`
			BuyerExpandInfo   struct {
				PersonalGuiderName    string `json:"personalGuiderName"`
				PersonalGuiderWid     int    `json:"personalGuiderWid"`
				AttributionStoreId    int    `json:"attributionStoreId"`
				AttributionStoreName  string `json:"attributionStoreName"`
				PersonalGuiderStoreId int    `json:"personalGuiderStoreId"`
			} `json:"buyerExpandInfo"`
			PersonalGuiderNo string `json:"personalGuiderNo"`
		} `json:"guideInfo"`
		DiscountInfos []struct {
			DiscountLevel int `json:"discountLevel"`
			DiscountExt   struct {
				AttributionType int `json:"attributionType"`
			} `json:"discountExt"`
			Name           string  `json:"name"`
			CostAmount     float64 `json:"costAmount"`
			DiscountAmount float64 `json:"discountAmount"`
			DiscountType   int32   `json:"discountType"`
			SubType        int     `json:"subType"`
			DiscountId     string  `json:"discountId"`
		} `json:"discountInfos"`
	} `json:"orderInfo"`
	RightsInfos []struct {
		OrderNo          int64  `json:"orderNo"`
		RightsStatus     int    `json:"rightsStatus"`
		RightsId         int64  `json:"rightsId"`
		RightsStatusName string `json:"rightsStatusName"`
		RightsItemId     int64  `json:"rightsItemId"`
	} `json:"rightsInfos"`
	FulfillInfoList []struct {
		Exception     string `json:"exception"`
		FulfillType   int    `json:"fulfillType"`
		ReceivingTime int64  `json:"receivingTime"`
		DeliveryInfo  struct {
			CompanyCode             string `json:"companyCode"`
			Number                  string `json:"number"`
			ExpectReceivedStartTime int64  `json:"expectReceivedStartTime"`
			ExpectReceivedEndTime   int64  `json:"expectReceivedEndTime"`
			WriteOffId              int    `json:"writeOffId"`
			CompanyName             string `json:"companyName"`
			StatusName              string `json:"statusName"`
			ExpectReceivedDate      int64  `json:"expectReceivedDate"`
			WriteOffName            string `json:"writeOffName"`
			ExpectReceivedTypeName  string `json:"expectReceivedTypeName"`
			ExpectReceivedType      int    `json:"expectReceivedType"`
			Status                  int    `json:"status"`
		} `json:"deliveryInfo"`
		OrderNo  int64 `json:"orderNo"`
		SendInfo struct {
			AddressInfo struct {
				Area       string `json:"area"`
				Zip        string `json:"zip"`
				Address    string `json:"address"`
				AddressExt struct {
					AreaCode     string `json:"areaCode"`
					CountyCode   string `json:"countyCode"`
					CityCode     string `json:"cityCode"`
					ProvinceCode string `json:"provinceCode"`
				} `json:"addressExt"`
				Province  string `json:"province"`
				City      string `json:"city"`
				Latitude  string `json:"latitude"`
				County    string `json:"county"`
				Longitude string `json:"longitude"`
			} `json:"addressInfo"`
			SenderAddress string `json:"senderAddress"`
			Sender        struct {
				SenderName   string `json:"senderName"`
				SenderMobile string `json:"senderMobile"`
			} `json:"sender"`
		} `json:"sendInfo"`
		DeliveryTime      int64 `json:"deliveryTime"`
		ExpectFulfillTime int64 `json:"expectFulfillTime"`
		ConsignOrder      struct {
			ConsignTime    int64 `json:"consignTime"`
			CancelTime     int64 `json:"cancelTime"`
			CancelType     int   `json:"cancelType"`
			ConfirmEndTime int64 `json:"confirmEndTime"`
			FulfillNo      int64 `json:"fulfillNo"`
			ConsignItems   []struct {
				Code       string `json:"code"`
				Name       string `json:"name"`
				TemplateId int    `json:"templateId"`
				Type       int    `json:"type"`
			} `json:"consignItems"`
			ConsignVidName string `json:"consignVidName"`
			ConfirmTime    int64  `json:"confirmTime"`
			PickupCode     string `json:"pickupCode"`
			ConfirmInfo    struct {
				Vid     int64  `json:"vid"`
				Wid     int64  `json:"wid"`
				Phone   string `json:"phone"`
				Name    string `json:"name"`
				VidName string `json:"vidName"`
				VidType int    `json:"vidType"`
			} `json:"confirmInfo"`
			ConfirmType   int `json:"confirmType"`
			LogisticsInfo struct {
				CompanyCode string `json:"companyCode"`
				CompanyName string `json:"companyName"`
				DeliveryNo  string `json:"deliveryNo"`
				Appointment int64  `json:"appointment"`
				Remark      string `json:"remark"`
				OutOrderNo  string `json:"outOrderNo"`
			} `json:"logisticsInfo"`
		} `json:"consignOrder"`
		ReceiveInfo struct {
			ReceiverAddress string `json:"receiverAddress"`
			AddressInfo     struct {
				Area       string `json:"area"`
				Zip        string `json:"zip"`
				Address    string `json:"address"`
				AddressExt struct {
					AreaCode     string `json:"areaCode"`
					CountyCode   string `json:"countyCode"`
					CityCode     string `json:"cityCode"`
					ProvinceCode string `json:"provinceCode"`
				} `json:"addressExt"`
				Province  string `json:"province"`
				City      string `json:"city"`
				Latitude  string `json:"latitude"`
				County    string `json:"county"`
				Longitude string `json:"longitude"`
			} `json:"addressInfo"`
			Receiver struct {
				IdCardExt struct {
					BehindImg  string `json:"behindImg"`
					IsVerified bool   `json:"isVerified"`
					FrontImg   string `json:"frontImg"`
					UserName   string `json:"userName"`
				} `json:"idCardExt"`
				ReceiverName   string `json:"receiverName"`
				IdCardNo       string `json:"idCardNo"`
				ReceiverMobile string `json:"receiverMobile"`
			} `json:"receiver"`
			PickUpVid  int    `json:"pickUpVid"`
			PickUpName string `json:"pickUpName"`
		} `json:"receiveInfo"`
		Remark     string `json:"remark"`
		UpdateTime string `json:"updateTime"`
		BuyerInfo  struct {
			BuyerRemark string `json:"buyerRemark"`
			Wid         int    `json:"wid"`
		} `json:"buyerInfo"`
		AutoReceivingTime int64 `json:"autoReceivingTime"`
		CancelTime        int64 `json:"cancelTime"`
		FulfillStatus     int   `json:"fulfillStatus"`
		FulfillNo         int64 `json:"fulfillNo"`
		FulfillItemList   []struct {
			Product struct {
				ItemProducts []struct {
					ItemSkuQuantity int   `json:"itemSkuQuantity"`
					ItemSkuId       int64 `json:"itemSkuId"`
					CombSkuId       int64 `json:"combSkuId"`
					Warehouses      []struct {
						Quantity      int    `json:"quantity"`
						WarehouseId   int    `json:"warehouseId"`
						WarehouseType string `json:"warehouseType"`
						WarehouseName string `json:"warehouseName"`
					} `json:"warehouses"`
					ProductType int `json:"productType"`
				} `json:"itemProducts"`
			} `json:"product"`
			DeliveryNum int `json:"deliveryNum"`
			GoodsId     int `json:"goodsId"`
			OrderItemId int `json:"orderItemId"`
		} `json:"fulfillItemList"`
		IsSplitPackage   int   `json:"isSplitPackage"`
		AutoDeliveryTime int64 `json:"autoDeliveryTime"`
		DeliveryVid      int64 `json:"deliveryVid"`
		FulfillMethod    int   `json:"fulfillMethod"`
	} `json:"fulfillInfoList"`
}

//微盟订单详情数据结构
type WeiMengOrderDetailResponse struct {
	Code struct {
		Errcode string `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	} `json:"code"`
	Data struct {
		OrderInfo struct {
			OrderBaseInfo struct {
				AutoCancelTime     int64  `json:"autoCancelTime"`
				OrderType          int32  `json:"orderType"`
				FinishTime         int64  `json:"finishTime"`
				OrderSource        int    `json:"orderSource"`
				OrderNo            int64  `json:"orderNo"`
				PayTime            int64  `json:"payTime"`
				DeliveryType       int32  `json:"deliveryType"`
				OrderStatus        int32  `json:"orderStatus"`
				ChannelType        int    `json:"channelType"`
				ConfirmTime        int64  `json:"confirmTime"`
				UpdateTime         int64  `json:"updateTime"`
				ParentOrderNo      int64  `json:"parentOrderNo"`
				FinishDeliveryTime int64  `json:"finishDeliveryTime"`
				PayType            int    `json:"payType"`
				ThirdOrderNo       string `json:"thirdOrderNo"`
				IsDeleted          int    `json:"isDeleted"`
				CreateTime         int64  `json:"createTime"`
				BizSourceType      int    `json:"bizSourceType"`
				PayStatus          int    `json:"payStatus"`
			} `json:"orderBaseInfo"`
			BuyerInfo struct {
				BuyerRemark    string `json:"buyerRemark"`
				Wid            int    `json:"wid"`
				UserNickName   string `json:"userNickName"`
				MemberBenefits []struct {
					BenefitType int `json:"benefitType"`
				} `json:"memberBenefits"`
			} `json:"buyerInfo"`
			OrderFulfill struct {
				DeliveryCoName  string `json:"deliveryCoName"`
				CustomFieldInfo []struct {
					ShowScene []struct {
					} `json:"showScene"`
					Name  string `json:"name"`
					Sort  int    `json:"sort"`
					Type  string `json:"type"`
					Value string `json:"value"`
					Key   string `json:"key"`
				} `json:"customFieldInfo"`
				ExpRcvStartTime int64 `json:"expRcvStartTime"`
				DeliveryTime    int64 `json:"deliveryTime"`
				ExpRcvType      int   `json:"expRcvType"`
				FulfillStatus   int   `json:"fulfillStatus"`
				ReceiverInfo    struct {
					Area            string `json:"area"`
					Zip             string `json:"zip"`
					Address         string `json:"address"`
					City            string `json:"city"`
					CityCode        string `json:"cityCode"`
					ProvinceCode    string `json:"provinceCode"`
					Latitude        string `json:"latitude"`
					County          string `json:"county"`
					CertificateInfo struct {
						BehindImg       string `json:"behindImg"`
						FrontImg        string `json:"frontImg"`
						UserName        string `json:"userName"`
						CertificateNo   string `json:"certificateNo"`
						CertificateType string `json:"certificateType"`
					} `json:"certificateInfo"`
					AreaCode   string `json:"areaCode"`
					CountyCode string `json:"countyCode"`
					Province   string `json:"province"`
					Phone      string `json:"phone"`
					Name       string `json:"name"`
					Longitude  string `json:"longitude"`
				} `json:"receiverInfo"`
				DeliveryType  int    `json:"deliveryType"`
				ExpRcvDate    int64  `json:"expRcvDate"`
				ExpRcvEndTime int64  `json:"expRcvEndTime"`
				DeliveryCode  string `json:"deliveryCode"`
			} `json:"orderFulfill"`
			CancelInfo struct {
				Reason        string `json:"reason"`
				CancelType    int    `json:"cancelType"`
				SpecialReason string `json:"specialReason"`
				Id            int    `json:"id"`
			} `json:"cancelInfo"`
			FlagInfo struct {
				FlagRank    int    `json:"flagRank"`
				FlagContent string `json:"flagContent"`
			} `json:"flagInfo"`
			TotalDiscounts []struct {
				DiscountAmount  float64 `json:"discountAmount"`
				DiscountType    int     `json:"discountType"`
				AttributionType int     `json:"attributionType"`
			} `json:"totalDiscounts"`
			OrderBizExt struct {
				OperatorInfo struct {
					OperatorPhone string `json:"operatorPhone"`
					OperatorId    string `json:"operatorId"`
					OperatorName  string `json:"operatorName"`
				} `json:"operatorInfo"`
				LabelInfos []struct {
					Attachment string `json:"attachment"`
					LabelType  string `json:"labelType"`
					AttachId   string `json:"attachId"`
				} `json:"labelInfos"`
				FeatureType int `json:"featureType"`
				FinishInfo  struct {
					FinishOrderType int `json:"finishOrderType"`
				} `json:"finishInfo"`
				SaleChannelType int `json:"saleChannelType"`
			} `json:"orderBizExt"`
			MerchantInfo struct {
				ProductId         int    `json:"productId"`
				ProductInstanceId int    `json:"productInstanceId"`
				ProcessVid        int64  `json:"processVid"`
				ProductName       string `json:"productName"`
				VidName           string `json:"vidName"`
				ProcessVidName    string `json:"processVidName"`
				Vid               int64  `json:"vid"`
				MerchantId        int64  `json:"merchantId"`
				BosName           string `json:"bosName"`
				BosId             int64  `json:"bosId"`
				MerchantExtInfo   struct {
					ProcessVidNumber string `json:"processVidNumber"`
					VidNumber        string `json:"vidNumber"`
					DeliveryVid      int64  `json:"deliveryVid"`
				} `json:"merchantExtInfo"`
				ProcessVidType int `json:"processVidType"`
				VidType        int `json:"vidType"`
			} `json:"merchantInfo"`
			Items []struct {
				SalePrice float64 `json:"salePrice"`
				GoodsExt  struct {
					OriginSkuNum  int    `json:"originSkuNum"`
					OriginSkuName string `json:"originSkuName"`
					GroupInfos    []struct {
						Grade   int `json:"grade"`
						GroupId int `json:"groupId"`
					} `json:"groupInfos"`
					ProductCategory string `json:"productCategory"`
				} `json:"goodsExt"`
				GoodsId       int64    `json:"goodsId"`
				CategoryTitle []string `json:"categoryTitle"`
				SkuBarCode    string   `json:"skuBarCode"`
				PriceInfos    []struct {
					Amount      float64 `json:"amount"`
					Description string  `json:"description"`
					Type        int     `json:"type"`
				} `json:"priceInfos"`
				SkuAttrInfo      string `json:"skuAttrInfo"`
				GoodsType        int    `json:"goodsType"`
				UnitType         int    `json:"unitType"`
				ItemId           int    `json:"itemId"`
				SubGoodsType     int    `json:"subGoodsType"`
				ActivityTypeList []int  `json:"activityTypeList"`
				ProductInfos     []struct {
					ItemSkuQuantity int    `json:"itemSkuQuantity"`
					Price           int    `json:"price"`
					CombSkuId       int64  `json:"combSkuId"`
					CombTitle       string `json:"combTitle"`
					Title           string `json:"title"`
					WarehouseInfos  []struct {
						Quantity      int    `json:"quantity"`
						WarehouseId   int    `json:"warehouseId"`
						WarehouseType string `json:"warehouseType"`
						WarehouseName string `json:"warehouseName"`
					} `json:"warehouseInfos"`
					ProductExt struct {
						Unit             string `json:"unit"`
						ProductCode      string `json:"productCode"`
						ImageUrl         string `json:"imageUrl"`
						ProductAttribute string `json:"productAttribute"`
					} `json:"productExt"`
					ProductType int `json:"productType"`
				} `json:"productInfos"`
				ImageUrl   string `json:"imageUrl"`
				GoodsTitle string `json:"goodsTitle"`
				GoodsCode  string `json:"goodsCode"`
				PayInfo    struct {
					ShouldPayAmount     float64 `json:"shouldPayAmount"`
					TotalAmount         float64 `json:"totalAmount"`
					PayAmount           float64 `json:"payAmount"`
					TotalDiscountAmount float64 `json:"totalDiscountAmount"`
					AmountInfos         []struct {
						ShouldPayAmount float64 `json:"shouldPayAmount"`
						Amount          float64 `json:"amount"`
						PayAmount       float64 `json:"payAmount"`
						Description     string  `json:"description"`
						Type            int     `json:"type"`
					} `json:"amountInfos"`
				} `json:"payInfo"`
				CategoryId int `json:"categoryId"`
				ItemBizExt struct {
					GoodsCustom struct {
						CustomFields []struct {
							Name  string `json:"name"`
							Sort  int    `json:"sort"`
							Type  string `json:"type"`
							Value string `json:"value"`
							Key   string `json:"key"`
						} `json:"customFields"`
						CloudCustom string `json:"cloudCustom"`
					} `json:"goodsCustom"`
					LabelInfos []struct {
						Attachment string `json:"attachment"`
						LabelType  string `json:"labelType"`
						AttachId   string `json:"attachId"`
					} `json:"labelInfos"`
					GoodsSellMode         int    `json:"goodsSellMode"`
					ExpandField           string `json:"expandField"`
					GoodsPromotionOrderId string `json:"goodsPromotionOrderId"`
					RightsServiceType     int    `json:"rightsServiceType"`
					AbilityCode           []struct {
					} `json:"abilityCode"`
					GoodsLimitSwitch   int    `json:"goodsLimitSwitch"`
					GoodsPromotionType int    `json:"goodsPromotionType"`
					ActivityStockType  int    `json:"activityStockType"`
					OuterMerchantId    string `json:"outerMerchantId"`
					GoodsGuideType     int    `json:"goodsGuideType"`
					OuterSkuId         string `json:"outerSkuId"`
					BizInfos           []struct {
						SubBizType int    `json:"subBizType"`
						BizType    int    `json:"bizType"`
						BizOrderId string `json:"bizOrderId"`
						BizId      int64  `json:"bizId"`
					} `json:"bizInfos"`
					GoodsSourceType int    `json:"goodsSourceType"`
					OuterGoodsId    string `json:"outerGoodsId"`
				} `json:"itemBizExt"`
				SkuCode       string  `json:"skuCode"`
				SkuId         int64   `json:"skuId"`
				SkuNum        float64 `json:"skuNum"`
				DiscountInfos []struct {
					DiscountLevel int `json:"discountLevel"`
					DiscountExt   struct {
						AttributionType int `json:"attributionType"`
					} `json:"discountExt"`
					Name           string  `json:"name"`
					CostAmount     float64 `json:"costAmount"`
					DiscountAmount float64 `json:"discountAmount"`
					DiscountType   int     `json:"discountType"`
					SubType        int     `json:"subType"`
					DiscountId     string  `json:"discountId"`
				} `json:"discountInfos"`
			} `json:"items"`
			PayInfo struct {
				ShouldPayAmount     int     `json:"shouldPayAmount"`
				TotalAmount         int     `json:"totalAmount"`
				PayAmount           float64 `json:"payAmount"`
				TotalDiscountAmount float64 `json:"totalDiscountAmount"`
				AmountInfos         []struct {
					ShouldPayAmount float64 `json:"shouldPayAmount"`
					Amount          float64 `json:"amount"`
					PayAmount       float64 `json:"payAmount"`
					Description     string  `json:"description"`
					Type            int     `json:"type"`
				} `json:"amountInfos"`
				PayItems []struct {
					PayTradeId     int    `json:"payTradeId"`
					Phase          int    `json:"phase"`
					PayType        int    `json:"payType"`
					PayTime        int64  `json:"payTime"`
					ChannelTrxNo   string `json:"channelTrxNo"`
					PayMethodIds   []int  `json:"payMethodIds"`
					PayId          int    `json:"payId"`
					TradeId        string `json:"tradeId"`
					PayItemExtInfo struct {
						Amount     float64 `json:"amount"`
						InteractId string  `json:"interactId"`
					} `json:"payItemExtInfo"`
				} `json:"payItems"`
			} `json:"payInfo"`
			GuideInfo struct {
				PrivateGuiderWid  int    `json:"privateGuiderWid"`
				GuiderName        string `json:"guiderName"`
				GuiderWid         int    `json:"guiderWid"`
				PrivateGuiderName string `json:"privateGuiderName"`
				GuiderNo          string `json:"guiderNo"`
				BuyerExpandInfo   struct {
					PersonalGuiderName    string `json:"personalGuiderName"`
					PersonalGuiderWid     int    `json:"personalGuiderWid"`
					AttributionStoreId    int    `json:"attributionStoreId"`
					AttributionStoreName  string `json:"attributionStoreName"`
					PersonalGuiderStoreId int    `json:"personalGuiderStoreId"`
				} `json:"buyerExpandInfo"`
				PersonalGuiderNo string `json:"personalGuiderNo"`
			} `json:"guideInfo"`
			DiscountInfos []struct {
				DiscountLevel int `json:"discountLevel"`
				DiscountExt   struct {
					AttributionType int `json:"attributionType"`
				} `json:"discountExt"`
				Name           string  `json:"name"`
				CostAmount     float64 `json:"costAmount"`
				DiscountAmount float64 `json:"discountAmount"`
				DiscountType   int32   `json:"discountType"`
				SubType        int     `json:"subType"`
				DiscountId     string  `json:"discountId"`
			} `json:"discountInfos"`
		} `json:"orderInfo"`
		RightsInfos []struct {
			OrderNo          int64  `json:"orderNo"`
			RightsStatus     int    `json:"rightsStatus"`
			RightsId         int64  `json:"rightsId"`
			RightsStatusName string `json:"rightsStatusName"`
			RightsItemId     int64  `json:"rightsItemId"`
		} `json:"rightsInfos"`
		FulfillInfoList []struct {
			Exception     string `json:"exception"`
			FulfillType   int    `json:"fulfillType"`
			ReceivingTime int64  `json:"receivingTime"`
			DeliveryInfo  struct {
				CompanyCode             string `json:"companyCode"`
				Number                  string `json:"number"`
				ExpectReceivedStartTime int64  `json:"expectReceivedStartTime"`
				ExpectReceivedEndTime   int64  `json:"expectReceivedEndTime"`
				WriteOffId              int    `json:"writeOffId"`
				CompanyName             string `json:"companyName"`
				StatusName              string `json:"statusName"`
				ExpectReceivedDate      int64  `json:"expectReceivedDate"`
				WriteOffName            string `json:"writeOffName"`
				ExpectReceivedTypeName  string `json:"expectReceivedTypeName"`
				ExpectReceivedType      int    `json:"expectReceivedType"`
				Status                  int    `json:"status"`
			} `json:"deliveryInfo"`
			OrderNo  int64 `json:"orderNo"`
			SendInfo struct {
				AddressInfo struct {
					Area       string `json:"area"`
					Zip        string `json:"zip"`
					Address    string `json:"address"`
					AddressExt struct {
						AreaCode     string `json:"areaCode"`
						CountyCode   string `json:"countyCode"`
						CityCode     string `json:"cityCode"`
						ProvinceCode string `json:"provinceCode"`
					} `json:"addressExt"`
					Province  string `json:"province"`
					City      string `json:"city"`
					Latitude  string `json:"latitude"`
					County    string `json:"county"`
					Longitude string `json:"longitude"`
				} `json:"addressInfo"`
				SenderAddress string `json:"senderAddress"`
				Sender        struct {
					SenderName   string `json:"senderName"`
					SenderMobile string `json:"senderMobile"`
				} `json:"sender"`
			} `json:"sendInfo"`
			DeliveryTime      int64 `json:"deliveryTime"`
			ExpectFulfillTime int64 `json:"expectFulfillTime"`
			ConsignOrder      struct {
				ConsignTime    int64 `json:"consignTime"`
				CancelTime     int64 `json:"cancelTime"`
				CancelType     int   `json:"cancelType"`
				ConfirmEndTime int64 `json:"confirmEndTime"`
				FulfillNo      int64 `json:"fulfillNo"`
				ConsignItems   []struct {
					Code       string `json:"code"`
					Name       string `json:"name"`
					TemplateId int    `json:"templateId"`
					Type       int    `json:"type"`
				} `json:"consignItems"`
				ConsignVidName string `json:"consignVidName"`
				ConfirmTime    int64  `json:"confirmTime"`
				PickupCode     string `json:"pickupCode"`
				ConfirmInfo    struct {
					Vid     int64  `json:"vid"`
					Wid     int64  `json:"wid"`
					Phone   string `json:"phone"`
					Name    string `json:"name"`
					VidName string `json:"vidName"`
					VidType int    `json:"vidType"`
				} `json:"confirmInfo"`
				ConfirmType   int `json:"confirmType"`
				LogisticsInfo struct {
					CompanyCode string `json:"companyCode"`
					CompanyName string `json:"companyName"`
					DeliveryNo  string `json:"deliveryNo"`
					Appointment int64  `json:"appointment"`
					Remark      string `json:"remark"`
					OutOrderNo  string `json:"outOrderNo"`
				} `json:"logisticsInfo"`
			} `json:"consignOrder"`
			ReceiveInfo struct {
				ReceiverAddress string `json:"receiverAddress"`
				AddressInfo     struct {
					Area       string `json:"area"`
					Zip        string `json:"zip"`
					Address    string `json:"address"`
					AddressExt struct {
						AreaCode     string `json:"areaCode"`
						CountyCode   string `json:"countyCode"`
						CityCode     string `json:"cityCode"`
						ProvinceCode string `json:"provinceCode"`
					} `json:"addressExt"`
					Province  string `json:"province"`
					City      string `json:"city"`
					Latitude  string `json:"latitude"`
					County    string `json:"county"`
					Longitude string `json:"longitude"`
				} `json:"addressInfo"`
				Receiver struct {
					IdCardExt struct {
						BehindImg  string `json:"behindImg"`
						IsVerified bool   `json:"isVerified"`
						FrontImg   string `json:"frontImg"`
						UserName   string `json:"userName"`
					} `json:"idCardExt"`
					ReceiverName   string `json:"receiverName"`
					IdCardNo       string `json:"idCardNo"`
					ReceiverMobile string `json:"receiverMobile"`
				} `json:"receiver"`
				PickUpVid  int    `json:"pickUpVid"`
				PickUpName string `json:"pickUpName"`
			} `json:"receiveInfo"`
			Remark     string `json:"remark"`
			UpdateTime string `json:"updateTime"`
			BuyerInfo  struct {
				BuyerRemark string `json:"buyerRemark"`
				Wid         int    `json:"wid"`
			} `json:"buyerInfo"`
			AutoReceivingTime int64 `json:"autoReceivingTime"`
			CancelTime        int64 `json:"cancelTime"`
			FulfillStatus     int   `json:"fulfillStatus"`
			FulfillNo         int64 `json:"fulfillNo"`
			FulfillItemList   []struct {
				Product struct {
					ItemProducts []struct {
						ItemSkuQuantity int   `json:"itemSkuQuantity"`
						ItemSkuId       int64 `json:"itemSkuId"`
						CombSkuId       int64 `json:"combSkuId"`
						Warehouses      []struct {
							Quantity      int    `json:"quantity"`
							WarehouseId   int    `json:"warehouseId"`
							WarehouseType string `json:"warehouseType"`
							WarehouseName string `json:"warehouseName"`
						} `json:"warehouses"`
						ProductType int `json:"productType"`
					} `json:"itemProducts"`
				} `json:"product"`
				DeliveryNum int `json:"deliveryNum"`
				GoodsId     int `json:"goodsId"`
				OrderItemId int `json:"orderItemId"`
			} `json:"fulfillItemList"`
			IsSplitPackage   int   `json:"isSplitPackage"`
			AutoDeliveryTime int64 `json:"autoDeliveryTime"`
			DeliveryVid      int64 `json:"deliveryVid"`
			FulfillMethod    int   `json:"fulfillMethod"`
		} `json:"fulfillInfoList"`
	} `json:"data"`
}

//微盟退款单详情数据结构
type WeiMengRefundDetailResponse struct {
	Code struct {
		Errcode string `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	} `json:"code"`
	Data struct {
		RightsInfo struct {
			RightsStatusLogs []struct {
				DateTime int64  `json:"dateTime"`
				Type     string `json:"type"`
			} `json:"rightsStatusLogs"`
			MerchantInfo struct {
				Vid            int    `json:"vid"`
				MerchantId     int64  `json:"merchantId"`
				BosName        string `json:"bosName"`
				BosId          int64  `json:"bosId"`
				ProcessVid     int    `json:"processVid"`
				VidName        string `json:"vidName"`
				ProcessVidName string `json:"processVidName"`
			} `json:"merchantInfo"`
			RightsItems []struct {
				GoodsReceiveType int   `json:"goodsReceiveType"`
				ReturnNum        int32 `json:"returnNum"`
				OrderItemId      int64 `json:"orderItemId"`
				RightsAssets     []struct {
					AssetsNum    int     `json:"assetsNum"`
					AssetsType   int     `json:"assetsType"`
					AssetsTarget int     `json:"assetsTarget"`
					AssetsAmount float64 `json:"assetsAmount"`
				} `json:"rightsAssets"`
				RefundDetail struct {
					ApplyAmountInfos []struct {
						Amount float64 `json:"amount"`
						Type   int     `json:"type"`
					} `json:"applyAmountInfos"`
					RefundAmountInfos []struct {
						Amount float64 `json:"amount"`
						Type   int     `json:"type"`
					} `json:"refundAmountInfos"`
					ApplyAmount  float64 `json:"applyAmount"`
					RefundAmount float64 `json:"refundAmount"`
				} `json:"refundDetail"`
				ApplyNum  int `json:"applyNum"`
				GoodsInfo struct {
					GoodsAbilities []struct {
						AbilityType int    `json:"abilityType"`
						AbilityCode string `json:"abilityCode"`
						BizId       int    `json:"bizId"`
					} `json:"goodsAbilities"`
					LabelInfos []struct {
						LabelType string `json:"labelType"`
					} `json:"labelInfos"`
					GoodsId           int64   `json:"goodsId"`
					RightsServiceType int     `json:"rightsServiceType"`
					SkuBarCode        string  `json:"skuBarCode"`
					SkuAttrInfo       string  `json:"skuAttrInfo"`
					GoodsType         int     `json:"goodsType"`
					SubGoodsType      int     `json:"subGoodsType"`
					Price             float64 `json:"price"`
					ImageUrl          string  `json:"imageUrl"`
					BizInfos          []struct {
						SubBizType int    `json:"subBizType"`
						BizType    int    `json:"bizType"`
						BizOrderId string `json:"bizOrderId"`
						BizId      int    `json:"bizId"`
					} `json:"bizInfos"`
					GoodsTitle string  `json:"goodsTitle"`
					GoodsCode  string  `json:"goodsCode"`
					SkuCode    string  `json:"skuCode"`
					SkuId      int     `json:"skuId"`
					SkuNum     float64 `json:"skuNum"`
				} `json:"goodsInfo"`
				RightsItemId int64 `json:"rightsItemId"`
			} `json:"rightsItems"`
			RightsOrder struct {
				RefundType      int    `json:"refundType"`
				RightsStatus    int    `json:"rightsStatus"`
				RightsType      int32  `json:"rightsType"`
				RightsId        int64  `json:"rightsId"`
				CreateTime      int64  `json:"createTime"`
				UpdateTime      int64  `json:"updateTime"`
				Currency        string `json:"currency"`
				RightsCauseType int    `json:"rightsCauseType"`
				RightsSource    int    `json:"rightsSource"`
			} `json:"rightsOrder"`
			OutRightsInfo struct {
				OutRightsNo string `json:"outRightsNo"`
			} `json:"outRightsInfo"`
			BuyerInfo struct {
				Wid            int    `json:"wid"`
				UserNickName   string `json:"userNickName"`
				MemberBenefits []struct {
					BenefitType int `json:"benefitType"`
				} `json:"memberBenefits"`
			} `json:"buyerInfo"`
			OriginOrder struct {
				OrderType        int    `json:"orderType"`
				OrderSource      int    `json:"orderSource"`
				OrderNo          int64  `json:"orderNo"`
				PayType          int    `json:"payType"`
				ChannelType      int    `json:"channelType"`
				OutOrderNo       string `json:"outOrderNo"`
				BizSourceType    int    `json:"bizSourceType"`
				ApplyOrderStatus int    `json:"applyOrderStatus"`
			} `json:"originOrder"`
			RightsAssets []struct {
				AssetsNum    int     `json:"assetsNum"`
				AssetsType   int     `json:"assetsType"`
				AssetsTarget int     `json:"assetsTarget"`
				AssetsAmount float64 `json:"assetsAmount"`
			} `json:"rightsAssets"`
			RefundDetail struct {
				ApplyAmountInfos []struct {
					Amount float64 `json:"amount"`
					Type   int     `json:"type"`
				} `json:"applyAmountInfos"`
				RefundAmountInfos []struct {
					Amount float64 `json:"amount"`
					Type   int     `json:"type"`
				} `json:"refundAmountInfos"`
				ApplyAmount  float64 `json:"applyAmount"`
				RefundAmount float64 `json:"refundAmount"`
			} `json:"refundDetail"`
			RefuseInfo struct {
				RefusedReason string `json:"refusedReason"`
			} `json:"refuseInfo"`
			FlagInfo struct {
				FlagRank    int    `json:"flagRank"`
				FlagContent string `json:"flagContent"`
			} `json:"flagInfo"`
			ReturnOrder struct {
				ReceiverAddress     string `json:"receiverAddress"`
				ReceiverPhone       string `json:"receiverPhone"`
				DeliveryCompanyCode string `json:"deliveryCompanyCode"`
				DeliveryMethod      int    `json:"deliveryMethod"`
				ReceiverName        string `json:"receiverName"`
				DeliveryNo          string `json:"deliveryNo"`
				DeliveryType        int    `json:"deliveryType"`
				DeliveryCompany     string `json:"deliveryCompany"`
				DeliveryStatus      int    `json:"deliveryStatus"`
			} `json:"returnOrder"`
			RightsReason struct {
				ReasonImageUrls []string `json:"reasonImageUrls"`
				RightsRemark    string   `json:"rightsRemark"`
				RightsReason    string   `json:"rightsReason"`
			} `json:"rightsReason"`
			ExchangeOrder struct {
				OrderNo    int64 `json:"orderNo"`
				OrderItems []struct {
					OrderItemId int64 `json:"orderItemId"`
					GoodsInfo   struct {
						GoodsAbilities []struct {
							AbilityType int    `json:"abilityType"`
							AbilityCode string `json:"abilityCode"`
							BizId       int    `json:"bizId"`
						} `json:"goodsAbilities"`
						LabelInfos []struct {
							LabelType string `json:"labelType"`
						} `json:"labelInfos"`
						GoodsId           int64   `json:"goodsId"`
						RightsServiceType int     `json:"rightsServiceType"`
						SkuBarCode        string  `json:"skuBarCode"`
						SkuAttrInfo       string  `json:"skuAttrInfo"`
						GoodsType         int     `json:"goodsType"`
						SubGoodsType      int     `json:"subGoodsType"`
						Price             float64 `json:"price"`
						ImageUrl          string  `json:"imageUrl"`
						BizInfos          []struct {
							SubBizType int    `json:"subBizType"`
							BizType    int    `json:"bizType"`
							BizOrderId string `json:"bizOrderId"`
							BizId      int    `json:"bizId"`
						} `json:"bizInfos"`
						GoodsTitle string  `json:"goodsTitle"`
						GoodsCode  string  `json:"goodsCode"`
						SkuCode    string  `json:"skuCode"`
						SkuId      int     `json:"skuId"`
						SkuNum     float64 `json:"skuNum"`
					} `json:"goodsInfo"`
				} `json:"orderItems"`
			} `json:"exchangeOrder"`
			RefundAccount struct {
				AccountNum     string `json:"accountNum"`
				RefundMethodId int    `json:"refundMethodId"`
				UserName       string `json:"userName"`
				OpeningBank    string `json:"openingBank"`
			} `json:"refundAccount"`
		} `json:"rightsInfo"`
	} `json:"data"`
}

type MsgBody struct {
	GoodsId int64  `json:"goodsId"`
	Vid     string `json:"vid"`
}
