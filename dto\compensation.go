package dto
type Compensation struct {
	//订单号
	OrderId int64 `json:"order_id" form:"order_id" query:"order_id"`
	//赔付金额，单位是元
	Amount string `json:"amount" form:"amount" query:"amount"`
	//赔付原因说明
	Reason string `json:"reason" form:"reason" query:"reason"`
	//赔付凭证，推送图片url列表，多张图片url以英文逗号隔开。
	Pictures string `json:"pictures" form:"pictures" query:"pictures"`
	//赔付时间，为13位毫秒级的时间戳。
	Time int64  `json:"time" form:"time" query:"time"`
	//APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode int64  `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
}