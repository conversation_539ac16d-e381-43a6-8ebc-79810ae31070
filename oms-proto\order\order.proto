syntax = "proto3";

package order;

option go_package = "./oms-proto/order";

service WeiMengOrderService {
  // 新增微盟订单
  rpc OrderAdd(WeiMengAddOrderRequest) returns (AddOrderResponse) {}
  // 微盟订单取消
  rpc OrderCancel(WeiMengOrderCancelRequest) returns (CommonResponse) {}
  // 新增微盟退款单
  rpc OrderRefundAdd(WeiMOrderRefundAddRequest) returns (OrderRefundAddResponse) {}
  // 退款单更新
  rpc OrderRefundUpdate(WeiMOrderRefundAddRequest) returns (OrderRefundAddResponse) {}

  // 新增微盟退款单
  rpc OrderRefundCancel(WeiMengOrderRefundCancelRequest) returns (CommonResponse) {}
}

service OrderService {
  // 保存原始订单
  rpc OriginOrderSave(OriginOrderSaveRequest) returns (CommonResponse) {}
  // 保存原始退款单
  rpc OriginOrderRefundSave(OriginOrderRefundSaveRequest) returns (CommonResponse) {}
}

message OriginOrderSaveRequest {
  string origin_order_sn = 1;
  string order_time = 2;
  string order_json = 3;
  //  订单来源 1阿闻到家,2美团,3饿了么,4京东到家 5物竞天择 6子龙医疗门店 7魔鬼鱼 8:微盟 9:支付宝小程序
  int32 order_source = 4;
}
message OriginOrderRefundSaveRequest {
  string origin_order_sn = 1;
  string origin_refund_sn = 2;
  string order_time = 3;
  string order_json = 4;
  //订单来源 1阿闻到家,2美团,3饿了么,4京东到家 5物竞天择 6子龙医疗门店 7魔鬼鱼 8:微盟 9:支付宝小程序
  int32 order_source = 5;
}
//微盟取消订单请求参数
message WeiMengOrderCancelRequest {
  string origin_order_sn = 1;
}
message WeiMengOrderRefundCancelRequest {
  string refund_sn = 1;
}

//获取单据类
message WeiMengAddOrderRequest {
  // 渠道订单号(推送给oms平台的推送方订单，如阿闻订单号)
  string channel_order_sn = 1;
  // 交易单号（在美团等第三方没有对接oms时 取美团订单号阿闻 就取阿闻的订单号）
  string trade_order_sn = 2;
  // 订单状态：0已取消,1(默认)待审核,2待审核,3待出库 4审核拒绝 5全部出库 6取消
  int32   order_status = 3;
  // 店铺id(财务编码)
  string shop_id = 4;
  // 商户名称
  string shop_name = 5;
  // 发货仓库id
  string delivery_warehouse_code = 6;
  // 客户id 阿闻订单取阿闻的顾客id
  string member_id = 7;
  // 客户名称
  string member_name = 8;
  // 实际支付金额
  int32  pay_total = 9;
  //商品实际支付金额
  int32  goods_total = 10;
  // 总优惠金额(分)
  int32  privilege = 11;
  // 平台惠金额(分)
  int32  platform_privilege = 12;
  // 物流费(分)
  int32  freight = 13;
  //商家运费优惠金额
  int32  freight_privilege = 14;
  // 包装费(分)
  int32  packing_fee = 15;
  // 订单类型1B2C订单(默认) 2:预定订单
  int32   order_type = 16;
  // 配送类型,1快递,2外卖,3自提,4同城送
  int32   delivery_type = 17;
  // 交易类型 1付款发货 2货到付款
  int32   pay_type = 18;
  // 是否是虚拟订单，0否1是
  int32  is_virtual = 19;
  // 下单日期
  string order_time = 20;
  //收件人
  string  receiver_name = 21;
  //收件省
  string  receiver_province = 22;
  //收件市
  string  receiver_city = 23;
  //收件区
  string  receiver_district = 24;
  //收件地址
  string  receiver_address = 25;
  //收件电话
  string  receiver_phone = 26;
  // 收货地址纬度
  float latitude = 27;
  // 收货地址经度
  float longitude = 28;
  //买家留言
  string  buyer_memo = 29;
  // 备注
  string  note = 30;
  //用户下单时间
  string user_submit_time = 31;
  //订单商品
  repeated OrderProduct order_product = 32;
  //订单优惠
  repeated OrderPromotion order_promotion = 33;
  //订单支付
  repeated OrderPay order_pay = 34;
  // vid用于区分门店的标识字段
  string  Vid = 35;
}

message OrderProduct {
  // 货号
  string item_num = 1;
  // 商品原单价
  int32  marking_price = 2;
  // 外部商品id 用于区分相同商品不同记录
  int64  channel_product_id = 3;
  // 优惠后的单价
  int32  pay_price = 4;
  // 优惠前金额 marking_price * number
  int32  total = 5;
  // 实付金额
  int32  payment_total = 6;
  // 优惠金额  优惠前金额-实付金额
  int32  privilege_total = 7;
  // sku实付总金额
  int32  sku_pay_total = 8;
  // 数量
  int32  number = 9;
  // 是否赠品 0 否 1是
  int32  is_free = 10;
}

message OrderPay {
  // 交易类型 1付款发货 2货到付款
  int32 pay_type = 1;
  // 支付单号
  string pay_sn = 2;
  // 支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
  int32 pay_mode = 3;
  //支付状态 1未支付 2已支付
  int32  pay_status = 4;
  // 支付时间
  int64 pay_time = 5;
  // 实际支付金额
  int32 pay_amount = 6;
  //币种 1人民币 2美元
  int32 currency = 7;
}
message OrderPromotion {
  // 优惠活动id
  int32 promotion_id = 1;
  // 活动编码
  string promotion_code = 2;
  // 优惠活动类型
  int32 promotion_type = 3;
  // 活动名称
  string promotion_title = 4;
  // 商家优惠
  int32 poi_charge = 5;
  // 平台优惠
  int32 pt_charge = 6;
  //总优惠金额(商家加平台总和)
  int32 promotion_fee = 7;
  //开始时间
  string start_time = 8;
  // 结束时间
  int32 end_time = 9;
}

message CommonResponse {
  // 响应码
  int32 code = 1;
  // 返回信息
  string message = 2;
}

message AddOrderResponse {
  // 响应码
  int32 code = 1;
  //oms订单号
  string order_sn = 2;
  // 返回信息
  string message = 3;
}


message WeiMOrderRefundAddRequest{
//    售后单号。可以通过商家后台售后列表或 weimob_shop/rights/list/search 接口获取该 ID。
  int64 rightsId = 1;

//    订单编号。可以通过 weimob_shop/order/list/search 接口获取该 ID。
  int64 orderNo = 2;

//    售后通知信息
  RightsInfo  rightsInfo = 3;

  // 第三方的推送消息
  string pushMsh = 4;

}

message RightsInfo {
//    销售组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
    int64 vid = 1;
//    服务组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
    int64 processVid = 2;
//售后类型。支持的类型包括：1-退货退款；2-退款；5-退换货。
  int64 rightsType = 3;

//    售后状态。支持的类型包括：1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中。
  int64 rightsStatus = 4;

//    售后方式。支持的类型包括：1-买家申请售后；2-商家取消订单；5-收银台退款；6-外部平台商家售后；7-优惠券到期自动退款；8-系统自动售后；9-商家发起售后；10-付费券发券失败发起售后；20-买家取消订单。
  int64 rightsCauseType = 5;
//    售后来源。支持的类型包括：0-系统内部订单；100-历史导入订单；101-友朋；102-芸智；103-乐美；104-博申；201-分销市场供货商；401-全渠道导入订单。
  int64 rightsSource = 6;
//    退款类型。支持的类型包括：1-线上退款；2-线下退款；99-无需退款。
  int64 refundType = 7;
}


message WeiMengOrderRefundAddRequest {
  // 渠道退款号
  string channel_refund_sn = 1;
  //下单时间
  string order_time = 2;
  //正向订单号
  string channel_order_sn = 5;
  // 退款类型 1退款 2退货退款
  int32 refund_type = 7;
  // 退款金额
  int32 refund_amount = 9;
  // 退款原因
  string refund_reason = 10;
  // 备注
  string note = 11;
  // 退换货方式
  int32 delivery_type = 12;
  //是否全部退款 是 true ; 否 false
  OrderRefundProduct order_refund_product = 13;
}
message OrderRefundProduct {
  // 退款商品货号
  string item_num = 1;
  // 是否赠品 0 否 1是
  int32 is_free = 2;
  // 退款数量
  int32 refund_number = 3;
  // 退款价格
  int32 refund_price = 4;
  // 退款金额
  int32 refund_amount = 5;
  //退款商品记录id
  int64 item_id = 6;
}

message OrderRefundAddResponse{
  // 响应码
  int32 code = 1;
  //oms退款单号
  string refund_sn = 2;
  // 返回信息
  string message = 3;
}


