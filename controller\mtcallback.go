package controller

import (
	"encoding/json"
	"external-ui/pkg/app"
	"external-ui/pkg/code"
	"external-ui/proto/pc"
	"external-ui/utils"
	"fmt"
	"io/ioutil"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/limitedlee/microservice/common/logger"

	"external-ui/dto"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"external-ui/services"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"golang.org/x/net/context"
	"google.golang.org/grpc/metadata"
)

var ReturnMsg = `{"data":"ok"}`

func PaidOrders(c echo.Context) error {
	mtOrderDetailModel := new(dto.OrderDetail)
	if err := c.Bind(mtOrderDetailModel); err != nil {
		glog.Error("推送美团已支付订单参数绑定失败")
		return c.String(400, "")
	}
	dataStr := kit.JsonEncode(mtOrderDetailModel)
	glog.Info("推送美团已支付订单接收参数json：" + dataStr)
	var AppPoiCode string

	AppPoiCode, err := url.QueryUnescape(mtOrderDetailModel.AppPoiCode)
	if err != nil {
		glog.Error("美团订单解码AppPoiCode出错", err)
		if mtOrderDetailModel.AppPoiCode != "" {
			mtOrderDetailModel.AppPoiCode = AppPoiCode
		}
	} else {
		mtOrderDetailModel.AppPoiCode = AppPoiCode
	}

	if len(mtOrderDetailModel.AppPoiCode) == 0 {
		//美团会发送检测代码必须返回200
		glog.Info("推送订单回调：", mtOrderDetailModel.AppPoiCode)
		return c.String(200, ReturnMsg)
	}

	redisConn := utils.GetRedisConn()

	lockCard := "lock:mtOrderCreate" + fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView)
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Minute).Val()
	if !lockRes {
		glog.Info("创建订单OrderCreate1 lock:mtOrderCreate：" + fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView) + "存在：")
		return c.String(400, "")
	}
	defer redisConn.Del(lockCard)

	//判断第三方订单号是否存在
	ok := oldOrderSnIsExist(strconv.FormatInt(mtOrderDetailModel.WmOrderIdView, 10))
	if ok {
		return c.String(200, ReturnMsg)
	}
	orderid := fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView)
	storeMasterId, retCode := app.GetStoreMasterId(mtOrderDetailModel.AppId, app.Channel_JDDJ)
	if retCode != code.Success {
		glog.Error("PaidOrders,", "GetStoreMasterId failed,", mtOrderDetailModel.AppId, ",", app.Channel_JDDJ)
	}

	pushOrderInitialData(orderid, 2, dataStr)

	if mtOrderDetailModel.IncmpCode == 0 && (mtOrderDetailModel.Status == 1 || mtOrderDetailModel.Status == 2) {
		model := new(oc.MtAddOrderRequest)

		model.OrderSn = orderid
		model.OrderStatus = 20
		model.OrderStatusChild = 20101
		model.ShopId = mtOrderDetailModel.AppPoiCode
		wm_poi_name, _ := url.QueryUnescape(mtOrderDetailModel.WmPoiName)
		model.ShopName = wm_poi_name
		recipient_name, _ := url.QueryUnescape(mtOrderDetailModel.RecipientName)
		model.ReceiverName = recipient_name

		if mtOrderDetailModel.PickType == 0 {
			recipient_address, _ := url.QueryUnescape(mtOrderDetailModel.RecipientAddress)
			recipient_address1 := strings.Split(recipient_address, "@#")[0]
			recipient_address2 := strings.Split(recipient_address, "@#")[1]
			r, err := regexp.Compile(".+?(省|市|自治区|自治州|街道|县|区)")
			if err != nil {
				glog.Info("推送美团已支付订单从地址转换省市县失败:", orderid+recipient_address)
				return c.String(500, "")
			}

			array := r.FindAllString(recipient_address2, -1)
			for _, v := range array {
				if strings.Contains(v, "省") ||
					strings.Contains(v, "自治区") {
					model.ReceiverState = v
				}
				if strings.Contains(v, "市") ||
					strings.Contains(v, "自治州") {
					model.ReceiverCity = v
				}
				if strings.Contains(v, "区") ||
					strings.Contains(v, "街道") ||
					strings.Contains(v, "县") {
					model.ReceiverDistrict = v
				}
			}
			model.ReceiverAddress = recipient_address2 + " " + recipient_address1
		}

		model.ReceiverPhone = mtOrderDetailModel.RecipientPhone
		intPayType := mtOrderDetailModel.PayType
		//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
		if intPayType == 2 {
			model.PayType = "Cod"
		} else {
			model.PayType = "NoCod"
		}
		model.ReceiverMobile = mtOrderDetailModel.RecipientPhone
		model.GjpStatus = "Payed"
		floatTotal := mtOrderDetailModel.Total //订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
		model.Total = int32(kit.YuanToFen(floatTotal))
		floatOriginalPrice := mtOrderDetailModel.OriginalPrice                                 //订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
		model.Privilege = int32(kit.YuanToFen(floatOriginalPrice) - kit.YuanToFen(floatTotal)) //总优惠金额
		model.PackingCost = int32(kit.YuanToFen(cast.ToFloat64(mtOrderDetailModel.PackageBagMoneyYuan)))
		floatShippingFee := mtOrderDetailModel.ShippingFee //配送费
		model.Freight = int32(kit.YuanToFen(floatShippingFee))
		model.IsPay = 1
		model.CreateTime = kit.GetTimeNow(time.Unix(mtOrderDetailModel.Ctime, 0))
		model.Latitude = float64(mtOrderDetailModel.Latitude)   //纬度
		model.Longitude = float64(mtOrderDetailModel.Longitude) //经度
		model.PickupCode = fmt.Sprintf("%d", mtOrderDetailModel.DaySeq)
		intTotalWeight := mtOrderDetailModel.TotalWeight
		model.LogisticsCode = mtOrderDetailModel.LogisticsCode
		model.TotalWeight = int32(intTotalWeight) //订单商品总重量 单位为克/g。
		intEstimateArrivalTime := mtOrderDetailModel.EstimateArrivalTime
		if intEstimateArrivalTime == 0 {
			model.ExpectedTime = ""
		} else {
			model.ExpectedTime = kit.GetTimeNow(time.Unix(intEstimateArrivalTime, 0))
		}
		//订单类型
		intDeliveryTime := mtOrderDetailModel.DeliveryTime
		if intDeliveryTime == 0 {
			model.OrderType = 1
		} else {
			model.OrderType = 2
		}
		model.PayTime = kit.GetTimeNow()
		model.PaySn = "" //美团没有这个信息暂时写空
		//var invoiceData map[string]string
		invoiceData := make(map[string]string)
		invoiceData["发票抬头"] = mtOrderDetailModel.InvoiceTitle
		invoiceData["纳税人识别号"] = mtOrderDetailModel.TaxpayerId
		model.Invoice = kit.JsonEncode(invoiceData)
		caution, _ := url.QueryUnescape(mtOrderDetailModel.Caution)
		model.BuyerMemo = caution
		intPickType := mtOrderDetailModel.PickType
		if intPickType == 0 {
			model.DeliveryType = 2
		} else if intPickType == 1 {
			model.DeliveryType = 3
		} else {
			model.DeliveryType = 1
		}
		model.DeliveryRemark = ""
		extras, _ := url.QueryUnescape(mtOrderDetailModel.Extras)
		model.Extras = extras
		//新增优惠信息对象
		model.OrderPromotion = DiscountExtra(extras)

		//取平台服务费
		poi_receive_detail_yuan, _ := url.QueryUnescape(mtOrderDetailModel.PoiReceiveDetailYuan)
		var foodShareFeeCharge = dto.PoiReceiveDetailYuan{}
		x := (*[2]uintptr)(unsafe.Pointer(&poi_receive_detail_yuan))
		h := [3]uintptr{x[0], x[1], x[1]}
		err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &foodShareFeeCharge)
		if err != nil {
			glog.Error("推送美团已支付订单解码poi_receive_detail_yuan出错" + orderid + err.Error())
		}
		//服务费
		model.ServiceCharge = int32(kit.YuanToFen(cast.ToFloat64(foodShareFeeCharge.FoodShareFeeChargeByPoi))) //平台服务费

		reconciliationExtras := new(dto.ReconciliationExtras)
		err = json.Unmarshal([]byte(foodShareFeeCharge.ReconciliationExtras), reconciliationExtras)
		if err != nil {
			glog.Error("推送美团已支付订单解码reconciliationExtras出错" + orderid + err.Error())
		}
		//履约费
		model.ContractFee = int32(kit.YuanToFen(cast.ToFloat64(reconciliationExtras.PerformanceServiceFee)))

		var listProduct []dto.Detail
		detail, _ := url.QueryUnescape(mtOrderDetailModel.Detail)
		x = (*[2]uintptr)(unsafe.Pointer(&detail))
		h = [3]uintptr{x[0], x[1], x[1]}
		err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listProduct)
		if err != nil {
			glog.Error("推送美团已支付订单解码商品信息出错" + orderid + err.Error())
			return c.String(500, "")
		}

		//计算单个产品优惠金额
		skuBenefitDetailMap := make(map[string]dto.SkuBenefitDetail)
		sku_benefit_detail := mtOrderDetailModel.SkuBenefitDetail
		if len(sku_benefit_detail) > 0 {
			var listSkuBenefitDetail []dto.SkuBenefitDetail
			sku_benefit_detail, _ = url.QueryUnescape(sku_benefit_detail)
			x = (*[2]uintptr)(unsafe.Pointer(&sku_benefit_detail))
			h = [3]uintptr{x[0], x[1], x[1]}
			err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listSkuBenefitDetail)
			if err != nil {
				glog.Error("推送美团已支付订单" + orderid + err.Error())
				return c.String(500, "")
			}
			if len(listSkuBenefitDetail) > 0 {
				for _, e := range listSkuBenefitDetail {
					skuBenefitDetailMap[e.SkuId] = e
				}
			}
		}

		//订单商品
		var goodsTotal int32 = 0

		for _, item := range listProduct {
			// 如果美团商品sku_id为空，则不处理
			if strings.TrimSpace(item.SkuID) == "" {
				glog.Error("推送美团已支付订单" + orderid + ",商品  " + item.FoodName + " 对应sku_id为空")
				return c.String(200, ReturnMsg)
			}
		}

		// 商品是否已经添加过，针对多个sku同时存在的情况，只添加一次
		productAdded := make(map[string]bool)

		if len(listProduct) > 0 {
			for _, item := range listProduct {
				if productAdded[item.SkuID] {
					continue
				}
				var product oc.OrderProductModel
				product.OrderId = ""
				product.ProductType = 1
				product.Sku = item.SkuID
				product.ProductId = item.AppFoodCode
				product.ProductName = item.FoodName
				product.Price = int32(kit.YuanToFen(item.Price))
				product.MarkingPrice = int32(kit.YuanToFen(item.Price))
				product.Number = item.Quantity
				product.Specs = item.Spec
				product.Privilege = 0                                                  //商家优惠金额
				product.PrivilegePt = 0                                                //平台优惠金额
				product.PrivilegeTotal = 0                                             //总优惠金额
				product.PayPrice = int32(kit.YuanToFen(item.Price))                    //实际支付单价（美团）
				product.SkuPayTotal = item.Quantity * int32(kit.YuanToFen(item.Price)) //sku实际支付金额

				// 存在多个商品的促销场景，商品合并
				if benefit, ok := skuBenefitDetailMap[item.SkuID]; ok {
					product.Privilege = int32(kit.YuanToFen(benefit.TotalPoiCharge))
					product.PrivilegePt = int32(kit.YuanToFen(benefit.TotalMtCharge))
					product.PrivilegeTotal = int32(kit.YuanToFen(benefit.TotalReducePrice))

					product.PayPrice = int32(kit.YuanToFen(benefit.ActivityPrice))
					product.MarkingPrice = int32(kit.YuanToFen(benefit.OriginPrice))
					product.Number = benefit.Count
					product.SkuPayTotal = int32(kit.YuanToFen(benefit.TotalActivityPrice) + kit.YuanToFen(benefit.TotalMtCharge)) //平台优惠也要算进去

					// 标记商品已经合并过
					productAdded[item.SkuID] = true
				}

				product.PaymentTotal = (product.MarkingPrice * product.Number) - product.PrivilegeTotal
				model.OrderProductModel = append(model.OrderProductModel, &product)
				goodsTotal = goodsTotal + (product.MarkingPrice * product.Number)
			}
		}
		model.GoodsTotal = goodsTotal //商品总金额（未加运费，不加包装费，不减优惠金额）

		//支付信息
		var payInfo oc.OrderPayInfoModel
		payInfo.OrderSn = model.OrderSn
		payInfo.PaySn = ""
		payInfo.PayMode = 3
		payInfo.PayAmount = model.Total
		payInfo.PayTime = model.PayTime
		model.PayInfo = append(model.PayInfo, &payInfo)

		var logbuild strings.Builder
		logbuild.WriteString("推送美团已支付订单参数：" + kit.JsonEncode(model)) // 请求参数

		model.AppChannel = storeMasterId
		//加入context渠道信息
		grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 2, UserAgent: 6, AppChannel: int(storeMasterId)}}
		ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

		ocClient := oc.GetOrderServiceClient()
		grpcRes, err := ocClient.Cart.MtSubmitOrderData(ctx, model)
		if err != nil {
			logbuild.WriteString("推送美团已支付订单处理结果：" + err.Error())
			glog.Error(logbuild.String())
			return c.JSON(500, grpcRes)
		}
		if grpcRes.Code != 200 {
			logbuild.WriteString("推送美团已支付订单处理结果2" + grpcRes.Error)
			glog.Error(logbuild.String())
			glog.Error("推送美团已支付订单调用接口返回: ", orderid, kit.JsonEncode(grpcRes)) // 记录日志
			return c.JSON(400, grpcRes)
		}
		logbuild.WriteString("推送美团已支付订单处理结果：成功")          // 处理结果
		glog.Info("推送美团已支付订单调用接口参数:", logbuild.String())  // 记录日志
		glog.Info("推送美团已支付订单调用接口结果1: ", orderid, grpcRes) // 记录日志
		return c.String(200, ReturnMsg)
	} else {
		//利用查询订单详情接口重新推10次
		result := "fail"
		for i := 0; i <= 10; i++ {
			result = OrderRePush(fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView), storeMasterId)
			if result == "ok" {
				break
			}
		}

		if result == "ok" {
			return c.String(200, ReturnMsg)
		} else {
			return c.String(400, "")
		}
	}
}

// 利用查询订单详情接口重新推
func OrderRePush(orderId string, appChannel int32) string {
	etClient := et.GetExternalClient()
	defer etClient.Close()

	orderDetaiModel := new(et.MtOrderDetailRequest)
	orderDetaiModel.OrderId = orderId
	orderDetaiModel.IsMtLogistics = "1"
	orderDetaiModel.StoreMasterId = appChannel
	var logbuild strings.Builder
	logbuild.WriteString("利用查询订单详情接口重新推参数：" + kit.JsonEncode(orderDetaiModel)) // 请求参数
	//todo tp mt
	grpcResEt, err := etClient.MtOrder.GetMtOrderDetail(etClient.Ctx, orderDetaiModel)
	if err != nil {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return "fail"
	}
	if grpcResEt.Code != 200 {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + grpcResEt.Error)
		glog.Error(logbuild.String())
		return "fail"
	}

	var mtOrderDetailModel dto.OrderDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.MTOrderSDetail), &mtOrderDetailModel)
	if err != nil {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return "fail"
	}

	if mtOrderDetailModel.IncmpCode == 0 && (mtOrderDetailModel.Status == 1 || mtOrderDetailModel.Status == 2) {
		model := new(oc.MtAddOrderRequest)
		orderid := fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView)
		model.OrderSn = orderid
		model.OrderStatus = 20
		model.OrderStatusChild = 20101
		model.ShopId = mtOrderDetailModel.AppPoiCode
		wm_poi_name, _ := url.QueryUnescape(mtOrderDetailModel.WmPoiName)
		model.ShopName = wm_poi_name
		recipient_name, _ := url.QueryUnescape(mtOrderDetailModel.RecipientName)
		model.ReceiverName = recipient_name
		if mtOrderDetailModel.PickType == 0 {
			recipient_address, _ := url.QueryUnescape(mtOrderDetailModel.RecipientAddress)
			recipient_address1 := strings.Split(recipient_address, "@#")[0]
			recipient_address2 := strings.Split(recipient_address, "@#")[1]
			var r *regexp.Regexp
			r, err = regexp.Compile(".+?(省|市|自治区|自治州|县|区)")
			if err != nil {
				glog.Info("利用查询订单详情接口重新推,从地址转换省市县失败:", orderid+recipient_address)
				return "fail"
			}
			array := r.FindAllString(recipient_address2, -1)
			for _, v := range array {
				if strings.Contains(v, "省") ||
					strings.Contains(v, "自治区") {
					model.ReceiverState = v
				}
				if strings.Contains(v, "市") ||
					strings.Contains(v, "自治州") {
					model.ReceiverCity = v
				}
				if strings.Contains(v, "区") ||
					strings.Contains(v, "街道") ||
					strings.Contains(v, "县") {
					model.ReceiverDistrict = v
				}
			}
			model.ReceiverAddress = recipient_address2 + " " + recipient_address1
		}

		model.ReceiverPhone = mtOrderDetailModel.RecipientPhone
		intPayType := mtOrderDetailModel.PayType
		//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
		if intPayType == 2 {
			model.PayType = "Cod"
		} else {
			model.PayType = "NoCod"
		}
		model.ReceiverMobile = mtOrderDetailModel.RecipientPhone
		model.GjpStatus = "Payed"
		model.Total = int32(kit.YuanToFen(mtOrderDetailModel.Total))                          //订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
		floatOriginalPrice := mtOrderDetailModel.OriginalPrice                                //订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
		model.Privilege = int32(kit.YuanToFen(floatOriginalPrice - mtOrderDetailModel.Total)) //总优惠金额
		model.PackingCost = int32(kit.YuanToFen(cast.ToFloat64(mtOrderDetailModel.PackageBagMoneyYuan)))
		floatShippingFee := mtOrderDetailModel.ShippingFee //配送费
		model.Freight = int32(kit.YuanToFen(floatShippingFee))
		model.IsPay = 1
		ct := time.Unix(mtOrderDetailModel.Ctime, 0)
		model.CreateTime = kit.GetTimeNow(ct)
		model.Latitude = float64(mtOrderDetailModel.Latitude)   //纬度
		model.Longitude = float64(mtOrderDetailModel.Longitude) //经度
		model.PickupCode = fmt.Sprintf("%d", mtOrderDetailModel.DaySeq)
		intTotalWeight := mtOrderDetailModel.TotalWeight
		model.TotalWeight = int32(intTotalWeight) //订单商品总重量 单位为克/g。
		model.LogisticsCode = mtOrderDetailModel.LogisticsCode
		intEstimateArrivalTime := mtOrderDetailModel.EstimateArrivalTime
		if intEstimateArrivalTime != 0 {
			model.ExpectedTime = kit.GetTimeNow(time.Unix(intEstimateArrivalTime, 0))
		}
		//订单类型
		intDeliveryTime := mtOrderDetailModel.DeliveryTime
		if intDeliveryTime == 0 {
			model.OrderType = 1
		} else {
			model.OrderType = 2
		}
		model.CreateTime = kit.GetTimeNow(ct)
		model.PayTime = kit.GetTimeNow()
		model.PaySn = "" //美团没有这个信息暂时写空
		var invoiceData map[string]string
		invoiceData = make(map[string]string)
		invoiceData["发票抬头"] = mtOrderDetailModel.InvoiceTitle
		invoiceData["纳税人识别号"] = mtOrderDetailModel.TaxpayerId
		model.Invoice = kit.JsonEncode(invoiceData)
		caution, _ := url.QueryUnescape(mtOrderDetailModel.Caution)
		model.BuyerMemo = caution
		intPickType := mtOrderDetailModel.PickType
		//1快递 2外卖 3自提
		if intPickType == 0 {
			model.DeliveryType = 2
		} else if intPickType == 1 {
			model.DeliveryType = 3
		} else {
			model.DeliveryType = 1
		}
		extras, _ := url.QueryUnescape(mtOrderDetailModel.Extras)
		model.Extras = extras
		//新增优惠信息对象
		model.OrderPromotion = DiscountExtra(extras)

		//取平台服务费
		poi_receive_detail_yuan, _ := url.QueryUnescape(mtOrderDetailModel.PoiReceiveDetailYuan)
		var foodShareFeeCharge = dto.PoiReceiveDetailYuan{}
		x := (*[2]uintptr)(unsafe.Pointer(&poi_receive_detail_yuan))
		h := [3]uintptr{x[0], x[1], x[1]}
		err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &foodShareFeeCharge)
		if err != nil {
			glog.Error("OrderRePush-推送美团已支付订单解码poi_receive_detail_yuan出错" + orderid + err.Error())
		}
		model.ServiceCharge = int32(kit.YuanToFen(cast.ToFloat64(foodShareFeeCharge.FoodShareFeeChargeByPoi))) //平台服务费

		reconciliationExtras := new(dto.ReconciliationExtras)
		err = json.Unmarshal([]byte(foodShareFeeCharge.ReconciliationExtras), reconciliationExtras)
		if err != nil {
			glog.Error("OrderRePush-推送美团已支付订单解码reconciliationExtras出错" + orderid + err.Error())
		}
		//履约费
		model.ContractFee = int32(kit.YuanToFen(cast.ToFloat64(reconciliationExtras.PerformanceServiceFee)))

		var listProduct []dto.Detail
		detail, _ := url.QueryUnescape(mtOrderDetailModel.Detail)
		x = (*[2]uintptr)(unsafe.Pointer(&detail))
		h = [3]uintptr{x[0], x[1], x[1]}
		err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listProduct)
		if err != nil {
			glog.Error("利用查询订单详情接口重新推美团已支付订单 " + orderid + err.Error())
			return "fail"
		}

		//计算单个产品优惠金额
		skuBenefitDetailMap := make(map[string]dto.SkuBenefitDetail)
		sku_benefit_detail := mtOrderDetailModel.SkuBenefitDetail
		if len(sku_benefit_detail) > 0 {
			var listSkuBenefitDetail []dto.SkuBenefitDetail
			sku_benefit_detail, _ = url.QueryUnescape(sku_benefit_detail)
			x = (*[2]uintptr)(unsafe.Pointer(&sku_benefit_detail))
			h = [3]uintptr{x[0], x[1], x[1]}
			err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listSkuBenefitDetail)
			if err != nil {
				glog.Error("利用查询订单详情接口重新推美团已支付订单 " + orderid + err.Error())
				return "fail"
			}
			if len(listSkuBenefitDetail) > 0 {
				for _, e := range listSkuBenefitDetail {
					skuBenefitDetailMap[e.SkuId] = e
				}
			}
		}

		//订单商品
		var goodsTotal int32 = 0
		// 商品是否已经添加过，针对多个sku同时存在的情况，只添加一次
		productAdded := make(map[string]bool)

		if len(listProduct) > 0 {
			for _, item := range listProduct {
				if productAdded[item.SkuID] {
					continue
				}
				var product oc.OrderProductModel
				product.OrderId = ""
				product.ProductType = 1
				product.Sku = item.SkuID
				product.ProductId = item.AppFoodCode
				product.ProductName = item.FoodName
				product.Price = int32(kit.YuanToFen(item.Price))
				product.MarkingPrice = int32(kit.YuanToFen(item.Price))
				product.Number = item.Quantity
				product.Specs = item.Spec
				product.Privilege = 0                                                  //商家优惠金额
				product.PrivilegePt = 0                                                //平台优惠金额
				product.PrivilegeTotal = 0                                             //总优惠金额
				product.PayPrice = int32(kit.YuanToFen(item.Price))                    //实际支付单价（美团）
				product.SkuPayTotal = item.Quantity * int32(kit.YuanToFen(item.Price)) //sku实际支付金额

				// 存在多个商品的促销场景，商品合并
				if benefit, ok := skuBenefitDetailMap[item.SkuID]; ok {
					product.Privilege = int32(kit.YuanToFen(benefit.TotalPoiCharge))
					product.PrivilegePt = int32(kit.YuanToFen(benefit.TotalMtCharge))
					product.PrivilegeTotal = int32(kit.YuanToFen(benefit.TotalReducePrice))

					product.PayPrice = int32(kit.YuanToFen(benefit.ActivityPrice))
					product.MarkingPrice = int32(kit.YuanToFen(benefit.OriginPrice))
					product.Number = benefit.Count
					product.SkuPayTotal = int32(kit.YuanToFen(benefit.TotalActivityPrice) + kit.YuanToFen(benefit.TotalMtCharge)) //平台优惠也要算进去

					// 标记商品已经合并过
					productAdded[item.SkuID] = true
				}

				product.PaymentTotal = (product.MarkingPrice * product.Number) - product.PrivilegeTotal
				model.OrderProductModel = append(model.OrderProductModel, &product)
				goodsTotal = goodsTotal + (product.MarkingPrice * product.Number)
			}
		}

		model.GoodsTotal = goodsTotal //商品总金额（未加运费，不加包装费，不减优惠金额）

		//支付信息
		var payInfo oc.OrderPayInfoModel
		payInfo.OrderSn = model.OrderSn
		payInfo.PayMode = 3
		payInfo.PayAmount = model.Total
		payInfo.PayTime = model.PayTime
		model.PayInfo = append(model.PayInfo, &payInfo)

		logbuild.WriteString("利用查询订单详情接口重新推订单中心参数：" + kit.JsonEncode(model)) // 请求参数

		//加入context渠道信息
		grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 2, UserAgent: 6, AppChannel: int(appChannel)}}
		ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

		model.AppChannel = appChannel
		ocClient := oc.GetOrderServiceClient()
		var grpcRes *oc.MtAddOrderResponse
		grpcRes, err = ocClient.Cart.MtSubmitOrderData(ctx, model)
		if err != nil {
			logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果：" + err.Error())
			glog.Error(logbuild.String())
			return "fail"
		}
		if grpcRes.Code != 200 {
			logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果2" + grpcRes.Error)
			glog.Error(logbuild.String())
			glog.Error("利用查询订单详情接口重新推美团已支付订单调用接口返回: ", orderid, grpcRes) // 记录日志
			return "fail"
		}
		logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果：成功")          // 处理结果
		glog.Info("利用查询订单详情接口重新推美团已支付订单调用接口参数:", logbuild.String())  // 记录日志
		glog.Info("利用查询订单详情接口重新推美团已支付订单调用接口结果1: ", orderid, grpcRes) // 记录日志

		logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果：成功") // 处理结果
		glog.Info(logbuild.String())                        // 记录日志
		return "ok"
	} else {
		return "fail"
	}

}

func SynchronizeShopStatus(c echo.Context) error {
	model := new(ShopStatusRequest)

	if err := c.Bind(model); err != nil {
		return c.JSON(200, ReturnMsg)
	}

	return c.String(200, ReturnMsg)
}

// 推送已确认订单
func OrderConfirmCallback(c echo.Context) error {
	order_id := c.FormValue("wm_order_id_view")
	status := c.FormValue("status")

	if len(order_id) == 0 {
		//美团会发送检测代码必须返回200
		return c.String(200, ReturnMsg)
	}

	model := new(oc.MtAcceptOrderRequest)
	model.OrderSn = order_id
	model.Status = status
	var logbuild strings.Builder
	logbuild.WriteString("推送美团已确认订单接收美团参数：" + kit.JsonEncode(model)) // 请求参数

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.RPC.MtAcceptOrder(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		logbuild.WriteString("推送美团已确认订单处理结果：" + err.Error())
		glog.Error(logbuild.String()) // 记录日志
		return c.JSON(500, grpcRes)
	}
	if grpcRes.Code != 200 {
		logbuild.WriteString("推送美团已确认订单处理结果：" + grpcRes.Error)
		glog.Info(logbuild.String()) // 记录日志
		return c.JSON(400, grpcRes)
	}
	logbuild.WriteString("推送美团已确认订单处理结果：成功") // 处理结果
	glog.Info(logbuild.String())             // 记录日志
	return c.String(200, ReturnMsg)
}

// 推送已完成订单
func OrderCompletedCallback(c echo.Context) error {
	order_id := c.FormValue("wm_order_id_view")
	if len(order_id) == 0 {
		glog.Info("已完成订单推送回调", order_id)
		return c.String(200, ReturnMsg)
	}
	model := new(oc.AccomplishOrderRequest)
	model.OrderSn = order_id
	model.ConfirmTime = kit.GetTimeNow()

	var logbuild strings.Builder
	logbuild.WriteString("推送美团已完成订单接收美团参数：" + kit.JsonEncode(model)) // 请求参数

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.RPC.AccomplishOrder(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		logbuild.WriteString("推送美团已完成订单处理结果：" + err.Error())
		glog.Error(logbuild.String()) // 记录日志
		return c.JSON(500, grpcRes)
	}
	if grpcRes.Code != 200 {
		logbuild.WriteString("推送美团已完成订单处理结果：" + grpcRes.Error)
		glog.Error(logbuild.String()) // 记录日志
		return c.JSON(400, grpcRes)
	}
	logbuild.WriteString("推送美团已完成订单订单处理结果：成功") // 处理结果
	glog.Info(logbuild.String())               // 记录日志
	return c.String(200, ReturnMsg)
}

type ShopStatusRequest struct {
	Timestamp   int64  `json:"timestamp" form:"timestamp" query:"timestamp"`
	AppId       int32  `json:"app_id" form:"app_id" query:"app_id"`
	Sig         string `json:"sig" form:"sig" query:"sig"`
	AppPoiCode  string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	PoiStatus   int32  `json:"poi_status" form:"poi_status" query:"poi_status"`
	Reason      string `json:"reason" form:"reason" query:"reason"`
	OperateTime int64  `json:"operate_time" form:"operate_time" query:"operate_time"`
	OperateUser string `json:"operate_user" form:"operate_user" query:"operate_user"`
}

// callback lei  strat
// /APP方URL 推送用户或客服取消订单（必接）
func SendOrderCencelHandle(c echo.Context) error {
	param := new(dto.CancelParam)
	var requestParam dto.SendOrderCancel
	var logbuild strings.Builder

	if err := c.Bind(param); err != nil {
		glog.Info("SendOrderCencelHandle参数解析失败", err.Error())
		return c.String(200, "参数解析失败")
	}
	//Order_id, err := strconv.ParseInt(c.QueryParam("order_id"), 10, 64)
	/*if err == nil {
		requestParam.Order_id = Order_id // 订单ID
	}*/
	requestParam.Order_id = param.OrderId
	if param.OrderId == 0 {
		glog.Info("已完成订单推送回调", param.OrderId)
		return c.String(200, ReturnMsg)
	}
	//reason, _ := url.QueryUnescape(c.QueryParam("reason"))
	requestParam.Reason, _ = url.QueryUnescape(param.Reason) // 取消原因
	/*Reason_code, err := strconv.Atoi(c.QueryParam("reason_code"))
	if err == nil {
		requestParam.Reason_code = Reason_code // 取消码

	}*/
	requestParam.Reason_code = param.ReasonCode
	logbuild.WriteString("【推送用户或客服取消订单】接收美团参数：" + kit.JsonEncode(requestParam)) // 请求参数
	service := services.CallBackHandleService{}
	err := service.OrderCancelHandle(requestParam)
	if err != nil {
		return c.String(400, err.Error())
	}

	logbuild.WriteString("处理结果：成功") // 处理结果
	glog.Info(logbuild.String())    // 记录日志
	return c.String(200, ReturnMsg)
}

// / 推送催单消息（必接）
func ReminderCallback(c echo.Context) error {
	var requestParam dto.ReminderRequest
	var logbuild strings.Builder
	order_id, err := strconv.ParseInt(c.FormValue("order_id"), 10, 64)
	if err == nil {
		requestParam.Order_id = order_id
	}
	status, err := strconv.Atoi(c.FormValue("status"))
	if err == nil {
		// 订单状态推送订单当前的状态。订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消
		requestParam.Status = status
	}
	// 订单配送方式，商家可在开放平台的【附录】文档中对照查看不同logistics_code对应的描述，
	// 如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等。 如商家想了解自己门店的配送方式以及如何区分等情况，请咨询美团品牌经理
	requestParam.Logistics_code = c.FormValue("logistics_code")
	// 美团配送订单状态code，目前美团配送状态值有：
	// 0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。
	logistics_status, err := strconv.Atoi(c.FormValue("logistics_status"))
	if err == nil {
		requestParam.Logistics_status = logistics_status
	}
	// 用户催单id，用户发起1次催单后：(1)如商家未回复催单且商品一直未送达，用户每隔10分钟可再发起一次催单；每次催单的remind_id不相同。
	// (2)如商家回复过催单，则用户想再次催单时，仅支持用户通过拨打商家电话联系的方式。
	remind_id, err := strconv.ParseInt(c.FormValue("order_id"), 10, 64)
	if err == nil {
		requestParam.Remind_id = remind_id
	}
	// 用户催单时间，为10位秒级时间戳。如用户发起了多次催单，此字段信息会推送多个催单时间。
	requestParam.Remind_time = c.FormValue("remind_time")
	// 催单消息发送时间，为13位毫秒级的时间戳。
	timestamp, err := strconv.ParseInt(c.FormValue("timestamp"), 10, 64)
	if err == nil {
		requestParam.Timestamp = timestamp
	}
	logbuild.WriteString("【推送催单消息】接收美团参数：" + kit.JsonEncode(requestParam)) // 请求参数
	service := services.CallBackHandleService{}
	err = service.ReminderHandle(requestParam)
	logbuild.WriteString("处理结果：成功") // 处理结果
	glog.Info(logbuild.String())    // 记录日志
	return c.String(200, ReturnMsg)
}

// @Summary test
// @Tags 美团配送的状态回调 接口
// @Accept json
// @Produce json
// @Param product body dto.MtLogisticsOrderStatus true " "
// @Router /mp-callback/order-logisticsstatus-callback [POST]
func PushMtLogisticsOrderStatus(c echo.Context) error {
	model := new(dto.MtLogisticsOrderStatus)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	if model.OrderId == 0 {
		glog.Info("美团推送全额退款信息：", model.OrderId)
		return c.String(200, ReturnMsg)
	}

	glog.Info("zx美配订单状态回调开始：", kit.JsonEncode(model))

	var params oc.DeliveryNodeRequest
	switch model.LogisticsStatus {
	case 0:
		params.Status = 0
	case 15:
		params.Status = 15
	case 10:
		params.Status = 20
	case 20:
		params.Status = 30
	case 40:
		params.Status = 50
	case 100:
		params.Status = 99
		params.CancelReason = "配送单取消"
	default:
		return c.String(200, ReturnMsg)
	}

	params.DeliveryId = model.OrderId
	params.OrderSn = strconv.FormatInt(model.OrderId, 10)
	params.MtPeisongId = strconv.FormatInt(model.OrderId, 10)
	model.DispatcherName, _ = url.QueryUnescape(model.DispatcherName)
	params.CourierName = model.DispatcherName
	params.CourierPhone = model.DispatcherMobile
	params.CreateTime = kit.GetTimeNow()

	//定义返回信息
	var out = new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.MtDeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("zx美配订单状态回调结束："+params.OrderSn, r)
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if r.Code != 200 {
		return c.JSON(400, r)
	}
	return c.String(200, ReturnMsg)
}

// 美团配送的订单异常回调
// 每次配送员上报订单异常（如商家未营业、顾客留错电话等等）时，会对合作方提供的异常回调url进行回调。
// @Summary 订单异常回调
// @Tags 订单异常回调 接口
// @Accept json
// @Produce json
// @Param product body dto.MtLogisticsException true " "
// @Router /mp-callback/order-logisticsexception-callback [POST]
func MtLogisticsExceptionCall(c echo.Context) error {
	model := new(dto.MtLogisticsException)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	//如果是空的，就是美团修改接口的时候的验证，直接返回成功
	if model.AppPoiCode == "" {
		return c.String(200, ReturnMsg)
	}

	glog.Info("zx美配订单异常状态回调开始：", kit.JsonEncode(model))

	var params oc.OrderExceptionRequest
	params.DeliveryId = model.OrderViewId
	params.OldOrderSn = model.OrderViewId
	params.ExceptionDescr, _ = url.QueryUnescape(model.ExceptionReason)
	params.DistributionMode = "美团专配"
	params.ExceptionTime = kit.GetTimeNow()
	params.Source = 3

	//配送异常产生时间，恢复时为0   说明订单是恢复异常
	if model.Ctime == "0" {
		params.OrderStatus = 5
		params.ExceptionDescr = "异常恢复"
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.OES.OrderExceptionAdd(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("美配订单异常状态回调结束："+model.OrderViewId, r)
	if err != nil {
		return c.JSON(200, err.Error())
	}
	if r.Code != 200 {
		return c.JSON(200, r)
	}

	return c.String(200, ReturnMsg)
}

// / 美团隐私号降级通知推送（必接）
// @Summary 隐私号降级通知推送（必接）
// @Tags 美团推送接口
// @Accept json
// @Produce json
// @Router /mt-callback/privacy-demotion-callback [POST]
func PrivacyDemotionCallback(c echo.Context) error {
	glog.Info("隐私号降级通知推送：")
	return c.String(200, ReturnMsg)
}

// 美团推送全额退款信息（必接）
// @Summary 美团推送全额退款信息（必接）
// @Tags 美团推送接口
// @Accept json
// @Produce json
// @Param product body dto.OrderRefundCallback true " "
// @Router /mt-callback/order-refund-all-callback [GET]
func OrderRefundALLCallback(c echo.Context) error {

	model := new(dto.PartialRefund)
	if err := c.Bind(model); err != nil {
		glog.Error("美团全额退款回调阿闻bind失败-错误：", err, "-请求参数：", c.Request().URL.RawQuery)
		return c.JSON(400, err.Error())
	}
	if model.OrderId == 0 {
		glog.Info("美团推送全额退款信息：", model.OrderId)
		return c.String(200, ReturnMsg)
	}

	//由于POST请求过来的是URL带的参数，所以需要转码
	modelStr, _ := url.QueryUnescape(kit.JsonEncode(model))
	kit.JsonDecode([]byte(modelStr), model)
	glog.Info("美团推送全额退款信息接收参数：" + kit.JsonEncode(model))

	// 判断ServiceType已开通退款退货场景：1-仅退款流程,2-退款退货流程
	if model.ServiceType == "1" || model.ServiceType == "2" {
		err := services.MtOrderRefundALL(c, model)
		if err != nil {
			return err
		}
		return c.String(200, ReturnMsg)
	}

	modelRequest := new(oc.OrderRetrunRequest)
	orderId := fmt.Sprintf("%d", model.OrderId)
	modelRequest.OrderId = orderId
	refundId := fmt.Sprintf("%d", model.RefundId)
	modelRequest.RefundId = refundId
	modelRequest.Ctime = model.CTime
	reason, _ := url.QueryUnescape(model.Reason)
	modelRequest.Reason = reason
	modelRequest.Pictures = model.Pictures
	modelRequest.NotifyType = model.NotifyType

	if model.ResType == 0 {
		modelRequest.ResType = "等待处理中"
	} else if model.ResType == 1 {
		modelRequest.ResType = "商家驳回退款请求"
	} else if model.ResType == 2 {
		modelRequest.ResType = "商家同意退款"
	} else if model.ResType == 3 {
		modelRequest.ResType = "客服驳回退款请求"
	} else if model.ResType == 4 {
		modelRequest.ResType = "客服帮商家同意退款"
	} else if model.ResType == 5 {
		modelRequest.ResType = "超时未处理系统自动同意"
	} else if model.ResType == 6 {
		modelRequest.ResType = "系统自动确认"
	} else if model.ResType == 7 {
		modelRequest.ResType = "用户取消退款申请"
	} else if model.ResType == 8 {
		modelRequest.ResType = "用户取消退款申诉"
	} else {
		modelRequest.ResType = "未知状态"
	}
	modelRequest.Status = model.Status
	modelRequest.ApplyOpUserType = model.ApplyOpUserType
	modelRequest.LogisticsInfo = model.LogisticsInfo
	modelRequest.OrderFrom = 2
	modelRequest.RefunType = 1
	stringMoney := "0"
	modelRequest.Money = stringMoney

	//调用grpc取ApplyType开始
	modelRefundRequest := new(et.OrderRefundDetailRequest)
	modelRefundRequest.WmOrderIdView = model.OrderId
	modelRefundRequest.RefundType = 1
	storeMasterId, err := services.GetAppChannelByOrderSn(cast.ToString(model.OrderId))
	if err != nil {
		glog.Error("OrderRefundALLCallback,", "GetAppChannelByOrderSn", model.OrderId, err)
	}
	modelRefundRequest.StoreMasterId = storeMasterId

	etClient := et.GetExternalClient()
	defer etClient.Close()

	grpcResEt, err := etClient.MtReturn.GetOrderRefundDetail(etClient.Ctx, modelRefundRequest)
	if err != nil {
		glog.Error("美团推送全额退款信息处理结果：", orderId, err.Error())
		return c.JSON(400, grpcResEt)
	}
	if grpcResEt.Code != 200 {
		glog.Error("美团推送全额退款信息调用接口返回: ", orderId, kit.JsonEncode(grpcResEt))
		return c.JSON(400, grpcResEt)
	}
	var mtOrderRefundData dto.OrderRefundDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt), &mtOrderRefundData)
	if err != nil {
		glog.Error("美团推送全额退款信息处理结果："+orderId, err.Error())
		return c.JSON(400, grpcResEt)
	}

	var mtOrderRefundDetail dto.OrderDetailList
	if len(mtOrderRefundData.Data) > 0 {
		//正常情况model.RefundId >0
		//但存在是0的情况 美团的回调存在这种问题 其解释说是他们那边服务降级 0的情况读取美团返回数据的最近一条
		if model.RefundId > 0 {
			for _, subject := range mtOrderRefundData.Data {
				if subject.RefundId == model.RefundId {
					mtOrderRefundDetail.ApplyType = subject.ApplyType
					mtOrderRefundDetail.RefundId = subject.RefundId
					break
				}
			}
		} else {
			var latestRefundTime int32
			for _, subject := range mtOrderRefundData.Data {
				if subject.Ctime >= latestRefundTime {
					latestRefundTime = subject.Ctime
					mtOrderRefundDetail.ApplyType = subject.ApplyType
					mtOrderRefundDetail.RefundId = subject.RefundId
				}
			}
		}
	}

	//如果美团返回的RefundId = 0 则 读取从美团拉取的详情中读取出来的退款单号
	if model.RefundId == 0 {
		//如果没有详情数据或者详情数据中没有退款id 写入日志
		if mtOrderRefundDetail.RefundId == 0 || len(mtOrderRefundData.Data) == 0 {
			glog.Error(model.OrderId, "-从美团退款详情中未获取到退款id,获取详情返回结果", kit.JsonEncode(grpcResEt))
		}
		modelRequest.RefundId = cast.ToString(mtOrderRefundDetail.RefundId)
	}

	if mtOrderRefundDetail.ApplyType == 0 {
		modelRequest.ApplyType = "订单取消自动确认退款"
	} else if mtOrderRefundDetail.ApplyType == 1 {
		modelRequest.ApplyType = "用户申请退款"
	} else if mtOrderRefundDetail.ApplyType == 2 {
		modelRequest.ApplyType = "客服帮用户申请退款"
	} else if mtOrderRefundDetail.ApplyType == 3 {
		modelRequest.ApplyType = "重复提交而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 4 {
		modelRequest.ApplyType = "支付成功消息在订单取消之后到达而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 5 {
		modelRequest.ApplyType = "支付成功消息在订单被置为无效之后到达而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 6 {
		modelRequest.ApplyType = "用户被商家拒绝后申诉"
	} else if mtOrderRefundDetail.ApplyType == 7 {
		modelRequest.ApplyType = "商家申请退款"
	} else {
		modelRequest.ApplyType = "未知类型"
	}

	//调用grpc取ApplyType结束
	glog.Info("美团推送全额退款信息调用GRPC请求参数：" + kit.JsonEncode(modelRequest))
	ocClient := oc.GetOrderServiceClient()

	grpcRes, err := ocClient.ROC.SaveMtRefundOrderData(kit.SetTimeoutCtx(context.Background()), modelRequest)
	glog.Info("美团推送全额退款信息接收参数6:" + orderId)
	if err != nil {
		glog.Error("美团推送全额退款信息处理结果：", orderId, err.Error())
		return c.JSON(400, grpcRes)
	}

	if grpcRes.Code != 200 {
		glog.Error("美团推送全额退款信息调用接口返回: ", orderId, kit.JsonEncode(grpcRes))
		return c.JSON(400, grpcRes)
	}

	glog.Info("美团推送全额退款信息处理结果：成功", orderId)
	return c.String(200, ReturnMsg)
}

// 推送非接口操作更新商品的信息
// @Accept json
// @Produce json
// @Router /mt-callback/product-new-callback [POST]
func ProductNewCallBack(c echo.Context) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("美团回调商品修改数据panic", "-err:", err)
		}
	}()
	body, _ := ioutil.ReadAll(c.Request().Body)
	if len(body) == 0 {
		return c.String(200, ReturnMsg)
	}

	Data := make(map[string]string)
	for _, x := range strings.Split(string(body), "&") {
		name := strings.Split(x, "=")[0]
		value, _ := url.QueryUnescape(strings.Split(x, "=")[1])
		Data[name] = value
	}
	model := make(dto.JSONData, 0)
	retailDataStr, _ := url.QueryUnescape(Data["retail_data"])
	retailDataStr, _ = url.QueryUnescape(retailDataStr)
	glog.Info("接到美团回调新增商品数据:", retailDataStr)
	err := json.Unmarshal([]byte(retailDataStr), &model)
	if err != nil {
		glog.Error("美团回调回调新增转JSON失败:", err.Error())
	}
	//需要直接下架的数据
	downMap := make(map[string][]string, 0)
	for _, k := range model {
		downMap[k.AppPoiCode] = append(downMap[k.AppPoiCode], k.AppSpuCode)
	}

	etClient := et.GetExternalClient()
	defer etClient.Close()
	for k := range downMap {
		for _, foodCodeItem := range downMap[k] {
			params := et.RetailDeleteReq{
				AppPoiCode:        k,
				AppFoodCode:       foodCodeItem,
				IsDeleteRetailCat: 2,
			}
			_, err := etClient.RPC.RetailDelete(etClient.Ctx, &params)
			if err != nil {
				//错误只记录日志
				glog.Error("非接口操作商品回调操作下架错误：", k, kit.JsonEncode(params), err.Error())
			}
		}
	}

	return c.String(200, ReturnMsg)
}

// 推送非接口操作更新商品的信息
// @Accept json
// @Produce json
// @Router /mt-callback/product-update-callback [POST]
func ProductUpdateCallBack(c echo.Context) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("美团回调商品修改数据panic", "-err:", err)
		}
	}()

	body, _ := ioutil.ReadAll(c.Request().Body)
	//bodystr, _ := url.QueryUnescape(string(body))
	if len(body) == 0 {
		return c.String(200, ReturnMsg)
	}
	Data := make(map[string]string)
	for _, x := range strings.Split(string(body), "&") {
		name := strings.Split(x, "=")[0]
		value, _ := url.QueryUnescape(strings.Split(x, "=")[1])
		Data[name] = value
	}
	model := make(dto.JSONData, 0)
	retailDataStr, _ := url.QueryUnescape(Data["retail_data"])
	retailDataStr, _ = url.QueryUnescape(retailDataStr)
	glog.Info("接到美团回调上架操作商品数据:", retailDataStr)
	err := json.Unmarshal([]byte(retailDataStr), &model)
	if err != nil {
		glog.Error("美团回调商品修改数据转JSON失败:", err.Error())
	}

	//阿闻需要下架的
	awenDownMap := make(map[string][]string, 0)
	//第三方需要下架的
	thridDownMap := make(map[string][]string, 0)
	//需要直接下架的数据
	downMap := make(map[string][]string, 0)
	//所有门店数据
	AllMap := make(map[string]string, 0)
	for _, k := range model {
		//是我们自己创建的商品，并且SKUID
		if len(k.DiffContents.Skus) == 1 && k.DiffContents.Skus[0].ID != "" {
			//如果是下架变成上架，判断我们这边是否存在这个商品，是否是上架状态，否则调用下架
			if k.DiffContents.IsSoldOut.Result == 0 && k.DiffContents.IsSoldOut.Origin == 1 {

				_, err = strconv.Atoi(k.AppSpuCode)
				//是我们这边的数据规则
				if err == nil {
					thridDownMap[k.AppPoiCode] = append(thridDownMap[k.AppPoiCode], k.AppSpuCode)
					AllMap[k.AppPoiCode] = ""
				} else {
					downMap[k.AppPoiCode] = append(downMap[k.AppPoiCode], k.AppSpuCode)
				}

			}
			//如果是下架商品,需要把我们这边下架
			if k.DiffContents.IsSoldOut.Result == 1 && k.DiffContents.IsSoldOut.Origin == 0 {

				_, err = strconv.Atoi(k.AppSpuCode)
				//是我们这边的数据规则
				if err == nil {
					awenDownMap[k.AppPoiCode] = append(awenDownMap[k.AppPoiCode], k.AppSpuCode)
					AllMap[k.AppPoiCode] = ""
				} else {
					//如果是下架商品不需要我们这边处理了。不是我们这边创建的商品
				}
			}

		} else {
			//如果变化是从下架变成上架，就直接给他下架。这个回调不是我们接口操作的上架才会回调
			if k.DiffContents.IsSoldOut.Result == 0 && k.DiffContents.IsSoldOut.Origin == 1 && k.AppSpuCode != "" {
				downMap[k.AppPoiCode] = append(downMap[k.AppPoiCode], k.AppSpuCode)
			}
		}
	}
	//如果有数据需要处理
	if len(AllMap) > 0 {
		var requestDown = new(pc.ThirdDownChannelProductRequest)
		for k := range AllMap {
			requestDown.ChannelId = 2
			requestDown.UserName = "第三方"
			if _, ok := awenDownMap[k]; ok {
				//此为存在
				item := new(pc.ThirdDownChannelProductDetail)
				item.AppPoiCode = k
				item.ProductId = append(item.ProductId, awenDownMap[k]...)
				requestDown.Detail = append(requestDown.Detail, item)
			}
			if _, ok := thridDownMap[k]; ok {
				item := new(pc.ThirdDownChannelProductDetail)
				item.AppPoiCode = k
				item.ProductId = append(item.ProductId, thridDownMap[k]...)
				requestDown.DetailThird = append(requestDown.DetailThird, item)
			}

		}

		client := pc.GetDcProductClient()
		defer client.Close()
		client.RPC.ThirdDownChannelProduct(client.Ctx, requestDown)
	}

	if len(downMap) > 0 {
		etClient := et.GetExternalClient()
		defer etClient.Close()
		for k := range downMap {

			var params = et.RetailSellStatusRequest{}
			params.AppPoiCode = k
			params.SellStatus = 1
			for _, foodCodeItem := range downMap[k] {
				params.FoodData = append(params.FoodData, &et.AppFoodCode{AppFoodCode: foodCodeItem})
			}
			storeMasterId, retCode := services.GetAppChannelByStoreId(k)
			if retCode != code.Success {
				glog.Error("external-ui:MtUserInfoPhoneNumber", "GetAppChannelByStoreId,", kit.JsonEncode(model), retCode)
				return c.String(400, "获取店铺主体Id失败")
			}
			params.StoreMasterId = storeMasterId
			_, err := etClient.RPC.RetailSellStatus(etClient.Ctx, &params)
			if err != nil {
				//错误只记录日志
				glog.Error("非接口操作商品回调操作下架错误：", k, params.FoodData, err.Error())
			}
		}
	}
	//
	//if len(model) > 0 {
	//	if model[0].DiffContents.IsSoldOut.Result == 0 {
	//
	//	}
	//}
	return c.String(200, ReturnMsg)
}

// 推送订单信息修改消息（必接）
// @Summary 推送订单信息修改消息（必接）
// @Tags 美团推送接口
// @Accept json
// @Produce json
// @Param order body dto.OrdersUpdate true " "
// @Router /mt-callback/order-update-callback [POST]
func OrderUpdateCallback(c echo.Context) error {
	var logbuild strings.Builder

	defer glog.Info("推送订单信息修改日志:", logbuild.String()) // 记录日志

	modelupdate := new(dto.OrdersUpdate)
	body, _ := ioutil.ReadAll(c.Request().Body)
	//bodystr, _ := url.QueryUnescape(string(body))
	glog.Info("推送订单信息修改原始数据:", body)
	if len(body) == 0 {
		return c.String(200, ReturnMsg)
	}
	Data := make(map[string]string)
	for _, x := range strings.Split(string(body), "&") {
		name := strings.Split(x, "=")[0]
		value, _ := url.QueryUnescape(strings.Split(x, "=")[1])
		Data[name] = value
	}
	modelupdate.OrderId = cast.ToInt64(Data["order_id"])

	appChannel, err := GetAppChannelByOrderSn(cast.ToString(modelupdate.OrderId))
	if err != nil {
		return c.String(400, "获取appChannel失败")
	}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	orderDetaiModel := new(et.MtOrderDetailRequest)
	orderDetaiModel.OrderId = cast.ToString(modelupdate.OrderId)
	orderDetaiModel.IsMtLogistics = "1"
	orderDetaiModel.StoreMasterId = appChannel

	logbuild.WriteString("利用查询订单详情接口重新推参数：" + kit.JsonEncode(orderDetaiModel)) // 请求参数
	//todo tp mt
	grpcResEt, err := etClient.MtOrder.GetMtOrderDetail(etClient.Ctx, orderDetaiModel)
	if err != nil {
		logbuild.WriteString("推送订单信息修改消息 查询订单详情失败：" + err.Error())
		//glog.Error(logbuild.String())
		return c.String(400, "推送订单信息修改消息 查询订单详情失败")
	}
	if grpcResEt.Code != 200 {
		logbuild.WriteString("推送订单信息修改消息 查询订单详情失败：" + grpcResEt.Error)
		//glog.Error(logbuild.String())
		return c.String(400, "推送订单信息修改消息 查询订单详情失败")
	}

	var mtOrderDetailModel dto.OrderDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.MTOrderSDetail), &mtOrderDetailModel)
	if err != nil {
		logbuild.WriteString("推送订单信息修改消息 查询订单详情失败：" + err.Error())
		//glog.Error(logbuild.String())
		return c.String(400, "推送订单信息修改消息 查询订单详情失败")
	}

	model := new(oc.MtAddOrderRequest)
	orderid := fmt.Sprintf("%d", mtOrderDetailModel.WmOrderIdView)
	model.OrderSn = orderid
	model.OrderStatus = 20
	model.OrderStatusChild = 20101
	model.ShopId = mtOrderDetailModel.AppPoiCode
	wm_poi_name, _ := url.QueryUnescape(mtOrderDetailModel.WmPoiName)
	model.ShopName = wm_poi_name
	recipient_name, _ := url.QueryUnescape(mtOrderDetailModel.RecipientName)
	model.ReceiverName = recipient_name
	if mtOrderDetailModel.PickType == 0 {
		recipient_address, _ := url.QueryUnescape(mtOrderDetailModel.RecipientAddress)
		recipient_address1 := strings.Split(recipient_address, "@#")[0]
		recipient_address2 := strings.Split(recipient_address, "@#")[1]
		var r *regexp.Regexp
		r, err = regexp.Compile(".+?(省|市|自治区|自治州|县|区)")
		if err != nil {
			//glog.Info("利用查询订单详情接口重新推,从地址转换省市县失败:", orderid+recipient_address)
			logbuild.WriteString("利用查询订单详情接口重新推,从地址转换省市县失败")
			return c.String(400, "利用查询订单详情接口重新推,从地址转换省市县失败")
		}
		array := r.FindAllString(recipient_address2, -1)
		for _, v := range array {
			if strings.Contains(v, "省") ||
				strings.Contains(v, "自治区") {
				model.ReceiverState = v
			}
			if strings.Contains(v, "市") ||
				strings.Contains(v, "自治州") {
				model.ReceiverCity = v
			}
			if strings.Contains(v, "区") ||
				strings.Contains(v, "街道") ||
				strings.Contains(v, "县") {
				model.ReceiverDistrict = v
			}
		}
		model.ReceiverAddress = recipient_address2 + " " + recipient_address1
	}

	model.ReceiverPhone = mtOrderDetailModel.RecipientPhone
	intPayType := mtOrderDetailModel.PayType
	//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
	if intPayType == 2 {
		model.PayType = "Cod"
	} else {
		model.PayType = "NoCod"
	}
	model.ReceiverMobile = mtOrderDetailModel.RecipientPhone
	model.Latitude = float64(mtOrderDetailModel.Latitude)   //纬度
	model.Longitude = float64(mtOrderDetailModel.Longitude) //经度
	intEstimateArrivalTime := mtOrderDetailModel.EstimateArrivalTime
	if intEstimateArrivalTime != 0 {
		model.ExpectedTime = kit.GetTimeNow(time.Unix(intEstimateArrivalTime, 0))
	}
	//订单类型
	intDeliveryTime := mtOrderDetailModel.DeliveryTime
	if intDeliveryTime == 0 {
		model.OrderType = 1
	} else {
		model.OrderType = 2
	}
	caution, _ := url.QueryUnescape(mtOrderDetailModel.Caution)
	model.BuyerMemo = caution
	intPickType := mtOrderDetailModel.PickType
	//1快递 2外卖 3自提
	if intPickType == 0 {
		model.DeliveryType = 2
	} else if intPickType == 1 {
		model.DeliveryType = 3
	} else {
		model.DeliveryType = 1
	}

	logbuild.WriteString("订单修改请求参数：" + kit.JsonEncode(model)) // 请求参数

	//加入context渠道信息
	grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 2, UserAgent: 6, AppChannel: int(appChannel)}}
	ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

	model.AppChannel = appChannel
	ocClient := oc.GetOrderServiceClient()
	var grpcRes *oc.MtAddOrderResponse

	//修改为订单信息修改
	grpcRes, err = ocClient.Cart.MtUpdateOrder(ctx, model)
	if err != nil {
		logbuild.WriteString("修改订单信息失败：" + err.Error())
		//glog.Error(logbuild.String())
		return c.String(400, "修改订单信息失败")
	}
	if grpcRes.Code != 200 {
		logbuild.WriteString("修改订单信息失败" + grpcRes.Error)
		//glog.Error(logbuild.String())
		return c.String(400, "修改订单信息失败")
	}
	logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果：成功") // 处理结果
	//glog.Info("利用查询订单详情接口重新推美团已支付订单调用接口结果1: ", orderid, grpcRes) // 记录日志

	//logbuild.WriteString("利用查询订单详情接口重新推美团已支付订单处理结果：成功") // 处理结果
	//glog.Info(logbuild.String())                        // 记录日志

	return c.String(200, ReturnMsg)
}

// 美团推送部分退款信息（必接）
// @Summary 美团推送部分退款信息（必接）
// @Tags 美团推送接口
// @Accept json
// @Produce json
// @Param product body dto.OrderRefundCallback true " "
// @Router /mt-callback/order-refund-portion-callback [GET]
func OrderRefundPortionCallback(c echo.Context) error {
	model := new(dto.PartialRefund)
	if err := c.Bind(model); err != nil {
		glog.Error("美团部分退款回调阿闻bind失败-错误：", err, "-请求参数：", c.Request().URL.RawQuery)
		return c.JSON(400, err.Error())
	}
	if model.OrderId == 0 {
		glog.Info("美团推送部分退款信息：", model.OrderId)
		return c.String(200, ReturnMsg)
	}
	glog.Info("美团推送部分退款信息接收参数：" + kit.JsonEncode(model))
	// 已开通退款退货场景Status不为空，ServiceType：1-仅退款流程,2-退款退货流程
	if model.Status != "" {
		err := services.MtOrderRefundPartCallBack(c, model)
		if err != nil {
			return c.JSON(400, err.Error())
		}
		return c.String(200, ReturnMsg)
	}

	modelRequest := new(oc.OrderRetrunRequest)
	orderId := fmt.Sprintf("%d", model.OrderId)
	modelRequest.OrderId = orderId
	refundId := fmt.Sprintf("%d", model.RefundId)
	modelRequest.RefundId = refundId
	modelRequest.Ctime = model.CTime
	reason, _ := url.QueryUnescape(model.Reason)
	modelRequest.Reason = reason
	modelRequest.Pictures = model.Pictures
	modelRequest.NotifyType = model.NotifyType
	if model.ResType == 0 {
		modelRequest.ResType = "等待处理中"
	} else if model.ResType == 1 {
		modelRequest.ResType = "商家驳回退款请求"
	} else if model.ResType == 2 {
		modelRequest.ResType = "商家同意退款"
	} else if model.ResType == 3 {
		modelRequest.ResType = "客服驳回退款请求"
	} else if model.ResType == 4 {
		modelRequest.ResType = "客服帮商家同意退款"
	} else if model.ResType == 5 {
		modelRequest.ResType = "超时未处理系统自动同意"
	} else if model.ResType == 6 {
		modelRequest.ResType = "系统自动确认"
	} else if model.ResType == 7 {
		modelRequest.ResType = "用户取消退款申请"
	} else if model.ResType == 8 {
		modelRequest.ResType = "用户取消退款申诉"
	} else {
		modelRequest.ResType = "未知状态"
	}
	modelRequest.Status = model.Status
	modelRequest.ApplyOpUserType = model.ApplyOpUserType
	modelRequest.LogisticsInfo = model.LogisticsInfo
	modelRequest.OrderFrom = 2
	modelRequest.RefunType = 2
	stringMoney := fmt.Sprintf("%.2f", model.Money)
	modelRequest.Money = stringMoney
	var listProduct []dto.Food
	//由于POST请求过来的是URL带的参数，所以需要转码
	foodStr, _ := url.QueryUnescape(model.Food)
	x := (*[2]uintptr)(unsafe.Pointer(&foodStr))
	h := [3]uintptr{x[0], x[1], x[1]}
	err := json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listProduct)
	if err != nil {
		glog.Error("美团推送部分退款信息：" + orderId + err.Error())
		return c.String(500, "")
	}

	if len(listProduct) > 0 {
		for _, item := range listProduct {
			var product oc.RefundGoodsOrder
			product.GoodsId = item.SkuId
			product.Quantity = item.Count
			refundPrice := fmt.Sprintf("%.2f", item.RefundPrice*float64(item.Count))
			product.RefundAmount = refundPrice
			product.FoodPrice = float32(item.FoodPrice)
			product.Barcode = item.Upc
			stringRefundId := strconv.FormatInt(model.RefundId, 10)
			product.Refundorderid = stringRefundId
			product.FoodName = item.FoodName
			product.RefundPrice = float64(item.RefundPrice)
			product.BoxPrice = float64(item.BoxPrice)
			product.BoxNum = float32(item.BoxNum)
			product.Tkcount = item.Count
			modelRequest.RefundGoodsOrders = append(modelRequest.RefundGoodsOrders, &product)
		}
	}

	//调用grpc取ApplyType开始
	modelRefundRequest := new(et.OrderRefundDetailRequest)
	modelRefundRequest.WmOrderIdView = model.OrderId
	modelRefundRequest.RefundType = 2

	storeMasterId, err := services.GetAppChannelByOrderSn(cast.ToString(model.OrderId))
	if err != nil {
		glog.Error("OrderRefundPortionCallback,", "GetAppChannelByOrderSn", model.OrderId, err)
	}
	modelRefundRequest.StoreMasterId = storeMasterId

	glog.Info("美团推送部分退款信息接收参数1:" + kit.JsonEncode(modelRefundRequest))

	etClient := et.GetExternalClient()
	defer etClient.Close()

	grpcResEt, err := etClient.MtReturn.GetOrderRefundDetail(etClient.Ctx, modelRefundRequest)
	if err != nil {
		glog.Error("美团推送部分退款信息处理结果122"+orderId, err)
		glog.Error("美团推送部分退款信息处理结果：", orderId)
		return c.JSON(400, grpcResEt)
	}
	if grpcResEt.Code != 200 {
		glog.Error("美团推送部分退款信息调用接口返回: ", orderId, kit.JsonEncode(grpcResEt))
		return c.JSON(400, grpcResEt)
	}

	var mtOrderRefundData dto.OrderRefundDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt), &mtOrderRefundData)
	if err != nil {
		glog.Error("美团推送部分退款信息处理结果：", orderId, err.Error())
		return c.JSON(400, grpcResEt)
	}

	var mtOrderRefundDetail dto.OrderDetailList
	if len(mtOrderRefundData.Data) > 0 {
		for _, subject := range mtOrderRefundData.Data {
			if subject.RefundId == model.RefundId {
				mtOrderRefundDetail.ApplyType = subject.ApplyType
				if len(subject.RefundPartialEstimateCharge.ActivityMeituanAmount) > 0 {
					mtOrderRefundDetail.ActivityPtAmount = -cast.ToFloat64(subject.RefundPartialEstimateCharge.ActivityMeituanAmount)
				} else {
					mtOrderRefundDetail.ActivityPtAmount = 0
				}
				break
			}
		}
	}

	if mtOrderRefundDetail.ApplyType == 0 {
		modelRequest.ApplyType = "订单取消自动确认退款"
	} else if mtOrderRefundDetail.ApplyType == 1 {
		modelRequest.ApplyType = "用户申请退款"
	} else if mtOrderRefundDetail.ApplyType == 2 {
		modelRequest.ApplyType = "客服帮用户申请退款"
	} else if mtOrderRefundDetail.ApplyType == 3 {
		modelRequest.ApplyType = "重复提交而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 4 {
		modelRequest.ApplyType = "支付成功消息在订单取消之后到达而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 5 {
		modelRequest.ApplyType = "支付成功消息在订单被置为无效之后到达而自动申请"
	} else if mtOrderRefundDetail.ApplyType == 6 {
		modelRequest.ApplyType = "用户被商家拒绝后申诉"
	} else if mtOrderRefundDetail.ApplyType == 7 {
		modelRequest.ApplyType = "商家申请退款"
	} else {
		modelRequest.ApplyType = "未知类型"
	}
	//调用grpc取ApplyType结束
	modelRequest.ActivityPtAmount = mtOrderRefundDetail.ActivityPtAmount
	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.ROC.SaveMtRefundOrderData(kit.SetTimeoutCtx(context.Background()), modelRequest)
	if grpcRes.Code != 200 {
		glog.Error("美团推送全额退款信息调用接口返回: ", orderId, kit.JsonEncode(grpcRes))
		return c.JSON(400, grpcRes)

	}
	glog.Info("美团推送部分退款信息处理结果：成功", orderId)
	return c.String(200, ReturnMsg)
}

// APP方URL 推送客服赔付商家责任订单信息
// @Summary APP方URL 推送客服赔付商家责任订单信息
// @Tags 美团推送接口
// @Accept json
// @Produce json
// @Param product body dto.Compensation true " "
// @Router /mt-callback/order-compensation-callback [POST]
func CompensationCallback(c echo.Context) error {
	var logbuild strings.Builder
	model := new(dto.Compensation)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	logbuild.WriteString("APP方URL推送客服赔付商家责任订单信息：" + kit.JsonEncode(model)) // 请求参数
	glog.Info(logbuild.String())
	return c.String(200, ReturnMsg)
}

// 处理优惠信息
func DiscountExtra(extras string) []*oc.OrderPromotionModel {
	var OrderPromotionModel []*oc.OrderPromotionModel
	if len(extras) > 0 {
		orderDiscountExtraList := make([]dto.OrderDiscountExtra, 0)
		err := json.Unmarshal([]byte(extras), &orderDiscountExtraList)
		if err == nil {
			for _, v := range orderDiscountExtraList {
				OrderPromotionModel = append(OrderPromotionModel, &oc.OrderPromotionModel{
					PromotionId:    int32(v.ActDetailId),
					PromotionType:  int32(v.Type),
					PromotionTitle: v.Remark,
					PoiCharge:      int32(kit.YuanToFen(v.PoiCharge)),
					PtCharge:       int32(kit.YuanToFen(v.MtCharge)),
					PromotionFee:   int32(kit.YuanToFen(v.ReduceFee)),
				})
			}
		}
	}
	return OrderPromotionModel
}

/*func getRefundDetail(orderId int64) {
	//调用grpc取ApplyType开始
	modelRefundRequest := new(et.OrderRefundDetailRequest)
	modelRefundRequest.WmOrderIdView = orderId
	modelRefundRequest.RefundType = 1
	storeMasterId, err := services.GetAppChannelByOrderSn(cast.ToString(orderId))
	if err != nil {
		glog.Error("OrderRefundALLCallback,", "GetAppChannelByOrderSn", model.OrderId, err)
	}
	modelRefundRequest.StoreMasterId = storeMasterId

	etClient := et.GetExternalClient()
	defer etClient.Close()

	grpcResEt, err := etClient.MtReturn.GetOrderRefundDetail(etClient.Ctx, modelRefundRequest)
	if err != nil {
		glog.Error("美团推送全额退款信息处理结果：", orderId, err.Error())
		return c.JSON(400, grpcResEt)
	}
	if grpcResEt.Code != 200 {
		glog.Error("美团推送全额退款信息调用接口返回: ", orderId, kit.JsonEncode(grpcResEt))
		return c.JSON(400, grpcResEt)
	}
	var mtOrderRefundData dto.OrderRefundDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt), &mtOrderRefundData)
	if err != nil {
		glog.Error("美团推送全额退款信息处理结果："+orderId, err.Error())
		return c.JSON(400, grpcResEt)
	}
	var mtOrderRefundDetail dto.OrderDetailList
	if len(mtOrderRefundData.Data) > 0 {
		for _, subject := range mtOrderRefundData.Data {
			if subject.RefundId == model.RefundId {
				mtOrderRefundDetail.ApplyType = subject.ApplyType
				break
			}
		}
	}
}*/

// ProductDelCallback [消息推送]美团商家端操作删除的商品APP方URL
// @Accept json
// @Produce json
// @Router /mt-callback/product-del-callback [POST]
func ProductDelCallback(c echo.Context) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("美团回调删除商品panic", "-err:", err)
		}
	}()

	// 获取参数
	body, _ := ioutil.ReadAll(c.Request().Body)
	if len(body) == 0 {
		return c.String(200, ReturnMsg)
	}

	// 准备一个Map接受请求中的body内容
	bodyMap := make(map[string]string)
	for _, x := range strings.Split(string(body), "&") {
		name := strings.Split(x, "=")[0]
		value, _ := url.QueryUnescape(strings.Split(x, "=")[1])
		bodyMap[name] = value
	}

	// 创建一个结构体，接受业务参数
	productDel := make(dto.ProductDelCallback, 0)
	retailDataStr, _ := url.QueryUnescape(bodyMap["retail_data"])
	retailDataStr, _ = url.QueryUnescape(retailDataStr)
	glog.Info("接收到消息推送-美团商家端操作删除商品:", retailDataStr)
	err := json.Unmarshal([]byte(retailDataStr), &productDel)
	if err != nil {
		glog.Error("接收到消息推送-美团商家端操作删除商品，参数转JSON失败:", err.Error())
	}

	// 按门店划分商品id集合
	poiSpuMap := make(map[string][]string, 0)
	for _, k := range productDel {
		poiSpuMap[k.AppPoiCode] = append(poiSpuMap[k.AppPoiCode], k.AppSpuCode)
	}

	// 调product-center，执行删除操作
	pcClient := pc.GetDcChannelProductClient()
	defer pcClient.Close()
	for poi := range poiSpuMap {
		var req = pc.MtDelProductReq{
			AppPoiCode: poi,
			AppSpuCode: poiSpuMap[poi],
		}

		_, err := pcClient.RPC.MtDelProduct(pcClient.Ctx, &req)
		if err != nil {
			//错误只记录日志
			glog.Error("接收到消息推送-美团商家端操作删除商品错误：", kit.JsonEncode(req), err.Error())
		}
	}

	return nil
}

func SelfDelivery(c echo.Context) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("美团回调自配送panic", "-err:", err)
		}
	}()
	var logbuild strings.Builder
	model := new(dto.SelfDeliveryInfo)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	logbuild.WriteString("美团回调自配送信息：" + kit.JsonEncode(model)) // 请求参数
	glog.Info(logbuild.String())

	storeMasterId, retCode := app.GetStoreMasterId(model.AppPoiCode, 2)
	if retCode != code.Success {
		glog.Error("PaidOrders,", "GetStoreMasterId failed,", model.AppPoiCode, ",", 2)
	}
	if storeMasterId != 12 {
		return c.String(200, ReturnMsg)
	}
	etClient := et.GetExternalClient()
	defer etClient.Close()

	orderDetaiModel := new(et.MtOrderDetailRequest)
	orderDetaiModel.OrderId = cast.ToString(model.OrderViewId)
	orderDetaiModel.IsMtLogistics = "1"
	orderDetaiModel.StoreMasterId = storeMasterId
	logbuild.WriteString("利用查询订单详情接口重新推参数：" + kit.JsonEncode(orderDetaiModel)) // 请求参数
	//todo tp mt
	grpcResEt, err := etClient.MtOrder.GetMtOrderDetail(etClient.Ctx, orderDetaiModel)
	if err != nil {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(400, err.Error())
	}
	if grpcResEt.Code != 200 {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + grpcResEt.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, grpcResEt)
	}

	var mtOrderDetailModel dto.OrderDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.MTOrderSDetail), &mtOrderDetailModel)
	if err != nil {
		logbuild.WriteString("利用查询订单详情接口重新推处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(400, grpcResEt)
	}

	var params oc.DeliveryNodeRequest

	//params.DeliveryId = model.DeliveryId
	params.OrderSn = cast.ToString(model.OrderViewId)
	switch model.LogisticsStatus {
	case 0, 1:
		params.Status = 0
	case 10:
		params.Status = 20
	case 20:
		params.Status = 30
	case 40:
		params.Status = 50
	case 100:
		params.Status = 99
	default:
		//不需要更新的状态
		return c.String(200, ReturnMsg)
	}

	params.DeliveryId = model.OrderViewId
	params.OrderSn = strconv.FormatInt(model.OrderViewId, 10)
	params.MtPeisongId = strconv.FormatInt(model.OrderViewId, 10)
	params.CreateTime = kit.GetTimeNow()
	params.CourierName = mtOrderDetailModel.LogisticsDispatcherName
	params.CourierPhone = mtOrderDetailModel.LogisticsDispatcherMobile
	//params.CancelReason = CancelReason
	//params.PredictDeliveryTime = model.PredictDeliveryTime

	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	// if err != nil {
	// 	glog.Error("PushOrderStatus", "GetAppChannelByOrderSn", model.OrderId, err)
	// 	return c.JSON(200, result)
	// }
	// params.StoreMasterId = storeMasterId

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.DeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("美配订单状态回调结束："+cast.ToString(model.OrderViewId), r)
	if err != nil || r.Code != 200 {
		return c.JSON(400, r)
	}

	return c.String(200, ReturnMsg)
}
