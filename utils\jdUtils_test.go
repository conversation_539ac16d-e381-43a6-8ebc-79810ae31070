package utils

import (
	"reflect"
	"testing"
)

func Test_jddjHandle_GetAESKeyAndIv(t *testing.T) {
	type fields struct {
		requestParams map[string]string
		appSecret     string
	}
	tests := []struct {
		name    string
		fields  fields
		wantKey []byte
		wantIv  []byte
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				appSecret: "0bcbe9d6e6124cf2aef2856a540f1326",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jH := &jddjHandle{
				requestParams: tt.fields.requestParams,
				appSecret:     tt.fields.appSecret,
			}
			gotKey, gotIv := jH.GetAESKeyAndIv()
			if !reflect.DeepEqual(gotKey, tt.wantKey) {
				t.Errorf("GetAESKeyAndIv() gotKey = %v, want %v", gotKey, tt.wantKey)
			}
			if !reflect.DeepEqual(gotIv, tt.wantIv) {
				t.Errorf("GetAESKeyAndIv() gotIv = %v, want %v", gotIv, tt.wantIv)
			}
		})
	}
}
