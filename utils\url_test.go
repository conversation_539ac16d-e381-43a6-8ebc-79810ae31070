package utils

import (
	"net/url"
	"reflect"
	"testing"
)

func TestParseQuery(t *testing.T) {
	type args struct {
		query string
	}
	tests := []struct {
		name         string
		args         args
		wantQueryMap url.Values
		wantErr      bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				query: "app_key=41f4076326dd48218f35987a99903cd9&v=1.0&sign=C897898A2D921733C4CD86D4CCC60A70&format=json&jd_param_json=%7B%22businessTag%22%3A%22dj_new_promise_v2%3Bdj_new_cashier%3Bdj_aging_nextday%3B%3Bdj_cs%3Bdj_mobile_safe_order%3Bdj_coupon_logic_80%3Bpicking_up%3B%22%2C%22orderId%22%3A%222209599758000072%22%2C%22storeId%22%3A%2212191850%22%7D&token=42c2665f-afe7-4a56-af0e-0bc5cd1e0b51&timestamp=2022-04-22+02%3A36%3A48",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotQueryMap, err := ParseQuery(tt.args.query)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseQuery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotQueryMap, tt.wantQueryMap) {
				t.Errorf("ParseQuery() gotQueryMap = %v, want %v", gotQueryMap, tt.wantQueryMap)
			}
		})
	}
}
