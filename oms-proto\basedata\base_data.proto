syntax = "proto3";

package basedata;

option go_package = "./oms-proto/basedata";

service BaseDataService {
  // 单据类型
  rpc OrderType(CommonRequest) returns (CommonReply) {}
  // 订单来源
  rpc OrderSource(CommonRequest) returns (CommonReply) {}
  // 交易类型
  rpc PayMode(CommonRequest) returns (CommonReply) {}
  // 销售渠道
  rpc OrderChannel(CommonRequest) returns (CommonReply) {}
  // 交货方式
  rpc DeliveryMode(CommonRequest) returns (CommonReply) {}
}

//获取单据类
message CommonRequest {

}

message CommonReply {
  // 响应码
  int32 code = 1;
  // 返回信息
  string message = 2;
  // 通用数据
  repeated CommonData data = 3;
}


message CommonData {
  int32 id = 1;
  int32 value = 2;
  string remark = 3;
  string create_time = 4;
  string update_time = 5;
}
