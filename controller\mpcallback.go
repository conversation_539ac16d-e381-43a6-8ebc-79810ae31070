package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/dac"
	"external-ui/proto/oc"
	"external-ui/utils"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 门店状态回调
// @Tags 门店状态回调
// @Accept json
// @Produce json
// @Param product body dto.MpPushShopStatus true " "
// @Success 200 {object} dto.MpReturnResult
// @Failure 400 {object} dto.MpReturnResult
// @Router /external/mp-callback/shop-status [POST]
func PushShopStatus(c echo.Context) error {
	model := new(dto.MpPushShopStatus)

	result := new(dto.MpReturnResult)

	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(200, result)
	}
	glog.Info("美配门店状态回调开始：", model)

	dacClient := dac.GetDataCenterClient()
	defer dacClient.Close()

	var params dac.CreateShopStatusRequest
	params.ShopId = model.ShopId
	params.ChannelId = "2"
	params.Status = model.Status

	r, err := dacClient.RPC.CreateShopStatus(dacClient.Ctx, &params)
	glog.Info("美配门店状态回调结束：", r)
	if err != nil || r.Code != 200 {
		return c.JSON(200, result)
	}
	result.Code = 0
	return c.JSON(200, result)

}

// @Summary test
// @Tags 订单状态回调 接口
// @Accept json
// @Produce json
// @Param product body dto.MpPushOrderStatus true " "
// @Success 200 {object} dto.MpReturnResult
// @Failure 400 {object} dto.MpReturnResult
// @Router /mp-callback/order-status [POST]
func PushOrderStatus(c echo.Context) error {
	model := new(dto.MpPushOrderStatus)
	//返回失败
	result := &dto.MpReturnResult{
		Code: 1,
	}

	if err := c.Bind(model); err != nil {
		return c.JSON(200, result)
	}
	glog.Info("美配订单状态回调开始：", kit.JsonEncode(model))
	//屏蔽手动创建的配送单号
	if len(model.OrderId) > 2 && model.OrderId[:2] == "SI" {
		result.Code = 0
		return c.JSON(200, result)
	}

	var params oc.DeliveryNodeRequest
	CancelReason := model.CancelReason
	if model.CancelReasonId == 202 {
		CancelReason = "阿闻超时未接单自动取消"
	}
	params.DeliveryId = model.DeliveryId
	params.OrderSn = model.OrderId
	params.Status = model.Status
	params.CourierName = model.CourierName
	params.CourierPhone = model.CourierPhone
	params.CancelReason = CancelReason
	params.PredictDeliveryTime = model.PredictDeliveryTime
	params.CreateTime = kit.GetTimeNow()

	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	// if err != nil {
	// 	glog.Error("PushOrderStatus", "GetAppChannelByOrderSn", model.OrderId, err)
	// 	return c.JSON(200, result)
	// }
	// params.StoreMasterId = storeMasterId

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.DeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("美配订单状态回调结束："+model.MtPeisongId, r)
	if err != nil || r.Code != 200 {
		return c.JSON(200, result)
	}

	result.Code = 0
	return c.JSON(200, result)
}

func TestMp(c echo.Context) error {
	result := new(dto.MpReturnResult)
	result.Code = 1

	glog.Info("testget：", "248")
	return c.JSON(200, result)
}

//订单异常回调
//每次配送员上报订单异常（如商家未营业、顾客留错电话等等）时，会对合作方提供的异常回调url进行回调。
// @Summary 订单异常回调
// @Tags 订单异常回调 接口
// @Accept json
// @Produce json
// @Param product body dto.MpPushOrderUnusual true " "
// @Success 200 {object} dto.MpReturnResult
// @Failure 400 {object} dto.MpReturnResult
// @Router /mp-callback/order-unusual [POST]
func PushOrderUnusual(c echo.Context) error {
	model := new(dto.MpPushOrderUnusual)
	result := new(dto.MpReturnResult)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("美配订单异常状态回调开始：", model)

	var params oc.OrderExceptionRequest
	params.DeliveryId = strconv.FormatInt(model.DeliveryId, 10)
	params.MtPeisongId = model.MtPeisongId
	params.OrderId = model.OrderId
	params.ExceptionId = strconv.FormatInt(model.ExceptionId, 10)
	params.ExceptionCode = model.ExceptionCode
	params.ExceptionDescr = model.ExceptionDescr
	params.ExceptionTime = kit.GetTimeNow(time.Unix(cast.ToInt64(model.ExceptionTime), 0))
	params.CourierName = model.CourierName
	params.CourierPhone = model.CourierPhone
	params.Source = 3

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.OES.OrderExceptionAdd(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("美配订单异常状态回调结束："+model.MtPeisongId, r)
	if err != nil || r.Code != 200 {
		return c.JSON(200, result)
	}

	result.Code = 0
	return c.JSON(200, result)
}

//配送范围变更回调
//每次美团运营新增、修改配送范围时，会对合作方提供的配送范围变更回调url进行回调。
// @Summary 配送范围变更回调
// @Tags 配送范围变更回调 接口
// @Accept json
// @Produce json
// @Param product body dto.MpPushShopScope true " "
// @Success 200 {object} dto.MpReturnResult
// @Failure 400 {object} dto.MpReturnResult
// @Router /mp-callback/shop-scope [POST]
func PushShopScope(c echo.Context) error {
	model := new(dto.MpPushShopScope)
	result := new(dto.MpReturnResult)
	result.Code = 1
	err := c.Bind(model)
	if err != nil {
		return c.JSON(200, result)
	}

	//根据订单中的门店id获取对应的数据中心的门店财务编码
	FinanceCode := utils.HashGet("store:relation:mttodc", model.ShopId)
	if FinanceCode == "" {
		glog.Error("配送范围变更回调,财务编码不存在" + model.ShopId)
		return c.JSON(200, result)
	}

	dacClient := dac.GetDataCenterClient()
	defer dacClient.Close()

	grpcRes, err := dacClient.RPC.ShopDeliveryServiceGet(dacClient.Ctx, &dac.ShopDeliveryServiceGetRequest{
		Finance_Code: FinanceCode,
		Channel_Id:   int32(1),
	})
	if err != nil {
		grpcRes = new(dac.ShopDeliveryServiceGetResponse)
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		glog.Error("配送范围变更回调,店铺配送服务设置信息获取失败" + model.ShopId)
		return c.JSON(200, result)
	}
	if grpcRes.Data.IsPushScope {
		setModel := new(dac.ShopDeliveryServiceSetRequest)
		setModel.ChargeType = grpcRes.Data.Chargetype
		setModel.Basefee = grpcRes.Data.Basefee
		setModel.Deliveryfee = grpcRes.Data.Deliveryfee
		setModel.IsPushScope = grpcRes.Data.IsPushScope
		setModel.Finance_Code = FinanceCode
		setModel.Channel_Id = 1
		setModel.Distanceprice = grpcRes.Data.Distanceprice
		setModel.Weightprice = grpcRes.Data.Weightprice
		setModel.Specialtimefee = grpcRes.Data.Specialtimefee
		setModel.ZilongId = grpcRes.Data.ZilongId
		var mpShopArea dto.MpShopArea
		var list []dto.ShopAreaData
		err = json.Unmarshal([]byte(model.Scope), &mpShopArea)
		x := (*[2]uintptr)(unsafe.Pointer(&mpShopArea))
		h := [3]uintptr{x[0], x[1], x[1]}
		err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &list)
		if err != nil {
			glog.Error("配送范围变更回调失败"+model.ShopId+"", err)
			return c.JSON(200, result)
		}
		var str strings.Builder
		str.WriteString("[")
		if len(list) > 0 {
			for i := 0; i < len(list); i++ {
				xx := strconv.FormatFloat(list[i].X, 'f', -1, 64)
				yy := strconv.FormatFloat(list[i].Y, 'f', -1, 64)
				if i == 0 {
					str.WriteString("[" + yy + "," + xx + "]")
				} else {
					str.WriteString("[" + yy + "," + xx + "]")
				}
			}
		}
		str.WriteString("]")
		setModel.DeliveryArea = str.String()
		if setModel.Finance_Code == "" || setModel.Channel_Id == 0 {
			glog.Error("配送范围变更回调失败"+model.ShopId+"", "财务ID与渠道ID不能为空")
			return c.JSON(200, result)
		}

		_, err = dacClient.RPC.ShopDeliveryServiceSet(dacClient.Ctx, setModel)
		if err != nil {
			glog.Error("配送范围变更回调失败"+model.ShopId+"", err.Error())
			return c.JSON(200, result)
		}
	}
	result.Code = 0
	return c.JSON(200, result)
}
