package controller

import (
	"external-ui/proto/oc"
	"external-ui/utils"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"io/ioutil"
	"os"
	"strings"
	"time"
)

// 发票回调接口
func InvoiceCallback(c echo.Context) error {
	out := new(oc.InvoiceResponse)

	// 参数解析
	params, err := ioutil.ReadAll(c.Request().Body)
	glog.Error("发票回调请求日志:query:", c.QueryParams(), ",body:", string(params))
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(200, out)
	}

	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "production" || env == "pro" {
		kit.IsDebug = false
	} else {
		kit.IsDebug = true
	}

	if !kit.IsDebug {
		// 验签
		timestamp := c.FormValue("timestamp")
		strSign := utils.MakeSign(map[string]string{
			"appId":     config.GetString("BJAuth.AppId"),
			"timestamp": timestamp,
		}, string(params), config.GetString("BJAuth.Secret"))
		if strSign != c.FormValue("sign") {
			out.Code = 401
			out.Message = "sign签名错误"
			return c.JSON(200, out)
		}
		d, _ := time.ParseDuration("5m")
		if cast.ToInt64(timestamp) < time.Now().Add(-d).Unix() {
			out.Code = 400
			out.Message = "sign签名已过期"
			return c.JSON(200, out)
		}
	}

	// 服务调用
	client := oc.GetOrderServiceClient()
	res, err := client.IV.InvoiceCallback(client.Ctx, &oc.InvoiceCallbackRequest{
		ParamsJson: string(params),
	})
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("发票回调请求日志 错误", err.Error())
		return c.JSON(400, out)
	}

	return c.JSON(200, res)
}
