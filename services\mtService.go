package services

import (
	"context"
	"encoding/json"
	"errors"
	"external-ui/dto"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"fmt"
	"net/url"
	"strconv"
	"unsafe"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// MtOrderRefundALL 接收美团退货退款
func MtOrderRefundALL(c echo.Context, model *dto.PartialRefund) error {
	prefix := "美团推送全额退货退款，"
	modelRequest := new(oc.OrderRetrunRequest)
	orderId := fmt.Sprintf("%d", model.OrderId)
	modelRequest.OrderId = orderId
	refundId := fmt.Sprintf("%d", model.RefundId)

	modelRequest.RefundId = refundId
	modelRequest.Ctime = model.CTime
	reason, _ := url.QueryUnescape(model.Reason)
	modelRequest.Reason = reason
	modelRequest.Pictures = model.Pictures
	modelRequest.NotifyType = model.NotifyType
	modelRequest.ServiceType = model.ServiceType
	modelRequest.ApplyOpUserType = model.ApplyOpUserType
	// 退款类型处理
	err2, done := StatusText(c, model, modelRequest, orderId)
	if done {
		return err2
	}

	modelRequest.Status = model.Status
	modelRequest.LogisticsInfo = model.LogisticsInfo
	modelRequest.OrderFrom = 2
	modelRequest.RefunType = 1
	stringMoney := "0"
	modelRequest.Money = stringMoney

	//调用grpc取ApplyType开始
	modelRefundRequest := new(et.OrderRefundDetailRequest)
	modelRefundRequest.WmOrderIdView = model.OrderId
	modelRefundRequest.RefundType = cast.ToInt32(model.ServiceType)
	storeMasterId, err := GetAppChannelByOrderSn(cast.ToString(model.OrderId))
	if err != nil {
		glog.Errorf(prefix+"调用 GetAppChannelByOrderSn 失败：订单号:%s，错误信息：%s", orderId, err)
		return err
	}
	modelRefundRequest.StoreMasterId = storeMasterId

	etClient := et.GetExternalClient()
	defer etClient.Close()

	grpcResEt, err := etClient.MtReturn.GetOrderRefundDetail(etClient.Ctx, modelRefundRequest)
	if err != nil {
		glog.Errorf(prefix+"退款信息异常:订单号：%s,错误：%s,结果：%s", orderId, err.Error(), kit.JsonEncode(grpcResEt))
		return err
	}
	if grpcResEt.Code != 200 {
		glog.Errorf(prefix+"退款信息调用接口返回:订单号：%s,错误：%s,结果：%s ", orderId, err.Error(), kit.JsonEncode(grpcResEt))
		return errors.New("调用接口返回:" + kit.JsonEncode(grpcResEt))
	}
	var mtOrderRefundData dto.OrderRefundDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt), &mtOrderRefundData)
	if err != nil {
		glog.Errorf(prefix+"解析报错：订单号：%s,错误：%s"+orderId, err.Error())
		return err
	}

	var mtOrderRefundDetail dto.OrderDetailList

	mtOrderRefundDetail.ApplyType = 1
	if len(mtOrderRefundData.Data) > 0 {
		//正常情况model.RefundId >0
		//但存在是0的情况 美团的回调存在这种问题 其解释说是他们那边服务降级 0的情况读取美团返回数据的最近一条
		if model.RefundId > 0 {
			for _, subject := range mtOrderRefundData.Data {
				if subject.RefundId == model.RefundId {
					mtOrderRefundDetail.ApplyType = subject.ApplyType
					mtOrderRefundDetail.RefundId = subject.RefundId
					break
				}
			}
		} else {
			var latestRefundTime int32
			for _, subject := range mtOrderRefundData.Data {
				if subject.Ctime >= latestRefundTime {
					latestRefundTime = subject.Ctime
					mtOrderRefundDetail.ApplyType = subject.ApplyType
					mtOrderRefundDetail.RefundId = subject.RefundId
				}
			}
		}
	}

	//如果美团返回的RefundId = 0 则 读取从美团拉取的详情中读取出来的退款单号
	if model.RefundId == 0 {
		//如果没有详情数据或者详情数据中没有退款id 写入日志
		if mtOrderRefundDetail.RefundId == 0 || len(mtOrderRefundData.Data) == 0 {
			glog.Errorf(prefix+"从美团退款详情中未获取到退款id,获取详情返回:订单号：%s,结果：%s", orderId, kit.JsonEncode(grpcResEt))
		}
		modelRequest.RefundId = cast.ToString(mtOrderRefundDetail.RefundId)
	}
	//申请类型
	ApplyTypeText(mtOrderRefundDetail, modelRequest)

	//调用grpc取ApplyType结束
	glog.Infof(prefix + "调用GRPC请求参数：%s" + kit.JsonEncode(modelRequest))
	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.ROC.SaveMtRefundOrderData(kit.SetTimeoutCtx(context.Background()), modelRequest)
	if err != nil {
		glog.Errorf(prefix+"调用GRPC请求异常:订单号：%s,错误：%s,结果：%s", orderId, err.Error(), kit.JsonEncode(grpcRes))
		return err
	}

	if grpcRes.Code != 200 {
		glog.Errorf(prefix+"调用GRPC请求失败:订单号：%s,错误：%s,结果：%s", orderId, kit.JsonEncode(grpcRes))
		return errors.New("调用接口返回:" + kit.JsonEncode(grpcRes))
	}

	glog.Infof(prefix, "处理结果：成功，订单号：%s", orderId)
	return nil
}

// MtOrderRefundPart 接收美团部分退货退款
func MtOrderRefundPartCallBack(c echo.Context, model *dto.PartialRefund) error {
	prefix := "美团推送部分退货退款，"
	modelRequest := new(oc.OrderRetrunRequest)
	orderId := fmt.Sprintf("%d", model.OrderId)
	modelRequest.OrderId = orderId
	refundId := fmt.Sprintf("%d", model.RefundId)
	modelRequest.RefundId = refundId
	modelRequest.Ctime = model.CTime
	reason, _ := url.QueryUnescape(model.Reason)
	modelRequest.Reason = reason
	modelRequest.Pictures = model.Pictures
	modelRequest.NotifyType = model.NotifyType
	modelRequest.ServiceType = model.ServiceType
	modelRequest.ApplyOpUserType = model.ApplyOpUserType
	// 退款类型处理
	err2, done := StatusText(c, model, modelRequest, orderId)
	if done {
		return err2
	}

	modelRequest.Status = model.Status
	modelRequest.LogisticsInfo = model.LogisticsInfo
	modelRequest.OrderFrom = 2
	modelRequest.RefunType = 2
	stringMoney := fmt.Sprintf("%.2f", model.Money)
	modelRequest.Money = stringMoney
	var listProduct []dto.Food
	//由于请求过来的是URL带的参数，所以需要转码
	foodStr, _ := url.QueryUnescape(model.Food)
	x := (*[2]uintptr)(unsafe.Pointer(&foodStr))
	h := [3]uintptr{x[0], x[1], x[1]}
	err := json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &listProduct)
	if err != nil {
		glog.Errorf(prefix+"解析失败:订单号：%s,错误信息:%s", orderId, err.Error())
		return err
	}

	if len(listProduct) > 0 {
		for _, item := range listProduct {
			var product oc.RefundGoodsOrder
			product.GoodsId = item.SkuId
			product.Quantity = item.Count
			refundPrice := fmt.Sprintf("%.2f", item.RefundPrice*float64(item.Count))
			product.RefundAmount = refundPrice
			product.FoodPrice = float32(item.FoodPrice)
			product.Barcode = item.Upc
			stringRefundId := strconv.FormatInt(model.RefundId, 10)
			product.Refundorderid = stringRefundId
			product.FoodName = item.FoodName
			product.RefundPrice = float64(item.RefundPrice)
			product.BoxPrice = float64(item.BoxPrice)
			product.BoxNum = float32(item.BoxNum)
			product.Tkcount = item.Count
			modelRequest.RefundGoodsOrders = append(modelRequest.RefundGoodsOrders, &product)
		}
	}

	//调用grpc取ApplyType开始
	modelRefundRequest := new(et.OrderRefundDetailRequest)
	modelRefundRequest.WmOrderIdView = model.OrderId
	modelRefundRequest.RefundType = cast.ToInt32(model.ServiceType)

	storeMasterId, err := GetAppChannelByOrderSn(cast.ToString(model.OrderId))
	if err != nil {
		glog.Errorf(prefix+"GetAppChannelByOrderSn,订单号：%s,错误信息：%s", model.OrderId, err)
	}
	modelRefundRequest.StoreMasterId = storeMasterId

	glog.Infof(prefix + "信息接收参数1:%s" + kit.JsonEncode(modelRefundRequest))

	etClient := et.GetExternalClient()
	defer etClient.Close()

	grpcResEt, err := etClient.MtReturn.GetOrderRefundDetail(etClient.Ctx, modelRefundRequest)
	if err != nil {
		glog.Errorf(prefix+"GetOrderRefundDetail 调用异常,订单号：%s,错误信息：%s", orderId, err)
		return err
	}
	if grpcResEt.Code != 200 {
		glog.Errorf(prefix+"调用接口返回: 订单号：%s,错误信息：%s,返回结果：%s", orderId, err.Error(), kit.JsonEncode(grpcResEt))
		return err
	}

	var mtOrderRefundData dto.OrderRefundDetail
	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt), &mtOrderRefundData)
	if err != nil {
		glog.Errorf(prefix+"处理结果：订单号：%s,错误：%s", orderId, err.Error())
		return err
	}

	var mtOrderRefundDetail dto.OrderDetailList
	mtOrderRefundDetail.ApplyType = 1
	if len(mtOrderRefundData.Data) > 0 {
		for _, subject := range mtOrderRefundData.Data {
			if subject.RefundId == model.RefundId {
				mtOrderRefundDetail.ApplyType = subject.ApplyType
				if len(subject.RefundPartialEstimateCharge.ActivityMeituanAmount) > 0 {
					mtOrderRefundDetail.ActivityPtAmount = -cast.ToFloat64(subject.RefundPartialEstimateCharge.ActivityMeituanAmount)
				} else {
					mtOrderRefundDetail.ActivityPtAmount = 0
				}
				break
			}
		}
	}
	// 申请类型
	ApplyTypeText(mtOrderRefundDetail, modelRequest)
	//调用grpc取ApplyType结束
	modelRequest.ActivityPtAmount = mtOrderRefundDetail.ActivityPtAmount
	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.ROC.SaveMtRefundOrderData(kit.SetTimeoutCtx(context.Background()), modelRequest)
	if err != nil {
		glog.Errorf(prefix+"调用接口异常: 订单号：%s,错误信息：%s", orderId, err.Error())
		return err
	}
	if grpcRes.Code != 200 {
		glog.Errorf(prefix+"调用接口返回: 订单号：%s,错误信息：%s,返回结果：%s", orderId, kit.JsonEncode(grpcRes))
		return errors.New("调用接口返回:" + kit.JsonEncode(grpcRes))
	}
	glog.Infof(prefix+"处理结果：成功,订单号：", orderId)
	return nil
}

// ApplyTypeText 申请类型
func ApplyTypeText(mtOrderRefundDetail dto.OrderDetailList, modelRequest *oc.OrderRetrunRequest) {
	switch mtOrderRefundDetail.ApplyType {
	case 0:
		modelRequest.ApplyType = "订单取消自动确认退款"
	case 1:
		modelRequest.ApplyType = "用户申请退款"
	case 2:
		modelRequest.ApplyType = "客服帮用户申请退款"
	case 3:
		modelRequest.ApplyType = "重复提交而自动申请"
	case 4:
		modelRequest.ApplyType = "支付成功消息在订单取消之后到达而自动申请"
	case 5:
		modelRequest.ApplyType = "支付成功消息在订单被置为无效之后到达而自动申请"
	case 6:
		modelRequest.ApplyType = "用户被商家拒绝后申诉"
	case 7:
		modelRequest.ApplyType = "商家申请退款"
	default:
		modelRequest.ApplyType = "未知类型"
	}
}

// StatusText 美团退款状态
func StatusText(c echo.Context, model *dto.PartialRefund, modelRequest *oc.OrderRetrunRequest, orderId string) (error, bool) {
	switch model.ApplyOpUserType {
	case "1":
		modelRequest.Operationer = "美团客人"
	case "2":
		modelRequest.Operationer = "商家"
	case "3":
		modelRequest.Operationer = "美团客服"
	case "4":
		modelRequest.Operationer = "商家BD"
		modelRequest.ApplyOpUserType = "2"
	case "5":
		modelRequest.Operationer = "系统"
		modelRequest.ApplyOpUserType = "2"
	case "6":
		modelRequest.Operationer = "开放平台"
		modelRequest.ApplyOpUserType = "2"
	}

	resReason, _ := url.QueryUnescape(model.ResReason)
	switch model.Status {
	case "1":
		modelRequest.ResType = "申请中"
		modelRequest.NotifyType = "apply"
	case "10":
		modelRequest.ResType = "初审已同意"
		modelRequest.NotifyType = "agree"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "11":
		modelRequest.ResType = "初审已驳回"
		modelRequest.NotifyType = "reject"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "16":
		modelRequest.ResType = "初审已申诉"
		modelRequest.NotifyType = "apply"
	case "17":
		modelRequest.ResType = "初审申诉已同意"
		modelRequest.NotifyType = "agree"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "18":
		modelRequest.ResType = "初审申诉已驳回"
		modelRequest.NotifyType = "reject"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "20":
		//解析json中model.LogisticsInfo中的expressCompany、expressNumber、reason的值
		var logisticsInfo dto.LogisticsInfo
		err := json.Unmarshal([]byte(model.LogisticsInfo), &logisticsInfo)
		if err != nil {
			glog.Error("美团推送退货退款信息处理结果：物流信息解析失败："+orderId, err.Error())
			return err, true
		}
		modelRequest.ResType = fmt.Sprintf("终审已发起（用户已发货）,物流公司：%s,物流单号：%s,发货原因：%s", logisticsInfo.ExpressCompany, logisticsInfo.ExpressNumber, logisticsInfo.Reason)
		modelRequest.NotifyType = "apply"
	case "21":
		modelRequest.ResType = "终审已同意"
		modelRequest.NotifyType = "agree"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "22":
		modelRequest.ResType = "终审已驳回"
		modelRequest.NotifyType = "reject"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "26":
		modelRequest.ResType = "终审已申诉"
		modelRequest.NotifyType = "apply"
	case "27":
		modelRequest.ResType = "终审申诉已同意"
		modelRequest.NotifyType = "agree"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "28":
		modelRequest.ResType = "终审申诉已驳回"
		modelRequest.NotifyType = "reject"
		model.Reason = resReason
		modelRequest.Operationer = ""
	case "30":
		modelRequest.ResType = "已取消"
		modelRequest.NotifyType = "cancelRefund"
	default:
		modelRequest.ResType = "未知状态"
	}

	return nil, false
}
