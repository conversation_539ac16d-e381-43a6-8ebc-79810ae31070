package route

import (
	"external-ui/controller"
	"github.com/labstack/echo/v4"
)

func MytCallbackGroup(e *echo.Group) {
	g := e.Group("/myt/")
	g.POST("delivery_change", controller.MytDeliveryChange)
	g.POST("order_confirm", controller.MytOrderConfirm)
	g.POST("order_change", controller.MytOrderChange)
	g.POST("multi_rider_location", controller.MytMultiRiderLocation)
	g.POST("order_list", controller.MytOrderlist)
	g.<PERSON>OST("order_detail", controller.MytOrderDetail)
}
