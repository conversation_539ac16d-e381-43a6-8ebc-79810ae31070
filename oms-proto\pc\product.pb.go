// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pc/product.proto

package pc

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{0}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 商品查询vo
type ProductRequest struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductRequest) Reset()         { *m = ProductRequest{} }
func (m *ProductRequest) String() string { return proto.CompactTextString(m) }
func (*ProductRequest) ProtoMessage()    {}
func (*ProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{1}
}

func (m *ProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductRequest.Unmarshal(m, b)
}
func (m *ProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductRequest.Marshal(b, m, deterministic)
}
func (m *ProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductRequest.Merge(m, src)
}
func (m *ProductRequest) XXX_Size() int {
	return xxx_messageInfo_ProductRequest.Size(m)
}
func (m *ProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProductRequest proto.InternalMessageInfo

func (m *ProductRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

//商品信息返回
type ProductResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Total                int32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Message              string     `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Data                 []*Product `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ProductResponse) Reset()         { *m = ProductResponse{} }
func (m *ProductResponse) String() string { return proto.CompactTextString(m) }
func (*ProductResponse) ProtoMessage()    {}
func (*ProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{2}
}

func (m *ProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductResponse.Unmarshal(m, b)
}
func (m *ProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductResponse.Marshal(b, m, deterministic)
}
func (m *ProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductResponse.Merge(m, src)
}
func (m *ProductResponse) XXX_Size() int {
	return xxx_messageInfo_ProductResponse.Size(m)
}
func (m *ProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductResponse proto.InternalMessageInfo

func (m *ProductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ProductResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ProductResponse) GetData() []*Product {
	if m != nil {
		return m.Data
	}
	return nil
}

type Brand struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	//品牌名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	//排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// logo图片
	Logo string `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	//品牌描述
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	CreateDate  string `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	//是否推荐
	IsRecommend int32 `protobuf:"varint,7,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	//展现形式 1-图片，2-文字
	ShowType int32 `protobuf:"varint,8,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	//首字母
	Initial   string `protobuf:"bytes,9,opt,name=initial,proto3" json:"initial,omitempty"`
	CompanyId int32  `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	//所属分类id
	BrandCategoryId int32 `protobuf:"varint,11,opt,name=brand_category_id,json=brandCategoryId,proto3" json:"brand_category_id,omitempty"`
	//所属分类名称
	CategoryName         string   `protobuf:"bytes,12,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Brand) Reset()         { *m = Brand{} }
func (m *Brand) String() string { return proto.CompactTextString(m) }
func (*Brand) ProtoMessage()    {}
func (*Brand) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{3}
}

func (m *Brand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Brand.Unmarshal(m, b)
}
func (m *Brand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Brand.Marshal(b, m, deterministic)
}
func (m *Brand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Brand.Merge(m, src)
}
func (m *Brand) XXX_Size() int {
	return xxx_messageInfo_Brand.Size(m)
}
func (m *Brand) XXX_DiscardUnknown() {
	xxx_messageInfo_Brand.DiscardUnknown(m)
}

var xxx_messageInfo_Brand proto.InternalMessageInfo

func (m *Brand) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Brand) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Brand) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *Brand) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *Brand) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Brand) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *Brand) GetIsRecommend() int32 {
	if m != nil {
		return m.IsRecommend
	}
	return 0
}

func (m *Brand) GetShowType() int32 {
	if m != nil {
		return m.ShowType
	}
	return 0
}

func (m *Brand) GetInitial() string {
	if m != nil {
		return m.Initial
	}
	return ""
}

func (m *Brand) GetCompanyId() int32 {
	if m != nil {
		return m.CompanyId
	}
	return 0
}

func (m *Brand) GetBrandCategoryId() int32 {
	if m != nil {
		return m.BrandCategoryId
	}
	return 0
}

func (m *Brand) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

type Product struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	//分类id
	CategoryId int32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	//品牌id
	BrandId int32 `protobuf:"varint,3,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	//商品名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	//商品编号
	Code string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	//商品条码
	BarCode string `protobuf:"bytes,6,opt,name=bar_code,json=barCode,proto3" json:"bar_code,omitempty"`
	//商品添加日期
	CreateDate string `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	//商品最后更新日期
	UpdateDate string `protobuf:"bytes,8,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	//是否删除
	IsDel int32 `protobuf:"varint,9,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	//是否为组合商品
	IsGroup int32 `protobuf:"varint,10,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"`
	//商品图片（多图）
	Pic string `protobuf:"bytes,11,opt,name=pic,proto3" json:"pic,omitempty"`
	//商品卖点
	SellingPoint string `protobuf:"bytes,12,opt,name=selling_point,json=sellingPoint,proto3" json:"selling_point,omitempty"`
	//商品视频地址
	Video string `protobuf:"bytes,13,opt,name=video,proto3" json:"video,omitempty"`
	//电脑端详情内容
	ContentPc string `protobuf:"bytes,14,opt,name=content_pc,json=contentPc,proto3" json:"content_pc,omitempty"`
	//手机端详情内容
	ContentMobile string `protobuf:"bytes,15,opt,name=content_mobile,json=contentMobile,proto3" json:"content_mobile,omitempty"`
	//是否参与优惠折扣
	IsDiscount int32 `protobuf:"varint,16,opt,name=is_discount,json=isDiscount,proto3" json:"is_discount,omitempty"`
	//商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
	ProductType int32 `protobuf:"varint,17,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`
	//商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
	IsUse int32 `protobuf:"varint,18,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	//分类名称
	CategoryName string `protobuf:"bytes,19,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	// sku信息
	//  repeated Sku sku = 20;
	//商品属性
	//  repeated ProductAttr attr = 21;
	//渠道id
	ChannelId string `protobuf:"bytes,22,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	//品牌名称
	BrandName string `protobuf:"bytes,23,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	//京东分类id
	JdCategoryId int32 `protobuf:"varint,24,opt,name=jd_category_id,json=jdCategoryId,proto3" json:"jd_category_id,omitempty"`
	//是否为药品 0否，1是
	IsDrugs int32 `protobuf:"varint,25,opt,name=is_drugs,json=isDrugs,proto3" json:"is_drugs,omitempty"`
	// 商品应用范围（1电商，2前置仓，3门店仓）
	UseRange string `protobuf:"bytes,26,opt,name=use_range,json=useRange,proto3" json:"use_range,omitempty"`
	// 只有虚拟商品才有值(1.有效期至多少 2.有效期天数)
	TermType int32 `protobuf:"varint,27,opt,name=term_type,json=termType,proto3" json:"term_type,omitempty"`
	// 如果term_type=1 存：时间戳 如果term_type=2 存多少天
	TermValue int32 `protobuf:"varint,28,opt,name=term_value,json=termValue,proto3" json:"term_value,omitempty"`
	// 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)只有是组合商品才有值
	GroupType int32 `protobuf:"varint,29,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	// 是否支持过期退款 1：是 0：否
	VirtualInvalidRefund int32 `protobuf:"varint,30,opt,name=virtual_invalid_refund,json=virtualInvalidRefund,proto3" json:"virtual_invalid_refund,omitempty"`
	// 药品仓类型 (0:否 1：巨星药品仓)
	WarehouseType int32 `protobuf:"varint,31,opt,name=warehouse_type,json=warehouseType,proto3" json:"warehouse_type,omitempty"`
	// 是否医疗商品（0:不是 1：是，不会同步到gj商品库）
	IsIntelGoods         int32    `protobuf:"varint,32,opt,name=is_intel_goods,json=isIntelGoods,proto3" json:"is_intel_goods,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Product) Reset()         { *m = Product{} }
func (m *Product) String() string { return proto.CompactTextString(m) }
func (*Product) ProtoMessage()    {}
func (*Product) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{4}
}

func (m *Product) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Product.Unmarshal(m, b)
}
func (m *Product) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Product.Marshal(b, m, deterministic)
}
func (m *Product) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Product.Merge(m, src)
}
func (m *Product) XXX_Size() int {
	return xxx_messageInfo_Product.Size(m)
}
func (m *Product) XXX_DiscardUnknown() {
	xxx_messageInfo_Product.DiscardUnknown(m)
}

var xxx_messageInfo_Product proto.InternalMessageInfo

func (m *Product) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Product) GetCategoryId() int32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *Product) GetBrandId() int32 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *Product) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Product) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *Product) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *Product) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *Product) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *Product) GetIsDel() int32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

func (m *Product) GetIsGroup() int32 {
	if m != nil {
		return m.IsGroup
	}
	return 0
}

func (m *Product) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *Product) GetSellingPoint() string {
	if m != nil {
		return m.SellingPoint
	}
	return ""
}

func (m *Product) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *Product) GetContentPc() string {
	if m != nil {
		return m.ContentPc
	}
	return ""
}

func (m *Product) GetContentMobile() string {
	if m != nil {
		return m.ContentMobile
	}
	return ""
}

func (m *Product) GetIsDiscount() int32 {
	if m != nil {
		return m.IsDiscount
	}
	return 0
}

func (m *Product) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *Product) GetIsUse() int32 {
	if m != nil {
		return m.IsUse
	}
	return 0
}

func (m *Product) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *Product) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *Product) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *Product) GetJdCategoryId() int32 {
	if m != nil {
		return m.JdCategoryId
	}
	return 0
}

func (m *Product) GetIsDrugs() int32 {
	if m != nil {
		return m.IsDrugs
	}
	return 0
}

func (m *Product) GetUseRange() string {
	if m != nil {
		return m.UseRange
	}
	return ""
}

func (m *Product) GetTermType() int32 {
	if m != nil {
		return m.TermType
	}
	return 0
}

func (m *Product) GetTermValue() int32 {
	if m != nil {
		return m.TermValue
	}
	return 0
}

func (m *Product) GetGroupType() int32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *Product) GetVirtualInvalidRefund() int32 {
	if m != nil {
		return m.VirtualInvalidRefund
	}
	return 0
}

func (m *Product) GetWarehouseType() int32 {
	if m != nil {
		return m.WarehouseType
	}
	return 0
}

func (m *Product) GetIsIntelGoods() int32 {
	if m != nil {
		return m.IsIntelGoods
	}
	return 0
}

type WmProductCreatOrUpdateVo struct {
	// 类型 1： 新增 2 ：编辑更新
	Type int64 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 微盟的gooidsId
	GooidsId string `protobuf:"bytes,2,opt,name=gooidsId,proto3" json:"gooidsId,omitempty"`
	// 微盟的vid
	Vid                  string   `protobuf:"bytes,3,opt,name=vid,proto3" json:"vid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WmProductCreatOrUpdateVo) Reset()         { *m = WmProductCreatOrUpdateVo{} }
func (m *WmProductCreatOrUpdateVo) String() string { return proto.CompactTextString(m) }
func (*WmProductCreatOrUpdateVo) ProtoMessage()    {}
func (*WmProductCreatOrUpdateVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8f671ce25f94185, []int{5}
}

func (m *WmProductCreatOrUpdateVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WmProductCreatOrUpdateVo.Unmarshal(m, b)
}
func (m *WmProductCreatOrUpdateVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WmProductCreatOrUpdateVo.Marshal(b, m, deterministic)
}
func (m *WmProductCreatOrUpdateVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WmProductCreatOrUpdateVo.Merge(m, src)
}
func (m *WmProductCreatOrUpdateVo) XXX_Size() int {
	return xxx_messageInfo_WmProductCreatOrUpdateVo.Size(m)
}
func (m *WmProductCreatOrUpdateVo) XXX_DiscardUnknown() {
	xxx_messageInfo_WmProductCreatOrUpdateVo.DiscardUnknown(m)
}

var xxx_messageInfo_WmProductCreatOrUpdateVo proto.InternalMessageInfo

func (m *WmProductCreatOrUpdateVo) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WmProductCreatOrUpdateVo) GetGooidsId() string {
	if m != nil {
		return m.GooidsId
	}
	return ""
}

func (m *WmProductCreatOrUpdateVo) GetVid() string {
	if m != nil {
		return m.Vid
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseResponse)(nil), "pc.BaseResponse")
	proto.RegisterType((*ProductRequest)(nil), "pc.ProductRequest")
	proto.RegisterType((*ProductResponse)(nil), "pc.productResponse")
	proto.RegisterType((*Brand)(nil), "pc.Brand")
	proto.RegisterType((*Product)(nil), "pc.Product")
	proto.RegisterType((*WmProductCreatOrUpdateVo)(nil), "pc.WmProductCreatOrUpdateVo")
}

func init() { proto.RegisterFile("pc/product.proto", fileDescriptor_a8f671ce25f94185) }

var fileDescriptor_a8f671ce25f94185 = []byte{
	// 888 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x55, 0x51, 0x6f, 0x23, 0x35,
	0x10, 0x56, 0xd3, 0xa4, 0x49, 0x9c, 0x34, 0xcd, 0xf9, 0x8e, 0xc3, 0xd7, 0xbb, 0x92, 0x10, 0x8a,
	0x54, 0x21, 0xd1, 0x4a, 0x07, 0xe2, 0x89, 0xa7, 0xb6, 0xe2, 0x94, 0x07, 0xa0, 0x2c, 0xdc, 0x21,
	0x21, 0xa4, 0x95, 0xbb, 0x1e, 0x52, 0x1f, 0xbb, 0xf6, 0x62, 0x7b, 0x73, 0xea, 0x4f, 0x41, 0xfc,
	0x39, 0x7e, 0x0a, 0x9a, 0xb1, 0x37, 0x4d, 0xaf, 0xe2, 0xde, 0x3c, 0xdf, 0x37, 0xf6, 0xd8, 0xdf,
	0x7c, 0xb3, 0xcb, 0xa6, 0x75, 0x71, 0x56, 0x3b, 0xab, 0x9a, 0x22, 0x9c, 0xd6, 0xce, 0x06, 0xcb,
	0x3b, 0x75, 0xb1, 0xf8, 0x96, 0x8d, 0xcf, 0xa5, 0x87, 0x0c, 0x7c, 0x6d, 0x8d, 0x07, 0xce, 0x59,
	0xb7, 0xb0, 0x0a, 0xc4, 0xce, 0x7c, 0xe7, 0xa4, 0x97, 0xd1, 0x9a, 0x0b, 0xd6, 0xaf, 0xc0, 0x7b,
	0xb9, 0x02, 0xd1, 0x99, 0xef, 0x9c, 0x0c, 0xb3, 0x36, 0x5c, 0x1c, 0xb3, 0xc9, 0x55, 0x3c, 0x32,
	0x83, 0xbf, 0x1a, 0xf0, 0x01, 0xf7, 0x1b, 0x59, 0xc5, 0xfd, 0xc3, 0x8c, 0xd6, 0x8b, 0x35, 0x3b,
	0xa8, 0xdb, 0xac, 0x0f, 0x94, 0x79, 0xc2, 0x7a, 0xc1, 0x06, 0x59, 0x52, 0x91, 0x5e, 0x16, 0x83,
	0xed, 0xe2, 0xbb, 0xf7, 0x8a, 0xf3, 0x19, 0xeb, 0x2a, 0x19, 0xa4, 0xe8, 0xce, 0x77, 0x4f, 0x46,
	0x2f, 0x47, 0xa7, 0x75, 0x71, 0xda, 0x5e, 0x86, 0x88, 0xc5, 0xbf, 0x1d, 0xd6, 0x3b, 0x77, 0xd2,
	0x28, 0x3e, 0x61, 0x1d, 0xad, 0x52, 0xb1, 0x8e, 0x56, 0x9b, 0x5b, 0x76, 0xee, 0x6e, 0x89, 0x98,
	0xb7, 0x2e, 0x50, 0x95, 0x5e, 0x46, 0x6b, 0xc4, 0x4a, 0xbb, 0xb2, 0xa2, 0x1b, 0xf3, 0x70, 0xcd,
	0xe7, 0x6c, 0xa4, 0xc0, 0x17, 0x4e, 0xd7, 0x41, 0x5b, 0x23, 0x7a, 0x44, 0x6d, 0x43, 0x7c, 0xc6,
	0x46, 0x85, 0x03, 0x19, 0x20, 0x57, 0x32, 0x80, 0xd8, 0xa3, 0x0c, 0x16, 0xa1, 0x4b, 0x19, 0x80,
	0x7f, 0xca, 0xc6, 0xda, 0xe7, 0x0e, 0x0a, 0x5b, 0x55, 0x60, 0x94, 0xe8, 0x53, 0xc9, 0x91, 0xf6,
	0x59, 0x0b, 0xf1, 0xe7, 0x6c, 0xe8, 0x6f, 0xec, 0xbb, 0x3c, 0xdc, 0xd6, 0x20, 0x06, 0xc4, 0x0f,
	0x10, 0xf8, 0xe5, 0xb6, 0xa6, 0x86, 0x68, 0xa3, 0x83, 0x96, 0xa5, 0x18, 0x46, 0x4d, 0x52, 0xc8,
	0x8f, 0x18, 0x2b, 0x6c, 0x55, 0x4b, 0x73, 0x9b, 0x6b, 0x25, 0x18, 0xed, 0x1b, 0x26, 0x64, 0xa9,
	0xf8, 0x17, 0xec, 0xd1, 0x35, 0x0a, 0x92, 0x17, 0x32, 0xc0, 0xca, 0x3a, 0xca, 0x1a, 0x51, 0xd6,
	0x01, 0x11, 0x17, 0x09, 0x5f, 0x2a, 0xfe, 0x19, 0xdb, 0xdf, 0x64, 0x91, 0x58, 0x63, 0x2a, 0x35,
	0x6e, 0xc1, 0x1f, 0xb0, 0xb5, 0x7f, 0xf7, 0x59, 0x3f, 0x89, 0xfe, 0x40, 0x64, 0x94, 0x61, 0xab,
	0x4c, 0xec, 0x2a, 0x2b, 0xee, 0x2a, 0x3c, 0x63, 0x83, 0x78, 0x1b, 0xad, 0x92, 0xea, 0x7d, 0x8a,
	0x97, 0x77, 0x0d, 0xea, 0xde, 0x6f, 0x10, 0x79, 0x26, 0x2a, 0x1e, 0x3d, 0x83, 0x47, 0x48, 0x97,
	0x13, 0x1e, 0x75, 0xee, 0x5f, 0x4b, 0x77, 0x81, 0xd4, 0x7b, 0x5d, 0xe8, 0x3f, 0xe8, 0xc2, 0x8c,
	0x8d, 0x9a, 0x5a, 0x6d, 0x12, 0x06, 0x31, 0x21, 0x42, 0x94, 0xf0, 0x11, 0xdb, 0xd3, 0x3e, 0x57,
	0x10, 0x55, 0xee, 0x65, 0x3d, 0xed, 0x2f, 0xa1, 0xc4, 0x9a, 0xda, 0xe7, 0x2b, 0x67, 0x9b, 0x3a,
	0x29, 0xdc, 0xd7, 0xfe, 0x15, 0x86, 0x7c, 0xca, 0x76, 0x6b, 0x5d, 0x90, 0xa2, 0xc3, 0x0c, 0x97,
	0xa8, 0xa2, 0x87, 0xb2, 0xd4, 0x66, 0x95, 0xd7, 0x56, 0x9b, 0xd0, 0xaa, 0x98, 0xc0, 0x2b, 0xc4,
	0xd0, 0xf9, 0x6b, 0xad, 0xc0, 0x8a, 0x7d, 0x22, 0x63, 0x10, 0x7b, 0x69, 0x02, 0x98, 0x90, 0xd7,
	0x85, 0x98, 0x10, 0x35, 0x4c, 0xc8, 0x55, 0xc1, 0x3f, 0x67, 0x93, 0x96, 0xae, 0xec, 0xb5, 0x2e,
	0x41, 0x1c, 0x50, 0xca, 0x7e, 0x42, 0xbf, 0x27, 0x10, 0x5f, 0x89, 0x8f, 0xd0, 0xbe, 0xb0, 0x8d,
	0x09, 0x62, 0x1a, 0xbb, 0xa0, 0xfd, 0x65, 0x42, 0xd0, 0x8c, 0x69, 0x3a, 0xa3, 0xd9, 0x1e, 0x45,
	0x33, 0x26, 0x8c, 0xfc, 0x16, 0x85, 0x68, 0x3c, 0x08, 0xde, 0x0a, 0xf1, 0xda, 0xc3, 0x43, 0x87,
	0x3c, 0x7e, 0xe8, 0x10, 0x7a, 0xc5, 0x8d, 0x34, 0x06, 0x4a, 0x6c, 0xf3, 0xd3, 0xf4, 0x8a, 0x88,
	0x2c, 0x15, 0xd2, 0xd1, 0x03, 0x74, 0xc0, 0xc7, 0x91, 0x26, 0x84, 0x76, 0x1f, 0xb3, 0xc9, 0xdb,
	0xfb, 0x6e, 0x15, 0x74, 0x83, 0xf1, 0xdb, 0x6d, 0xab, 0xc6, 0x8e, 0x28, 0xd7, 0xac, 0xbc, 0x78,
	0xd6, 0x76, 0xe4, 0x12, 0x43, 0x9c, 0xa3, 0xc6, 0x43, 0xee, 0xa4, 0x59, 0x81, 0x38, 0xa4, 0xe3,
	0x07, 0x8d, 0x87, 0x0c, 0x63, 0x24, 0x03, 0xb8, 0x2a, 0xbe, 0xfb, 0x79, 0x1c, 0x32, 0x04, 0xe8,
	0xd1, 0x47, 0x8c, 0x11, 0xb9, 0x96, 0x65, 0x03, 0xe2, 0x45, 0x1c, 0x25, 0x44, 0xde, 0x20, 0x80,
	0x34, 0x59, 0x20, 0x6e, 0x3e, 0x8a, 0x34, 0x21, 0xb4, 0xfb, 0x6b, 0xf6, 0x74, 0xad, 0x5d, 0x68,
	0x64, 0x99, 0x6b, 0xb3, 0x96, 0xa5, 0x56, 0xb9, 0x83, 0x3f, 0x1a, 0xa3, 0xc4, 0x27, 0x94, 0xfa,
	0x24, 0xb1, 0xcb, 0x48, 0x66, 0xc4, 0x61, 0x4f, 0xdf, 0x49, 0x07, 0x37, 0x16, 0xef, 0x4c, 0x07,
	0xcf, 0x28, 0x7b, 0x7f, 0x83, 0xd2, 0xe1, 0xc7, 0x6c, 0xa2, 0x7d, 0xae, 0x4d, 0x80, 0x32, 0x5f,
	0x59, 0xab, 0xbc, 0x98, 0x47, 0x55, 0xb4, 0x5f, 0x22, 0xf8, 0x0a, 0xb1, 0xc5, 0xef, 0x4c, 0xfc,
	0x5a, 0xa5, 0xe1, 0xbc, 0x40, 0xdb, 0xff, 0xe8, 0x5e, 0x93, 0xb9, 0xdf, 0x58, 0x9c, 0x25, 0x3a,
	0x1e, 0xa7, 0x75, 0x37, 0xa3, 0x35, 0x3f, 0x64, 0x83, 0x95, 0xb5, 0x5a, 0xf9, 0xa5, 0x4a, 0x1f,
	0xc6, 0x4d, 0x8c, 0xc6, 0x5e, 0xa7, 0x29, 0x1d, 0x66, 0xb8, 0x7c, 0xf9, 0xcf, 0x0e, 0x9b, 0x24,
	0x8f, 0xfc, 0x0c, 0x6e, 0xad, 0x0b, 0xe0, 0xdf, 0xb0, 0xd1, 0x77, 0xda, 0xa8, 0xf6, 0x7b, 0xc0,
	0xb7, 0xbf, 0xc8, 0xf1, 0xf7, 0x70, 0xf8, 0x18, 0xb1, 0xf7, 0x7f, 0x06, 0x3f, 0xb1, 0xd9, 0xfd,
	0x8b, 0x42, 0x7b, 0xd3, 0x0b, 0x59, 0x96, 0xe7, 0xb2, 0xf8, 0x93, 0xbf, 0xc0, 0x7d, 0xff, 0xf7,
	0x9a, 0xc3, 0x29, 0xb2, 0xdb, 0xbf, 0xb1, 0xf3, 0xe9, 0x6f, 0x93, 0xd3, 0x33, 0x5b, 0xf9, 0x2f,
	0xe9, 0x4f, 0x77, 0x56, 0x17, 0xd7, 0x7b, 0xb4, 0xfa, 0xea, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x53, 0x33, 0x13, 0x9b, 0x07, 0x07, 0x00, 0x00,
}
