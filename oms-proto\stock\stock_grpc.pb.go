// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package stock

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StockServiceClient is the client API for StockService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StockServiceClient interface {
	// 冻结占用库存
	LockTakeStock(ctx context.Context, in *LockTakeStockRequest, opts ...grpc.CallOption) (*LockTakeStockResponse, error)
	// 解冻占用库存
	UnlockTakeStock(ctx context.Context, in *UnlockTakeStockRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 取消占用占用库存
	CancelTakeStock(ctx context.Context, in *CancelTakeStockRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 冻结在途库存
	LockCommingStock(ctx context.Context, in *LockCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 解冻在途库存
	UnlockCommingStock(ctx context.Context, in *UnlockCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 取消占在途库存
	CancelCommingStock(ctx context.Context, in *CancelCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error)
}

type stockServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStockServiceClient(cc grpc.ClientConnInterface) StockServiceClient {
	return &stockServiceClient{cc}
}

func (c *stockServiceClient) LockTakeStock(ctx context.Context, in *LockTakeStockRequest, opts ...grpc.CallOption) (*LockTakeStockResponse, error) {
	out := new(LockTakeStockResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/LockTakeStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stockServiceClient) UnlockTakeStock(ctx context.Context, in *UnlockTakeStockRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/UnlockTakeStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stockServiceClient) CancelTakeStock(ctx context.Context, in *CancelTakeStockRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/CancelTakeStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stockServiceClient) LockCommingStock(ctx context.Context, in *LockCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/LockCommingStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stockServiceClient) UnlockCommingStock(ctx context.Context, in *UnlockCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/UnlockCommingStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stockServiceClient) CancelCommingStock(ctx context.Context, in *CancelCommingStockRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/stock.StockService/CancelCommingStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StockServiceServer is the server API for StockService service.
// All implementations should embed UnimplementedStockServiceServer
// for forward compatibility
type StockServiceServer interface {
	// 冻结占用库存
	LockTakeStock(context.Context, *LockTakeStockRequest) (*LockTakeStockResponse, error)
	// 解冻占用库存
	UnlockTakeStock(context.Context, *UnlockTakeStockRequest) (*CommonResponse, error)
	// 取消占用占用库存
	CancelTakeStock(context.Context, *CancelTakeStockRequest) (*CommonResponse, error)
	// 冻结在途库存
	LockCommingStock(context.Context, *LockCommingStockRequest) (*CommonResponse, error)
	// 解冻在途库存
	UnlockCommingStock(context.Context, *UnlockCommingStockRequest) (*CommonResponse, error)
	// 取消占在途库存
	CancelCommingStock(context.Context, *CancelCommingStockRequest) (*CommonResponse, error)
}

// UnimplementedStockServiceServer should be embedded to have forward compatible implementations.
type UnimplementedStockServiceServer struct {
}

func (UnimplementedStockServiceServer) LockTakeStock(context.Context, *LockTakeStockRequest) (*LockTakeStockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LockTakeStock not implemented")
}
func (UnimplementedStockServiceServer) UnlockTakeStock(context.Context, *UnlockTakeStockRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockTakeStock not implemented")
}
func (UnimplementedStockServiceServer) CancelTakeStock(context.Context, *CancelTakeStockRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTakeStock not implemented")
}
func (UnimplementedStockServiceServer) LockCommingStock(context.Context, *LockCommingStockRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LockCommingStock not implemented")
}
func (UnimplementedStockServiceServer) UnlockCommingStock(context.Context, *UnlockCommingStockRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockCommingStock not implemented")
}
func (UnimplementedStockServiceServer) CancelCommingStock(context.Context, *CancelCommingStockRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelCommingStock not implemented")
}

// UnsafeStockServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StockServiceServer will
// result in compilation errors.
type UnsafeStockServiceServer interface {
	mustEmbedUnimplementedStockServiceServer()
}

func RegisterStockServiceServer(s grpc.ServiceRegistrar, srv StockServiceServer) {
	s.RegisterService(&StockService_ServiceDesc, srv)
}

func _StockService_LockTakeStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockTakeStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).LockTakeStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/LockTakeStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).LockTakeStock(ctx, req.(*LockTakeStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StockService_UnlockTakeStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockTakeStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).UnlockTakeStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/UnlockTakeStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).UnlockTakeStock(ctx, req.(*UnlockTakeStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StockService_CancelTakeStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelTakeStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).CancelTakeStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/CancelTakeStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).CancelTakeStock(ctx, req.(*CancelTakeStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StockService_LockCommingStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockCommingStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).LockCommingStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/LockCommingStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).LockCommingStock(ctx, req.(*LockCommingStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StockService_UnlockCommingStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockCommingStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).UnlockCommingStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/UnlockCommingStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).UnlockCommingStock(ctx, req.(*UnlockCommingStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StockService_CancelCommingStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelCommingStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StockServiceServer).CancelCommingStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/stock.StockService/CancelCommingStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StockServiceServer).CancelCommingStock(ctx, req.(*CancelCommingStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StockService_ServiceDesc is the grpc.ServiceDesc for StockService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StockService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stock.StockService",
	HandlerType: (*StockServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LockTakeStock",
			Handler:    _StockService_LockTakeStock_Handler,
		},
		{
			MethodName: "UnlockTakeStock",
			Handler:    _StockService_UnlockTakeStock_Handler,
		},
		{
			MethodName: "CancelTakeStock",
			Handler:    _StockService_CancelTakeStock_Handler,
		},
		{
			MethodName: "LockCommingStock",
			Handler:    _StockService_LockCommingStock_Handler,
		},
		{
			MethodName: "UnlockCommingStock",
			Handler:    _StockService_UnlockCommingStock_Handler,
		},
		{
			MethodName: "CancelCommingStock",
			Handler:    _StockService_CancelCommingStock_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "stock/stock.proto",
}
