package dto

type MpPushOrderStatus struct {
	//配送活动标识
	DeliveryId int64 `json:"delivery_id" form:"delivery_id" query:"delivery_id"`
	//美团配送内部订单id，最长不超过32个字符
	MtPeisongId string `json:"mt_peisong_id" form:"mt_peisong_id" query:"mt_peisong_id"`
	//外部订单号，最长不超过32个字符
	OrderId string`json:"order_id" form:"order_id" query:"order_id"`
	//状态代码，可选值为
	//0：待调度
	//20：已接单
	//30：已取货
	//50：已送达
	//99：已取消
	//回调接口的订单状态改变可能会跳过中间状态，比如从待调度状态直接变为已取货状态。
	//订单状态不会回流。即订单不会从已取货状态回到待调度状态。
	//订单状态为“已接单”和“已取货”时，如果当前骑手不能完成配送，会出现改派操作，例如：将订单从骑手A改派给骑手B，由骑手B完成后续配送，因此会出现同一订单多次返回同一状态不同骑手信息的情况”
	Status int32 `json:"status" form:"status" query:"status"`
	//配送员姓名（已接单，已取货状态的订单，配送员信息可能改变）
	CourierName string `json:"courier_name" form:"courier_name" query:"courier_name"`
	//配送员电话（已接单，已取货状态的订单，配送员信息可能改变）
	CourierPhone string `json:"courier_phone" form:"courier_phone" query:"courier_phone"`
	//取消原因id
	CancelReasonId int32 `json:"cancel_reason_id" form:"cancel_reason_id" query:"cancel_reason_id"`
	//取消原因详情，最长不超过256个字符
	CancelReason string `json:"cancel_reason" form:"cancel_reason" query:"cancel_reason"`
	//预计送达时间(注：只有“自由达”服务的订单状态回调有此字段)
	//即时单：只有骑手接单后，才会确定预计送达时间，因此状态为“已接单”、“已取货”、“已送达”时，此字段为非 0 值，其它状态下此值为 0；
	//预约单：下单成功即可确定预计送达时间，并且预计送达时间就是用户下单时传入的期望送达时间；
	//注：格式为 unix-timestamp，若预计送达时间还未确
	//定时，字段的值默认为 0；
	PredictDeliveryTime string `json:"predict_delivery_time" form:"predict_delivery_time" query:"predict_delivery_time"`
	//开放平台分配的appkey，合作方唯一标识。
	AppKey string `json:"appkey" form:"appkey" query:"appkey"`
	//	时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
	Timestamp int64 `json:"timestamp" form:"timestamp" query:"timestamp"`
	//数据签名
	Sign string `json:"sign" form:"sign" query:"sign"`
}
