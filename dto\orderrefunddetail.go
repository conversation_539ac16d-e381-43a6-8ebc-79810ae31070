package dto

type OrderRefundDetail struct {
	Data []*OrderDetailList `json:"data"`
}

type RefundPartialEstimateCharge struct {
	//本次退款商品的总金额，不含包装盒费，单商品总价=商品原价+赠品原价，位元
	TotalFoodAmount string `protobuf:"bytes,1,opt,name=total_food_amount,json=totalFoodAmount,proto3" json:"total_food_amount"`
	//本次退款商品包装盒费总价，单位元
	BoxAmount string `protobuf:"bytes,2,opt,name=box_amount,json=boxAmount,proto3" json:"box_amount"`
	//本次退款商家活动费用总支出成本，含赠品成本，单位元
	ActivityPoiAmount string `protobuf:"bytes,3,opt,name=activity_poi_amount,json=activityPoiAmount,proto3" json:"activity_poi_amount"`
	//本次退款美团活动补贴总金额，单位元
	ActivityMeituanAmount string `protobuf:"bytes,4,opt,name=activity_meituan_amount,json=activityMeituanAmount,proto3" json:"activity_meituan_amount"`
	//本次退款代理商活动承担金额，单位元
	ActivityAgentAmount string `protobuf:"bytes,5,opt,name=activity_agent_amount,json=activityAgentAmount,proto3" json:"activity_agent_amount"`
	//本次退款平台服务费总金额，单位元
	PlatformChargeFee string `protobuf:"bytes,6,opt,name=platform_charge_fee,json=platformChargeFee,proto3" json:"platform_charge_fee"`
	//结算金额，单位元
	SettleAmount string `protobuf:"bytes,7,opt,name=settle_amount,json=settleAmount,proto3" json:"settle_amount"`
	//商家活动支出分摊到商品上的优惠总金额，单位元
	Productpreferences string `protobuf:"bytes,8,opt,name=productpreferences,proto3" json:"productpreferences"`
}

type OrderDetailList struct {
	//订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
	WmOrderIdView int64 `protobuf:"varint,1,opt,name=wm_order_id_view,json=wmOrderIdView,proto3" json:"wm_order_id_view"`
	// 订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//   退款id，每次发起部分退款的退款id不同。
	RefundId int64 `protobuf:"varint,3,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	// 退款申请发起时间，为10位秒级的时间戳。
	Ctime int32 `protobuf:"varint,4,opt,name=ctime,proto3" json:"ctime"`
	// 退款申请处理时间，为10位秒级的时间戳。如为商家主动发起的退款，退款申请的发起和处理时间相同；如用户申请后商家还未处理，此字段信息与ctime相同。
	Utime int32 `protobuf:"varint,5,opt,name=utime,proto3" json:"utime"`
	// 退款类型：1-全额退款；2-部分退款；3-退差价。
	RefundType int32 `protobuf:"varint,6,opt,name=refund_type,json=refundType,proto3" json:"refund_type"`
	//  商品库存不足，已于用户沟通退一件。
	//  商家处理退款时答复的内容
	ResReason string `protobuf:"bytes,7,opt,name=res_reason,json=resReason,proto3" json:"res_reason"`
	// 答复类型：0-未处理；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；
	//4-客服帮商家同意退款；5-超过3小时自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
	ResType int32 `protobuf:"varint,8,opt,name=res_type,json=resType,proto3" json:"res_type"`
	//   申请类型：0-订单取消自动确认退款； 1-用户申请退款； 2-客服帮用户申请退款； 3-重复提交而自动申请；
	//4-支付成功消息在订单取消之后到达而自动申请； 5-支付成功消息在订单被置为无效之后到达而自动申请； 6-用户被商家拒绝后申诉；7-商家申请退款。
	ApplyType int32 `protobuf:"varint,9,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	//  商家联系我说没货了
	//  申请退款的原因
	ApplyReason string `protobuf:"bytes,10,opt,name=apply_reason,json=applyReason,proto3" json:"apply_reason"`
	//   退款金额合计，单位是元，此字段信息为本次退款商品的总金额。
	//如本次退款为全额退款，则此字段信息为订单原始在线实付金额减去已部分退款的金额后，当前剩余的订单总金额。
	Money float64 `protobuf:"fixed64,11,opt,name=money,proto3" json:"money"`
	// 退款图片url的json格式数组，用户在申请退款时上传的退款图片，多张以英文逗号隔开，上限为9张。
	Pictures string `protobuf:"bytes,12,opt,name=pictures,proto3" json:"pictures"`
	// 部分退款的商品明细，适用于按件部分退和按克重退差价两种类型。
	WmAppRetailForOrderPartRefundList []*WmAppRetailForOrderPartRefundList `protobuf:"bytes,13,rep,name=wmAppRetailForOrderPartRefundList,proto3" json:"wmAppRetailForOrderPartRefundList"`
	//退款服务类型, 区分是否已开通退货退款售后业务。
	//未开通的场景：
	//0-退款流程或申诉流程
	//已开通场景：
	//1-仅退款流程
	//2-退款退货流程
	ServiceType int32 `protobuf:"varint,14,opt,name=service_type,json=serviceType,proto3" json:"service_type"`
	// 推送当前售后单的状态类型，仅适用支持退货退款业务的商家：
	//  1-已申请
	//  10-初审已同意
	//  11-初审已驳回
	//  16-初审已申诉
	//  17-初审申诉已同意
	//  18-初审申诉已驳回
	//  20-终审已发起（用户已发货）
	//  21-终审已同意
	//  22-终审已驳回
	//  26-终审已申诉
	//  27-终审申诉已同意
	//  28-终审申诉已驳回
	//  30-已取消
	Status int32 `protobuf:"varint,15,opt,name=status,proto3" json:"status"`
	//   推送当前退款或退货退款流程的发起方，仅适用于支持退货退款的商家。
	ApplyOpUserType int32 `protobuf:"varint,16,opt,name=apply_op_user_type,json=applyOpUserType,proto3" json:"apply_op_user_type"`
	// 物流信息集合，仅适用支持退货退款业务的品类；在用户提交物流信息以及商家终审的推送消息中展示。
	LogisticsInfo *LogisticsInfo `protobuf:"bytes,17,opt,name=logistics_info,json=logisticsInfo,proto3" json:"logistics_info"`
	//订单数据状态标记。当订单中部分字段的数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
	//-1：有数据降级
	//0：无数据降级
	IncmpCode    int32   `protobuf:"varint,18,opt,name=incmp_code,json=incmpCode,proto3" json:"incmp_code"`
	IncmpModules []int32 `protobuf:"varint,19,rep,packed,name=incmp_modules,json=incmpModules,proto3" json:"incmp_modules"`
	//本次退款美团活动补贴总金额，单位元 平台承担的优惠活动金额
	ActivityPtAmount            float64                      `protobuf:"fixed64,20,opt,name=activity_meituan_amount,proto3" json:"activity_meituan_amount"`
	RefundPartialEstimateCharge *RefundPartialEstimateCharge `protobuf:"bytes,21,opt,name=refund_partial_estimate_charge,json=RefundPartialEstimateCharge,proto3" json:"refund_partial_estimate_charge"`
}

// 部分退款的商品明细，适用于按件部分退和按克重退差价两种类型。
type WmAppRetailForOrderPartRefundList struct {
	//     APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
	AppFoodCode string `protobuf:"bytes,1,opt,name=app_food_code,json=appFoodCode,proto3" json:"app_food_code"`
	//  商品名称
	FoodName string `protobuf:"bytes,2,opt,name=food_name,json=foodName,proto3" json:"food_name"`
	//  商品sku唯一标识码，字段信息限定长度不超过40个字符。
	SkuId string `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//  商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
	Upc string `protobuf:"bytes,4,opt,name=upc,proto3" json:"upc"`
	// 商品sku的规格名称
	Spec string `protobuf:"bytes,5,opt,name=spec,proto3" json:"spec"`
	// 本次退款的商品数量：(1)如为按件部分退款，此字段信息为本次退款商品sku的数量。(2)如为按克重退差价，此字段信息为0。
	Count int32 `protobuf:"varint,6,opt,name=count,proto3" json:"count"`
	//  商品sku单件需使用打包盒的数量。（商品维度，在创建/更新商品时维护的信息）
	BoxNum float64 `protobuf:"fixed64,7,opt,name=box_num,json=boxNum,proto3" json:"box_num"`
	//  商品sku的单个打包盒的价格，单位是元。
	BoxPrice float64 `protobuf:"fixed64,8,opt,name=box_price,json=boxPrice,proto3" json:"box_price"`
	// 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
	FoodPrice float64 `protobuf:"fixed64,9,opt,name=food_price,json=foodPrice,proto3" json:"food_price"`
	//  商品sku优惠前原价(单价)，单位是元。此字段信息为当前订单中单件商品sku的原价。
	OriginFoodPrice float64 `protobuf:"fixed64,10,opt,name=origin_food_price,json=originFoodPrice,proto3" json:"origin_food_price"`
	//  退款价格（单价），单位是元。此字段信息为当前订单中单件此商品sku的退款价格，是单价。(1)如购买多件商品sku，仅1件享优惠价，计算时商品优惠金额会进行等比分摊。
	// (2)如商品是按克重退差价，refund_price字段信息是计算优惠分摊后，单件商品sku重量差异部分的价格。
	RefundPrice float64 `protobuf:"fixed64,11,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price"`
	//  商品sku的已退重量，单位是克/g。此字段仅适用于退差价类型，为商品sku标价重量与实拣重量的差异重量。
	RefundedWeight float64 `protobuf:"fixed64,12,opt,name=refunded_weight,json=refundedWeight,proto3" json:"refunded_weight"`
}
