syntax = "proto3";

package helloworld;

option go_package = "./oms-proto/helloworld";

// import "google/api/annotations.proto";

// Here is the overall greeting service definition where we define all our
// endpoints
service Greeter {
  // Sends a greeting
  rpc <PERSON><PERSON><PERSON>(HelloRequest) returns (HelloReply) {
    // option (google.api.http) = {
    //   post : "/v1/example/echo"
    //   body : "*"
    // };
  }
}

// The request message containing the user's name
message HelloRequest { string name = 1; }

// The response message containing the greetings
message HelloReply { string message = 1; }
