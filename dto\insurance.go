package dto

type InsuranceBaseResponse struct {
	//外部业务流水号
	BusinessNo string `json:"businessNo"`
	//消息代码
	ErrCode int `json:"errCode"`
	//结果消息
	ErrMsg string `json:"errMsg"`
}

type InsuranceOnLineRequest struct {
	//外部业务流水号
	BusinessNo string `json:"businessNo"`
	//保单号
	PolicyNo string `json:"policyNo"`
	//支付申请号
	PayApplyNo string `json:"payApplyNo"`
	//支付金额
	PayAmount int `json:"payAmount"`
	//支付时间
	PayTime string `json:"payTime"`
	//电子保单地址
	ElecPolicyUrl string `json:"elecPolicyUrl"`
	//总保额
	TotalInsuredAmt int `json:"totalInsuredAmt"`
	//实付总保费
	ActualPremiumAmt int `json:"actualPremiumAmt"`
	//保险起始时间
	InsuredBgnTime string `json:"insuredBgnTime"`
	//保险截止时间
	InsuredEndTime string `json:"insuredEndTime"`
	//投保人
	Holder Holder `json:"holder"`
	//被保人
	Insureds []Insureds `json:"insureds"`

	Target Target `json:"target"`
}

type InsuranceOnLineRequestPar struct {
	//外部业务流水号
	BusinessNo string `json:"businessNo"`
	//保单号
	PolicyNo string `json:"policyNo"`
	//支付申请号
	PayApplyNo string `json:"payApplyNo"`
	//支付金额
	PayAmount int `json:"payAmount"`
	//支付时间
	PayTime string `json:"payTime"`
	//电子保单地址
	ElecPolicyUrl string `json:"elecPolicyUrl"`
	//总保额
	TotalInsuredAmt int `json:"totalInsuredAmt"`
	//实付总保费
	ActualPremiumAmt int `json:"actualPremiumAmt"`
	//保险起始时间
	InsuredBgnTime string `json:"insuredBgnTime"`
	//保险截止时间
	InsuredEndTime string `json:"insuredEndTime"`
	//投保人
	Holder Holder `json:"holder"`
	//被保人
	Insureds []Insureds `json:"insureds"`

	Target Target `json:"target"`
}

//宠物等信息
type Target struct {
	//养犬许可证号码
	DogLicenseCode string `json:"dogLicenseCode"`
	//犬类免疫证号码
	ImmunityCertifiCode string `json:"immunityCertifiCode"`
	//宠物犬种类
	PetDogBreed string `json:"petDogBreed"`
	//所在城市
	HouseCity string `json:"houseCity"`
	//详细地址
	HouseAddress string `json:"houseAddress"`
	//宠物名称
	PetName string `json:"petName"`
	//1-猫，2-犬
	Category string `json:"category"`
	//小的分类名称 种类
	CategoryName string `json:"categoryName"`
	//出生日期
	Birthday string `json:"birthday"`
	//宠物性别
	Gender string `json:"gender"`
	//是否绝育
	Sterilization bool `json:"sterilization"`
	//是否免疫
	Immune bool `json:"immune"`
	//投保照片ZIP压缩包的base64格式
	Base64Str string `json:"base64Str"`
	//业务模式
	BizMode string `json:"bizMode"`
}

//投保人
type Holder struct {
	//投保人名称
	HolderName string `json:"holderName"`
	//证件类型
	IdcartType string `json:"idcartType"`
	//证件号码
	IdcartNo string `json:"idcartNo"`
	//投保人类型
	HolderType string `json:"holderType"`
	//手机号
	Mobile string `json:"mobile"`
	//出生日期
	BornDate string `json:"bornDate"`
	//性别
	//M	男
	//F	女
	Sex string `json:"sex"`
	//固话
	Telphone string `json:"telphone"`
	//邮箱
	Mail string `json:"mail"`
}

//被保人
type Insureds struct {
	//被保人名称
	InsuredName string `json:"insuredName"`
	//证件类型
	// 01	身份证
	//02	护照
	//03	军人证
	//04	学生证
	//05	台胞证
	//06	港澳返乡证
	//07	出生证
	//08	出生日期（未成年人使用）
	//09	统一社会信用代码
	//13	纳税人识别号
	//14	外国人永久居留身份证
	//99	其他
	IdcartType string `json:"idcartType"`
	//证件号码
	IdcartNo string `json:"idcartNo"`
	//出生日期
	BornDate string `json:"bornDate"`
	//性别
	//M	男
	//F	女
	Sex string `json:"sex"`
	//被保人与投保人关系
	//0	本人
	//1	配偶
	//2	父母
	//3	子女
	//4	兄弟姐妹
	//5	雇佣
	//6	监护人
	//7	被监护人
	//8	抚养
	//9	赡养
	//10	朋友
	//11	亲属
	//12	法定继承人
	//13	身故受益人
	//14	其他
	RelationWithHolder string `json:"relationWithHolder"`
	//手机号
	Mobile string `json:"mobile"`
}
