package controller

import (
	"net/http"
	"strings"

	"external-ui/dto"
	"external-ui/pkg/code"
	"external-ui/proto/et"
	"external-ui/services"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 订单确认退款请求
// @Tags 美团模拟测试
// @Accept json
// @Produce json
// @Param product body dto.MpOederRefund true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mt-test/order-refund-Agree [POST]
func MtOrderRefundAgree(c echo.Context) error {
	model := new(dto.MpOederRefund)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	var logbuild strings.Builder
	logbuild.WriteString("美团订单确认退款请求参数：" + kit.JsonEncode(model)) // 请求参数

	etClient := et.GetExternalClient()
	defer etClient.Close()

	var params = et.MtOrderRefundRequest{}
	params.OrderId = model.OrderId
	params.Reason = model.Reason

	storeMasterId, err := services.GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Error("MtOrderRefundAgree,", "GetAppChannelByOrderSn", model.OrderId, err)
		return c.JSON(http.StatusBadRequest, et.ExternalResponse{
			Message: "获取店铺主体id失败",
		})
	}
	params.StoreMasterId = storeMasterId

	res, err := etClient.MtOrder.MtOrderRefundAgree(etClient.Ctx, &params)
	if err != nil {
		logbuild.WriteString("美团订单确认退款处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(500, res)
	}
	if res.Code != 200 {
		logbuild.WriteString("美团订单确认退款处理结果：" + res.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, res)
	}
	logbuild.WriteString("美团订单确认退款处理结果：成功") // 处理结果
	glog.Info(logbuild.String())            // 记录日志
	return c.JSON(200, res)
}

// @Summary 订单驳回退款请求
// @Tags 美团模拟测试
// @Accept json
// @Produce json
// @Param product body dto.MpOederRefund true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mt-test/order-refund-reject [POST]
func MtOrderRefundReject(c echo.Context) error {
	model := new(dto.MpOederRefund)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	var logbuild strings.Builder
	logbuild.WriteString("美团订单驳回退款请求参数：" + kit.JsonEncode(model)) // 请求参数

	etClient := et.GetExternalClient()
	defer etClient.Close()

	var params = et.MtOrderRefundRequest{}
	params.OrderId = model.OrderId
	params.Reason = model.Reason

	storeMasterId, err := services.GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Error("MtOrderRefundReject,", "GetAppChannelByOrderSn", model.OrderId, err)
		logbuild.WriteString("美团订单驳回退款处理结果：" + "获取店铺主体信息失败")
		return c.JSON(http.StatusBadRequest, dto.ExternalResponse{Code: 400, Message: "获取店铺主体信息失败"})
	}
	params.StoreMasterId = storeMasterId

	res, err := etClient.MtOrder.MtOrderRefundReject(etClient.Ctx, &params)
	if err != nil {
		logbuild.WriteString("美团订单驳回退款处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(500, res)
	}
	if res.Code != 200 {
		logbuild.WriteString("美团订单驳回退款处理结果：" + res.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, res)
	}
	logbuild.WriteString("美团订单驳回退款处理结果：成功") // 处理结果
	glog.Info(logbuild.String())            // 记录日志
	return c.JSON(200, res)
}

// @Summary 商家订单取消
// @Tags 美团模拟测试
// @Accept json
// @Produce json
// @Param product body dto.MpOederRefund true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mt-test/order-cancel [POST]
func MtOrderCancel(c echo.Context) error {
	model := new(dto.MpOederRefund)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	var logbuild strings.Builder
	logbuild.WriteString("商家取消订单请求参数：" + kit.JsonEncode(model)) // 请求参数

	etClient := et.GetExternalClient()
	defer etClient.Close()

	var params = et.MtOrderCancelRequest{}
	params.OrderId = model.OrderId
	params.Reason = model.Reason
	storeMasterId, err := services.GetAppChannelByOrderSn(params.OrderId)
	if err != nil {
		glog.Error("MtOrderCancel", "GetAppChannelByOrderSn", params.OrderId, err)
		return c.JSON(400, et.ExternalResponse{
			Message: "GetAppChannelByOrderSn failed",
		})
	}
	params.StoreMasterId = storeMasterId

	res, err := etClient.MtOrder.MtOrderCancel(etClient.Ctx, &params)
	if err != nil {
		logbuild.WriteString("商家取消订单处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(500, res)
	}
	if res.Code != 200 {
		logbuild.WriteString("商家取消订单处理结果：" + res.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, res)
	}
	logbuild.WriteString("商家取消订单处理结果：成功") // 处理结果
	glog.Info(logbuild.String())          // 记录日志
	return c.JSON(200, res)
}

// @Summary 拉取用户真实手机号
// @Tags 美团模拟测试
// @Accept json
// @Produce json
// @Param product body dto.MpOederRefund true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mt-test/phonenumber-user [POST]
func MtUserInfoPhoneNumber(c echo.Context) error {
	model := new(dto.MtPullPhoneNumber)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	var logbuild strings.Builder
	logbuild.WriteString("拉取用户真实手机号请求参数：" + kit.JsonEncode(model)) // 请求参数

	var params = et.MtUserInfoPhoneNumberRequest{}
	params.AppPoiCode = model.AppPoiCode
	params.Limit = model.Limit
	params.Offset = model.Offset

	storeMasterId, retCode := services.GetAppChannelByStoreId(model.AppPoiCode)
	if retCode != code.Success {
		glog.Error("external-ui:MtUserInfoPhoneNumber", "GetAppChannelByStoreId,", kit.JsonEncode(model), retCode)
		return c.JSON(400, dto.ExternalResponse{Message: "获取店铺主体Id失败"})
	}
	params.StoreMasterId = storeMasterId

	etClient := et.GetExternalClient()
	defer etClient.Close()

	res, err := etClient.MtOrder.MtUserInfoPhoneNumber(etClient.Ctx, &params)
	if err != nil {
		logbuild.WriteString("拉取用户真实手机号处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(500, res)
	}
	if res.Code != 200 {
		logbuild.WriteString("拉取用户真实手机号处理结果：" + res.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, res)
	}

	logbuild.WriteString("拉取用户真实手机号处理结果：成功") // 处理结果
	glog.Info(logbuild.String())             // 记录日志
	return c.JSON(200, res)
}

// @Summary 拉取骑手真实手机号
// @Tags 美团模拟测试
// @Accept json
// @Produce json
// @Param product body dto.MpOederRefund true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mt-test/phonenumber-rider [POST]
func MtRiderInfoPhoneNumber(c echo.Context) error {
	model := new(dto.MtPullPhoneNumber)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}

	var logbuild strings.Builder
	logbuild.WriteString("拉取骑手真实手机号请求参数：" + kit.JsonEncode(model)) // 请求参数

	etClient := et.GetExternalClient()
	defer etClient.Close()

	var params = et.MtRiderInfoPhoneNumberRequest{}
	params.AppPoiCode = model.AppPoiCode
	params.Limit = model.Limit
	params.Offset = model.Offset

	storeMasterId, retCode := services.GetAppChannelByStoreId(model.AppPoiCode)
	if retCode != code.Success {
		glog.Error("external-ui:MtUserInfoPhoneNumber", "GetAppChannelByFinanceCode,", kit.JsonEncode(model), retCode)
		return c.JSON(400, dto.ExternalResponse{Message: "获取店铺主体Id失败"})
	}
	params.StoreMasterId = storeMasterId

	res, err := etClient.MtOrder.MtRiderInfoPhoneNumber(etClient.Ctx, &params)
	if err != nil {
		logbuild.WriteString("拉取骑手真实手机号处理结果：" + err.Error())
		glog.Error(logbuild.String())
		return c.JSON(500, res)
	}
	if res.Code != 200 {
		logbuild.WriteString("拉取骑手真实手机号处理结果：" + res.Error)
		glog.Error(logbuild.String())
		return c.JSON(400, res)
	}

	logbuild.WriteString("拉取骑手真实手机号处理结果：成功") // 处理结果
	glog.Info(logbuild.String())             // 记录日志
	return c.JSON(200, res)
}
