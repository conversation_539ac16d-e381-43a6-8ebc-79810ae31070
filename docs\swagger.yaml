definitions:
  cc.CardOrderValueData:
    properties:
      free_quality:
        description: 赠送价值，单位：分
        type: integer
      order_sn:
        description: 会员卡订单id
        type: string
    type: object
  cc.CardOrderValueReq:
    properties:
      order_sn:
        description: 卡id
        items:
          type: string
        type: array
    type: object
  cc.CardOrderValueResp:
    properties:
      code:
        description: 状态码，200正常，非200错误
        type: integer
      data:
        items:
          $ref: '#/definitions/cc.CardOrderValueData'
        type: array
      message:
        description: 消息
        type: string
    type: object
  cc.CardValueData:
    properties:
      coupon_code:
        description: 券编码
        type: string
      equity_name:
        description: 权益名称
        type: string
      free_quality:
        description: 权益价值
        type: integer
    type: object
  cc.CardValueReq:
    properties:
      card_id:
        description: 卡id
        type: integer
      scrm_id:
        description: 用户id
        type: string
    type: object
  cc.CardValueRes:
    properties:
      code:
        description: 状态码，200正常，非200错误
        type: integer
      data:
        items:
          $ref: '#/definitions/cc.CardValueData'
        type: array
      message:
        description: 消息
        type: string
    type: object
  controller.RefundOrderPayData:
    properties:
      backParam:
        description: 商户私有域：交易返回时原样返回给商户网站，给商户备用
        type: string
      callbackUrl:
        description: 后台回调地址
        type: string
      clientIP:
        description: 客户端 IP ：如 127.0.0.1
        type: string
      extendInfo:
        description: 扩展信息：预留字段，JSON 格式
        type: string
      refundAmt:
        description: 退款金额，以分为单位
        type: string
      refundId:
        description: 退款订单号
        type: string
      rspCode:
        description: 返回状态码 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
        type: string
      rspMessage:
        description: 返回信息
        type: string
      sign:
        description: 签名
        type: string
      transactionNo:
        description: 交易流水号
        type: string
    type: object
  dto.Compensation:
    properties:
      amount:
        description: 赔付金额，单位是元
        type: string
      app_poi_code:
        description: APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
        type: integer
      order_id:
        description: 订单号
        type: integer
      pictures:
        description: 赔付凭证，推送图片url列表，多张图片url以英文逗号隔开。
        type: string
      reason:
        description: 赔付原因说明
        type: string
      time:
        description: 赔付时间，为13位毫秒级的时间戳。
        type: integer
    type: object
  dto.Data:
    properties:
      product_info:
        items:
          $ref: '#/definitions/dto.NewProductInfo'
        type: array
      struct_code:
        items:
          type: string
        type: array
    type: object
  dto.ExternalResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: json 格式数据
        type: string
      error:
        description: 错误信息
        type: string
      external_code:
        description: 外部接口返回错误码（例如美配，美团）
        type: string
      message:
        description: 消息
        type: string
    type: object
  dto.GetProductPriceInfo:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/dto.Data'
        type: array
      msg:
        type: string
      status:
        type: integer
    type: object
  dto.IssOrderStatus:
    properties:
      abortReason:
        description: 取消原因
        type: string
      abortType:
        description: 取消类型 1：因客户取消 商家取消也会返回1 ；3：因闪送员取消 10：闪送系统自动取消
        type: integer
      courier:
        $ref: '#/definitions/dto.IssOrderStatusCourier'
        description: 送货员小哥哥的信息
      deductAmount:
        description: 取消订单情况下的扣款金额
        type: integer
      deliveryPassword:
        type: string
      issOrderNo:
        description: 闪送订单
        type: string
      orderNo:
        description: 我方订单号
        type: string
      pickupPassword:
        type: string
      status:
        description: 配送状态 20：派单中 30：取货中 40：闪送中 50：已完成 60：已取消
        type: integer
      statusDesc:
        description: 配送状态说明
        type: string
    type: object
  dto.IssOrderStatusCourier:
    properties:
      estimateDeliveryTimeTip:
        type: string
      latitude:
        type: string
      longitude:
        type: string
      mobile:
        type: string
      name:
        type: string
      serviceTimes:
        type: integer
    type: object
  dto.IssOrderStatusResponse:
    properties:
      data:
        description: 数据
      msg:
        description: 结果信息
        type: string
      status:
        description: 状态码
        type: integer
    type: object
  dto.MpOederRefund:
    properties:
      order_id:
        description: 外部订单号，最长不超过32个字符
        type: string
      reason:
        description: 原因
        type: string
    type: object
  dto.MpPushOrderStatus:
    properties:
      appkey:
        description: 开放平台分配的appkey，合作方唯一标识。
        type: string
      cancel_reason:
        description: 取消原因详情，最长不超过256个字符
        type: string
      cancel_reason_id:
        description: 取消原因id
        type: integer
      courier_name:
        description: 配送员姓名（已接单，已取货状态的订单，配送员信息可能改变）
        type: string
      courier_phone:
        description: 配送员电话（已接单，已取货状态的订单，配送员信息可能改变）
        type: string
      delivery_id:
        description: 配送活动标识
        type: integer
      mt_peisong_id:
        description: 美团配送内部订单id，最长不超过32个字符
        type: string
      order_id:
        description: 外部订单号，最长不超过32个字符
        type: string
      predict_delivery_time:
        description: |-
          预计送达时间(注：只有“自由达”服务的订单状态回调有此字段)
          即时单：只有骑手接单后，才会确定预计送达时间，因此状态为“已接单”、“已取货”、“已送达”时，此字段为非 0 值，其它状态下此值为 0；
          预约单：下单成功即可确定预计送达时间，并且预计送达时间就是用户下单时传入的期望送达时间；
          注：格式为 unix-timestamp，若预计送达时间还未确
          定时，字段的值默认为 0；
        type: string
      sign:
        description: 数据签名
        type: string
      status:
        description: |-
          状态代码，可选值为
          0：待调度
          20：已接单
          30：已取货
          50：已送达
          99：已取消
          回调接口的订单状态改变可能会跳过中间状态，比如从待调度状态直接变为已取货状态。
          订单状态不会回流。即订单不会从已取货状态回到待调度状态。
          订单状态为“已接单”和“已取货”时，如果当前骑手不能完成配送，会出现改派操作，例如：将订单从骑手A改派给骑手B，由骑手B完成后续配送，因此会出现同一订单多次返回同一状态不同骑手信息的情况”
        type: integer
      timestamp:
        description: 时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
        type: integer
    type: object
  dto.MpPushOrderUnusual:
    properties:
      appkey:
        description: 开放平台分配的appkey，合作方唯一标识。
        type: string
      courier_name:
        description: 上报订单异常的配送员姓名
        type: string
      courier_phone:
        description: 上报订单异常的配送员电话
        type: string
      delivery_id:
        description: 配送活动标识
        type: integer
      exception_code:
        description: |-
          订单异常代码，当前可能的值为：
          10001：顾客电话关机
          10002：顾客电话已停机
          10003：顾客电话无人接听
          10004：顾客电话为空号
          10005：顾客留错电话
          10006：联系不上顾客其他原因
          10101：顾客更改收货地址
          10201：送货地址超区
          10202：顾客拒收货品
          10203：顾客要求延迟配送
          10401：商家关店/未营业
        type: integer
      exception_descr:
        description: 订单异常详细信息
        type: string
      exception_id:
        description: 异常ID，用来唯一标识一个订单异常信息。接入方用此字段用保证接口调用的幂等性。
        type: integer
      exception_time:
        description: 配送员上报订单异常的时间，格式为long，时区为GMT+8，距离Epoch(1970年1月1日) 以秒计算的时间，即unix-timestamp。
        type: string
      mt_peisong_id:
        description: 美团配送内部订单id，最长不超过32个字符
        type: string
      order_id:
        description: 外部订单号，最长不超过32个字符
        type: string
      sign:
        description: 数据签名
        type: string
      timestamp:
        description: 时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
        type: integer
    type: object
  dto.MpPushShopScope:
    properties:
      appkey:
        description: 开放平台分配的appkey，合作方唯一标识。
        type: string
      delivery_service_code:
        description: 配送活动标识
        type: integer
      scope:
        description: |-
          门店配送范围
          例：[{"x":31.305655,"y":96.954307},
          {"x":31.237576,"y":97.025718},
          {"x":31.327946,"y":97.158928},
          {"x":31.35375,"y":97.006492}]
        type: string
      shop_id:
        description: 美团配送内部订单id，最长不超过32个字符
        type: string
      sign:
        description: 数据签名
        type: string
      timestamp:
        description: 时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
        type: integer
    type: object
  dto.MpPushShopStatus:
    properties:
      appkey:
        description: 开放平台分配的appkey，合作方唯一标识。
        type: string
      reject_message:
        description: 驳回原因
        type: string
      shop_id:
        description: 取货门店id，即合作方向美团提供的门店id
        type: string
      shop_name:
        description: 门店名称
        type: string
      sign:
        description: 数据签名
        type: string
      status:
        description: |-
          10-审核驳回
          20-审核通过
          30-创建成功
          40-上线可发单
        type: integer
      timestamp:
        description: 时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
        type: integer
    type: object
  dto.MpReturnResult:
    properties:
      code:
        type: integer
    type: object
  dto.Mpordertest:
    properties:
      delivery_id:
        description: 配送活动标识
        type: integer
      delivery_service_code:
        description: 配送服务代码：飞速达:4002，快速达:4011，及时达:4012，集中送:4013，自由达:4014') INT(11)
        type: integer
      mt_peisong_id:
        description: 美团配送内部订单id，最长不超过32个字符
        type: string
    type: object
  dto.MtLogisticsException:
    properties:
      app_id:
        description: 三方应用id
        type: string
      app_poi_code:
        description: 三方门店code
        type: string
      ctime:
        description: 配送异常产生时间，恢复时为0
        type: string
      exception_reason:
        description: |-
          1）配送异常原因：
          1.发配送超过15分钟没有骑手抢单
          2.骑手抢单超时未到店
          3.骑手到店超时未取货
          4.预订单预计送达时间前X分钟未取货
          5.发配送超过30分钟没有骑手抢单
          2）配送恢复描述
        type: string
      order_view_id:
        description: 订单号
        type: string
      utime:
        description: 配送异常恢复时间，产生时为0
        type: string
    type: object
  dto.MtLogisticsOrderStatus:
    properties:
      dispatcher_mobile:
        description: |-
          美团配送骑手的联系电话，取最新一次指派的骑手信息。
          骑手手机号会以隐私号形式推送，请兼容13812345678和13812345678_123456两种号码格式，最多不超过20位，以便对接隐私号订单。
        type: string
      dispatcher_name:
        description: 美团配送骑手的姓名，取最新一次指派的骑手信息。
        type: string
      logistics_status:
        description: 美团配送订单状态code，目前美团配送状态值有：0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。
        type: integer
      order_id:
        description: 订单号，数据库中请用bigint(20)存储此字段。
        type: integer
      time:
        description: 订单配送状态变更为当前状态的时间，推送10位秒级的时间戳。
        type: integer
      wm_order_id_view:
        description: 订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
        type: integer
    type: object
  dto.NewProductInfo:
    properties:
      product_code:
        type: string
      sell_price:
        type: string
    type: object
  dto.OrderRefundCallback:
    properties:
      apply_op_user_type:
        description: |-
          推送当前仅退款或退货退款流程的发起方，仅适用于支持退货退款的商家。
          1-用户,2-商家,3-客服,4-BD,5-系统,6-开放平台
        type: string
      ctime:
        description: 本次退款申请的退款id
        type: integer
      food:
        description: 部分退款商品信息，json格式数组。 部分退才有
        type: string
      incmp_code:
        description: |-
          订单数据状态标记。当订单中部分字段的数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
          -1：有数据降级
          0：无数据降级
        type: integer
      is_appeal:
        description: 本次申请是否为用户申诉退款，参考值：0-否；1-是。如为1则表示用户第一次申请全额退款时已被商家驳回，本次用户发起的申诉会由美团客服介入处理，商家不能操作。
        type: integer
      logistics_info:
        description: 物流信息集合，仅适用支持退货退款业务的品类；在用户提交物流信息以及商家终审的推送消息中展示。
        type: string
      notify_type:
        description: |-
          全额退款通知类型，参考值：apply-发起退款；agree-确认退款；reject-驳回退款；cancelRefund-用户取消退款申请；cancelRefundComplaint-用户取消退款申诉。
          支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
        type: string
      order_id:
        description: 订单号，数据库中请用bigint(20)存储此字段。
        type: integer
      pictures:
        description: 用户申请退款时上传的退款图片，多个图片url以英文逗号隔开，上限为9张图片。字段信息为json格式数组。
        type: string
      reason:
        description: 申请退款的原因
        type: string
      refund_id:
        description: 本次退款申请的退款id
        type: integer
      res_type:
        description: |-
          退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
          支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
        type: integer
      service_type:
        description: |-
          退款服务类型, 区分是否已开通退货退款售后业务。
          未开通的场景：
          0-退款流程或申诉流程
          已开通场景：,1-仅退款流程,2-退款退货流程
        type: string
      status:
        description: |-
          推送当前售后单的状态类型，仅适用支持退货退款业务的商家：
          1-已申请,10-初审已同意,11-初审已驳回,16-初审已申诉,17-初审申诉已同意,18-初审申诉已驳回,20-终审已发起（用户已发货）,21-终审已同意
          22-终审已驳回,26-终审已申诉,27-终审申诉已同意,28-终审申诉已驳回,30-已取消
        type: string
    type: object
  dto.ProductInfo:
    properties:
      product_code:
        description: 第三方货号
        type: string
      sell_price:
        description: 以分为单位
        type: string
    type: object
  dto.ProductPrice:
    properties:
      apiId:
        type: string
      apiSecret:
        type: string
      apiStr:
        type: string
      product_code:
        description: 产品编码
        items:
          type: string
        type: array
      sign:
        type: string
      struct_code:
        description: 财务编码
        items:
          type: string
        type: array
      timestamp:
        type: string
    type: object
  dto.SyncProductPrice:
    properties:
      product_info:
        description: 商品信息
        items:
          $ref: '#/definitions/dto.ProductInfo'
        type: array
      struct_code:
        description: 财务编码
        items:
          type: string
        type: array
    type: object
  dto.SyncProductPriceResponse:
    properties:
      code:
        description: 状态码
        type: integer
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
    type: object
  oc.CardBaseResponse:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.CardNewByStoreReq:
    properties:
      card_id:
        description: 卡模板id
        type: integer
      scrm_id:
        description: 用户id
        type: string
      store_id:
        description: 子龙门店id
        type: integer
    type: object
  oc.CardNewByStoreRes:
    properties:
      code:
        description: 状态码，200成功，400失败
        type: integer
      data:
        $ref: '#/definitions/oc.CardNewByStoreRes_Data'
      message:
        description: 消息
        type: string
    type: object
  oc.CardNewByStoreRes_Data:
    properties:
      order_sn:
        description: 卡订单号
        type: string
    type: object
  oc.CardPayNotifyReq:
    properties:
      order_sn:
        description: 订单号
        type: string
      source:
        description: 来源，0默认，1兑换码激活，2门店开卡
        type: integer
    type: object
  oc.CheckCardIdReq:
    properties:
      card_id:
        description: 卡id
        type: integer
    type: object
  oc.CheckCardIdRes:
    properties:
      code:
        description: 状态码，200成功，400失败
        type: integer
      data:
        $ref: '#/definitions/oc.CheckCardIdRes_Data'
      message:
        description: 消息
        type: string
    type: object
  oc.CheckCardIdRes_Data:
    properties:
      name:
        description: 有效时返回 卡名称
        type: string
      result:
        description: true 有效，false无效
        type: boolean
    type: object
  pc.ProductBaseResponse:
    properties:
      code:
        description: 200 成功，400失败
        type: integer
      message:
        description: 失败时返回原因
        type: string
    type: object
  pc.R1PriceSyncReq:
    properties:
      prices:
        items:
          $ref: '#/definitions/pc.R1PriceSyncReq_Price'
        type: array
    type: object
  pc.R1PriceSyncReq_Price:
    properties:
      centralizedPurchasePrice:
        description: 集采价
        type: number
      retailPrice:
        description: 建议零售价格
        type: number
      skuNo:
        description: 物料编码（货号）
        type: string
      tradePrice:
        description: 批发价
        type: number
    type: object
  pc.R1PriceSyncSkuReq:
    properties:
      search:
        description: 限定更新的范围
        type: string
      type:
        description: 范围类型 1 sku_id、2 货号、3 组合商品id、9全量
        type: integer
    type: object
  pc.ZiLongDrugSyncReq:
    properties:
      data:
        description: 商品药品数据
        items:
          $ref: '#/definitions/pc.ZiLongDrugSyncReq_Data'
        type: array
    type: object
  pc.ZiLongDrugSyncReq_Data:
    properties:
      can_sell:
        description: 是否可销
        type: integer
      is_prescribed_drug:
        description: 是否处方药,只药品有，1是、0否
        type: integer
      product_code:
        description: 商品货号
        type: string
    type: object
host: localhost:7033
info:
  contact: {}
  description: 这里是描述
  title: 第三方系统对接项目接口文档
  version: "1.0"
paths:
  /external/card/card-order-value:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/cc.CardOrderValueReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/cc.CardOrderValueResp'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/cc.CardOrderValueResp'
      summary: 查询会员卡下已使用子龙门店券赠送价值
      tags:
      - 会员卡/服务包
  /external/card/card-value:
    get:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/cc.CardValueReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/cc.CardValueRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/cc.CardValueRes'
      summary: 查询卡权益价值
      tags:
      - 会员卡/服务包
  /external/card/check-card-id:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CheckCardIdReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CheckCardIdRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CheckCardIdRes'
      summary: 检查卡模板是否有效
      tags:
      - 会员卡/服务包
  /external/card/new-by-store:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardNewByStoreReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardNewByStoreRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardNewByStoreRes'
      summary: 会员卡通过门店开卡（储值卡赠送）
      tags:
      - 会员卡/服务包
  /external/card/return-by-store:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardPayNotifyReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
      tags:
      - 会员卡/服务包
  /external/iss-callback/order-status:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.IssOrderStatus'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.IssOrderStatusResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.IssOrderStatusResponse'
      summary: 订单状态回调
      tags:
      - 订单状态回调
  /external/mp-callback/shop-status:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpPushShopStatus'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
      summary: 门店状态回调
      tags:
      - 门店状态回调
  /external/product/GetProductPrice:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.ProductPrice'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.GetProductPriceInfo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.GetProductPriceInfo'
      summary: 根据财务编码和产品id查询北京接口的价格数据
      tags:
      - v4.2.1
  /external/product/price_sync:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          items:
            $ref: '#/definitions/dto.SyncProductPrice'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.SyncProductPriceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.SyncProductPriceResponse'
      summary: 子龙价格同步
      tags:
      - v4.2.1
  /external/product/r1-price-sync:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/pc.R1PriceSyncReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
      tags:
      - 商品
  /external/product/r1-price-sync-sku:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/pc.R1PriceSyncSkuReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
      tags:
      - 商品
  /external/product/zilong-drug-sync:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/pc.ZiLongDrugSyncReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pc.ProductBaseResponse'
      tags:
      - 商品
  /external/refund-order/pay:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: apply
        required: true
        schema:
          $ref: '#/definitions/controller.RefundOrderPayData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
      summary: 申请售后单
      tags:
      - 售后单
  /external/tool/deal_channel_product:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.SyncProductPriceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.SyncProductPriceResponse'
      summary: 处理渠道商品数据
      tags:
      - v2.1.1
  /mp-callback/order-logisticsexception-callback:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MtLogisticsException'
      produces:
      - application/json
      summary: 订单异常回调
      tags:
      - 订单异常回调 接口
  /mp-callback/order-logisticsstatus-callback:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MtLogisticsOrderStatus'
      produces:
      - application/json
      summary: test
      tags:
      - 美团配送的状态回调 接口
  /mp-callback/order-status:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpPushOrderStatus'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
      summary: test
      tags:
      - 订单状态回调 接口
  /mp-callback/order-unusual:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpPushOrderUnusual'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
      summary: 订单异常回调
      tags:
      - 订单异常回调 接口
  /mp-callback/shop-scope:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpPushShopScope'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.MpReturnResult'
      summary: 配送范围变更回调
      tags:
      - 配送范围变更回调 接口
  /mp-test/order-arrange:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟接单
      tags:
      - 模拟测试
  /mp-test/order-deliver:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟送达
      tags:
      - 模拟测试
  /mp-test/order-location:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟获取骑手位置
      tags:
      - 模拟测试
  /mp-test/order-pickup:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟取货
      tags:
      - 模拟测试
  /mp-test/order-rearrange:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟改派
      tags:
      - 模拟测试
  /mp-test/order-reportException:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Mpordertest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 模拟上传异常
      tags:
      - 模拟测试
  /mt-callback/order-compensation-callback:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.Compensation'
      produces:
      - application/json
      summary: APP方URL 推送客服赔付商家责任订单信息
      tags:
      - 美团推送接口
  /mt-callback/order-refund-all-callback:
    get:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.OrderRefundCallback'
      produces:
      - application/json
      summary: 美团推送全额退款信息（必接）
      tags:
      - 美团推送接口
  /mt-callback/order-refund-portion-callback:
    get:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.OrderRefundCallback'
      produces:
      - application/json
      summary: 美团推送部分退款信息（必接）
      tags:
      - 美团推送接口
  /mt-callback/privacy-demotion-callback:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      summary: 隐私号降级通知推送（必接）
      tags:
      - 美团推送接口
  /mt-callback/product-new-callback:
    post:
      consumes:
      - application/json
      produces:
      - application/json
  /mt-callback/product-update-callback:
    post:
      consumes:
      - application/json
      produces:
      - application/json
  /mt-test/order-cancel:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpOederRefund'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 商家订单取消
      tags:
      - 美团模拟测试
  /mt-test/order-refund-Agree:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpOederRefund'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 订单确认退款请求
      tags:
      - 美团模拟测试
  /mt-test/order-refund-reject:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpOederRefund'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 订单驳回退款请求
      tags:
      - 美团模拟测试
  /mt-test/phonenumber-rider:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpOederRefund'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 拉取骑手真实手机号
      tags:
      - 美团模拟测试
  /mt-test/phonenumber-user:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/dto.MpOederRefund'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ExternalResponse'
      summary: 拉取用户真实手机号
      tags:
      - 美团模拟测试
swagger: "2.0"
