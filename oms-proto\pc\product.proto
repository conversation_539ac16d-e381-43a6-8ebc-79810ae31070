syntax = "proto3";

package pc;

option go_package = "./oms-proto/pc";

service productService {
  // 查询商品列表
  rpc FindProduct(ProductRequest) returns (productResponse);

  // 微盟商品创建更新回调处理货号和sku_id信息
  rpc WmProductCreateOrUpdateCallBack(WmProductCreatOrUpdateVo) returns (BaseResponse);
}


message BaseResponse {
  int32 code = 1;
  string message = 2;
}

// 商品查询vo
message ProductRequest {
  string name = 1;
}


//商品信息返回
message productResponse {
  int32 code = 1;
  int32 total = 2;
  string message = 3;
  repeated Product data = 4;
}

message Brand {
  int32 id = 1;
  //品牌名称
  string name = 2;
  //排序
  int32 sort = 3;
  // logo图片
  string logo = 4;
  //品牌描述
  string description = 5;

  string create_date = 6;
  //是否推荐
  int32 is_recommend = 7;
  //展现形式 1-图片，2-文字
  int32 show_type = 8;
  //首字母
  string initial = 9;

  int32 company_id = 10;
  //所属分类id
  int32 brand_category_id = 11;
  //所属分类名称
  string category_name = 12;
}



message Product {
  int32 id = 1;
  //分类id
  int32 category_id = 2;
  //品牌id
  int32 brand_id = 3;
  //商品名称
  string name = 4;
  //商品编号
  string code = 5;
  //商品条码
  string bar_code = 6;
  //商品添加日期
  string create_date = 7;
  //商品最后更新日期
  string update_date = 8;
  //是否删除
  int32 is_del = 9;
  //是否为组合商品
  int32 is_group = 10;
  //商品图片（多图）
  string pic = 11;
  //商品卖点
  string selling_point = 12;
  //商品视频地址
  string video = 13;
  //电脑端详情内容
  string content_pc = 14;
  //手机端详情内容
  string content_mobile = 15;
  //是否参与优惠折扣
  int32 is_discount = 16;
  //商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
  int32 product_type = 17;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 18;
  //分类名称
  string category_name = 19;
  // sku信息
//  repeated Sku sku = 20;
  //商品属性
//  repeated ProductAttr attr = 21;
  //渠道id
  string channel_id = 22;
  //品牌名称
  string brand_name = 23;
  //京东分类id
  int32 jd_category_id = 24;
  //是否为药品 0否，1是
  int32 is_drugs = 25;
  // 商品应用范围（1电商，2前置仓，3门店仓）
  string use_range = 26;
  // 只有虚拟商品才有值(1.有效期至多少 2.有效期天数)
  int32 term_type = 27;
  // 如果term_type=1 存：时间戳 如果term_type=2 存多少天
  int32 term_value = 28;
  // 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)只有是组合商品才有值
  int32 group_type = 29;
  // 是否支持过期退款 1：是 0：否
  int32 virtual_invalid_refund = 30;
  // 药品仓类型 (0:否 1：巨星药品仓)
  int32 warehouse_type = 31;

  // 是否医疗商品（0:不是 1：是，不会同步到gj商品库）
  int32 is_intel_goods = 32;
}


message WmProductCreatOrUpdateVo {
  // 类型 1： 新增 2 ：编辑更新
  int64 type = 1;

  // 微盟的gooidsId
  string gooidsId = 2;

  // 微盟的vid
  string vid = 3;

}