package utils

import (
	"strings"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"
)

var (
	redisHandle *kit.DBEngine
)

func SetupDbConn() {
	redisHandle = kit.NewRedisEngine(getRedisDsn())
	//redis定时探活
	go redisHandle.DBEngineCheck(redisHandle.NewRedisConnInterface, 3, 3)
}

//关闭数据库连接
func CloseDbConn() {
	redisHandle.Engine.(*redis.Client).Close()
}

func getRedisDsn(dsn ...string) string {
	if len(dsn) > 0 {
		return dsn[0]
	}

	dsnSlice := []string{
		config.GetString("redis.Addr"),
		config.GetString("redis.Password"),
		config.GetString("redis.DB"),
	}
	//dsnSlice = []string{"172.30.128.56:6379", "MkdGH*3ldf", "0"}
	return strings.Join(dsnSlice, "|")
}

//获取redis集群客户端
func GetRedisConn(dsn ...string) *redis.Client {
	if redisHandle == nil || redisHandle.Engine == nil {
		redisHandle = kit.NewRedisEngine(getRedisDsn(dsn...))
	}

	return redisHandle.Engine.(*redis.Client)
}
