// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package order

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WeiMengOrderServiceClient is the client API for WeiMengOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WeiMengOrderServiceClient interface {
	// 新增微盟订单
	OrderAdd(ctx context.Context, in *WeiMengAddOrderRequest, opts ...grpc.CallOption) (*AddOrderResponse, error)
	// 微盟订单取消
	OrderCancel(ctx context.Context, in *WeiMengOrderCancelRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 新增微盟退款单
	OrderRefundAdd(ctx context.Context, in *WeiMOrderRefundAddRequest, opts ...grpc.CallOption) (*OrderRefundAddResponse, error)
	// 退款单更新
	OrderRefundUpdate(ctx context.Context, in *WeiMOrderRefundAddRequest, opts ...grpc.CallOption) (*OrderRefundAddResponse, error)
	// 新增微盟退款单
	OrderRefundCancel(ctx context.Context, in *WeiMengOrderRefundCancelRequest, opts ...grpc.CallOption) (*CommonResponse, error)
}

type weiMengOrderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWeiMengOrderServiceClient(cc grpc.ClientConnInterface) WeiMengOrderServiceClient {
	return &weiMengOrderServiceClient{cc}
}

func (c *weiMengOrderServiceClient) OrderAdd(ctx context.Context, in *WeiMengAddOrderRequest, opts ...grpc.CallOption) (*AddOrderResponse, error) {
	out := new(AddOrderResponse)
	err := c.cc.Invoke(ctx, "/order.WeiMengOrderService/OrderAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weiMengOrderServiceClient) OrderCancel(ctx context.Context, in *WeiMengOrderCancelRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/order.WeiMengOrderService/OrderCancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weiMengOrderServiceClient) OrderRefundAdd(ctx context.Context, in *WeiMOrderRefundAddRequest, opts ...grpc.CallOption) (*OrderRefundAddResponse, error) {
	out := new(OrderRefundAddResponse)
	err := c.cc.Invoke(ctx, "/order.WeiMengOrderService/OrderRefundAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weiMengOrderServiceClient) OrderRefundUpdate(ctx context.Context, in *WeiMOrderRefundAddRequest, opts ...grpc.CallOption) (*OrderRefundAddResponse, error) {
	out := new(OrderRefundAddResponse)
	err := c.cc.Invoke(ctx, "/order.WeiMengOrderService/OrderRefundUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weiMengOrderServiceClient) OrderRefundCancel(ctx context.Context, in *WeiMengOrderRefundCancelRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/order.WeiMengOrderService/OrderRefundCancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WeiMengOrderServiceServer is the server API for WeiMengOrderService service.
// All implementations should embed UnimplementedWeiMengOrderServiceServer
// for forward compatibility
type WeiMengOrderServiceServer interface {
	// 新增微盟订单
	OrderAdd(context.Context, *WeiMengAddOrderRequest) (*AddOrderResponse, error)
	// 微盟订单取消
	OrderCancel(context.Context, *WeiMengOrderCancelRequest) (*CommonResponse, error)
	// 新增微盟退款单
	OrderRefundAdd(context.Context, *WeiMOrderRefundAddRequest) (*OrderRefundAddResponse, error)
	// 退款单更新
	OrderRefundUpdate(context.Context, *WeiMOrderRefundAddRequest) (*OrderRefundAddResponse, error)
	// 新增微盟退款单
	OrderRefundCancel(context.Context, *WeiMengOrderRefundCancelRequest) (*CommonResponse, error)
}

// UnimplementedWeiMengOrderServiceServer should be embedded to have forward compatible implementations.
type UnimplementedWeiMengOrderServiceServer struct {
}

func (UnimplementedWeiMengOrderServiceServer) OrderAdd(context.Context, *WeiMengAddOrderRequest) (*AddOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderAdd not implemented")
}
func (UnimplementedWeiMengOrderServiceServer) OrderCancel(context.Context, *WeiMengOrderCancelRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderCancel not implemented")
}
func (UnimplementedWeiMengOrderServiceServer) OrderRefundAdd(context.Context, *WeiMOrderRefundAddRequest) (*OrderRefundAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderRefundAdd not implemented")
}
func (UnimplementedWeiMengOrderServiceServer) OrderRefundUpdate(context.Context, *WeiMOrderRefundAddRequest) (*OrderRefundAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderRefundUpdate not implemented")
}
func (UnimplementedWeiMengOrderServiceServer) OrderRefundCancel(context.Context, *WeiMengOrderRefundCancelRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderRefundCancel not implemented")
}

// UnsafeWeiMengOrderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WeiMengOrderServiceServer will
// result in compilation errors.
type UnsafeWeiMengOrderServiceServer interface {
	mustEmbedUnimplementedWeiMengOrderServiceServer()
}

func RegisterWeiMengOrderServiceServer(s grpc.ServiceRegistrar, srv WeiMengOrderServiceServer) {
	s.RegisterService(&WeiMengOrderService_ServiceDesc, srv)
}

func _WeiMengOrderService_OrderAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengAddOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeiMengOrderServiceServer).OrderAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.WeiMengOrderService/OrderAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeiMengOrderServiceServer).OrderAdd(ctx, req.(*WeiMengAddOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeiMengOrderService_OrderCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengOrderCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeiMengOrderServiceServer).OrderCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.WeiMengOrderService/OrderCancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeiMengOrderServiceServer).OrderCancel(ctx, req.(*WeiMengOrderCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeiMengOrderService_OrderRefundAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMOrderRefundAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeiMengOrderServiceServer).OrderRefundAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.WeiMengOrderService/OrderRefundAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeiMengOrderServiceServer).OrderRefundAdd(ctx, req.(*WeiMOrderRefundAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeiMengOrderService_OrderRefundUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMOrderRefundAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeiMengOrderServiceServer).OrderRefundUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.WeiMengOrderService/OrderRefundUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeiMengOrderServiceServer).OrderRefundUpdate(ctx, req.(*WeiMOrderRefundAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeiMengOrderService_OrderRefundCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengOrderRefundCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeiMengOrderServiceServer).OrderRefundCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.WeiMengOrderService/OrderRefundCancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeiMengOrderServiceServer).OrderRefundCancel(ctx, req.(*WeiMengOrderRefundCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WeiMengOrderService_ServiceDesc is the grpc.ServiceDesc for WeiMengOrderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WeiMengOrderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "order.WeiMengOrderService",
	HandlerType: (*WeiMengOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OrderAdd",
			Handler:    _WeiMengOrderService_OrderAdd_Handler,
		},
		{
			MethodName: "OrderCancel",
			Handler:    _WeiMengOrderService_OrderCancel_Handler,
		},
		{
			MethodName: "OrderRefundAdd",
			Handler:    _WeiMengOrderService_OrderRefundAdd_Handler,
		},
		{
			MethodName: "OrderRefundUpdate",
			Handler:    _WeiMengOrderService_OrderRefundUpdate_Handler,
		},
		{
			MethodName: "OrderRefundCancel",
			Handler:    _WeiMengOrderService_OrderRefundCancel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "order/order.proto",
}

// OrderServiceClient is the client API for OrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderServiceClient interface {
	// 保存原始订单
	OriginOrderSave(ctx context.Context, in *OriginOrderSaveRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 保存原始退款单
	OriginOrderRefundSave(ctx context.Context, in *OriginOrderRefundSaveRequest, opts ...grpc.CallOption) (*CommonResponse, error)
}

type orderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderServiceClient(cc grpc.ClientConnInterface) OrderServiceClient {
	return &orderServiceClient{cc}
}

func (c *orderServiceClient) OriginOrderSave(ctx context.Context, in *OriginOrderSaveRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/order.OrderService/OriginOrderSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) OriginOrderRefundSave(ctx context.Context, in *OriginOrderRefundSaveRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/order.OrderService/OriginOrderRefundSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServiceServer is the server API for OrderService service.
// All implementations should embed UnimplementedOrderServiceServer
// for forward compatibility
type OrderServiceServer interface {
	// 保存原始订单
	OriginOrderSave(context.Context, *OriginOrderSaveRequest) (*CommonResponse, error)
	// 保存原始退款单
	OriginOrderRefundSave(context.Context, *OriginOrderRefundSaveRequest) (*CommonResponse, error)
}

// UnimplementedOrderServiceServer should be embedded to have forward compatible implementations.
type UnimplementedOrderServiceServer struct {
}

func (UnimplementedOrderServiceServer) OriginOrderSave(context.Context, *OriginOrderSaveRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OriginOrderSave not implemented")
}
func (UnimplementedOrderServiceServer) OriginOrderRefundSave(context.Context, *OriginOrderRefundSaveRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OriginOrderRefundSave not implemented")
}

// UnsafeOrderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderServiceServer will
// result in compilation errors.
type UnsafeOrderServiceServer interface {
	mustEmbedUnimplementedOrderServiceServer()
}

func RegisterOrderServiceServer(s grpc.ServiceRegistrar, srv OrderServiceServer) {
	s.RegisterService(&OrderService_ServiceDesc, srv)
}

func _OrderService_OriginOrderSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OriginOrderSaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).OriginOrderSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.OrderService/OriginOrderSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).OriginOrderSave(ctx, req.(*OriginOrderSaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_OriginOrderRefundSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OriginOrderRefundSaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).OriginOrderRefundSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/order.OrderService/OriginOrderRefundSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).OriginOrderRefundSave(ctx, req.(*OriginOrderRefundSaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderService_ServiceDesc is the grpc.ServiceDesc for OrderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "order.OrderService",
	HandlerType: (*OrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OriginOrderSave",
			Handler:    _OrderService_OriginOrderSave_Handler,
		},
		{
			MethodName: "OriginOrderRefundSave",
			Handler:    _OrderService_OriginOrderRefundSave_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "order/order.proto",
}
