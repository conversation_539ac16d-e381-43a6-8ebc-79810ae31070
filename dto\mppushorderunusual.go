package dto

type MpPushOrderUnusual struct {
	//配送活动标识
	DeliveryId int64 `json:"delivery_id" form:"delivery_id" query:"delivery_id"`
	//美团配送内部订单id，最长不超过32个字符
	MtPeisongId string `json:"mt_peisong_id" form:"mt_peisong_id" query:"mt_peisong_id"`
	//外部订单号，最长不超过32个字符
	OrderId string`json:"order_id" form:"order_id" query:"order_id"`

   //异常ID，用来唯一标识一个订单异常信息。接入方用此字段用保证接口调用的幂等性。
	ExceptionId int64`json:"exception_id" form:"exception_id" query:"exception_id"`
	//订单异常代码，当前可能的值为：
	//10001：顾客电话关机
	//10002：顾客电话已停机
	//10003：顾客电话无人接听
	//10004：顾客电话为空号
	//10005：顾客留错电话
	//10006：联系不上顾客其他原因
	//10101：顾客更改收货地址
	//10201：送货地址超区
	//10202：顾客拒收货品
	//10203：顾客要求延迟配送
	//10401：商家关店/未营业
	ExceptionCode int32`json:"exception_code" form:"exception_code" query:"exception_code"`
	//订单异常详细信息
	ExceptionDescr string`json:"exception_descr" form:"exception_descr" query:"exception_descr"`
	//配送员上报订单异常的时间，格式为long，时区为GMT+8，距离Epoch(1970年1月1日) 以秒计算的时间，即unix-timestamp。
	ExceptionTime string`json:"exception_time" form:"exception_time" query:"exception_time"`
	//上报订单异常的配送员姓名
	CourierName string`json:"courier_name" form:"courier_name" query:"courier_name"`
	//上报订单异常的配送员电话
	CourierPhone string`json:"courier_phone" form:"courier_phone" query:"courier_phone"`
	//开放平台分配的appkey，合作方唯一标识。
	AppKey string `json:"appkey" form:"appkey" query:"appkey"`
	//	时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
	Timestamp int64 `json:"timestamp" form:"timestamp" query:"timestamp"`
	//数据签名
	Sign string `json:"sign" form:"sign" query:"sign"`
}
