package controller

import (
	"context"
	"encoding/json"
	"external-ui/services"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"external-ui/dto"
	"external-ui/pkg/app"
	"external-ui/pkg/code"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"external-ui/proto/pc"
	"external-ui/utils"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"golang.org/x/exp/rand"
	"google.golang.org/grpc/metadata"
)

func ElmCallback(c echo.Context) error {
	var paras = make(map[string][]string)
	paras, _ = c.FormParams()
	glog.Info("ElmCallback-饿了么下行通知参数：", paras)
	//TODO
	/*验签 start*/
	//var errno = 0
	cmd := c.FormValue("cmd")
	source := c.FormValue("source")
	bodys, err := url.PathUnescape(c.FormValue("body"))
	glog.Info("ElmCallback body", c.FormValue("body"))
	if err != nil {
		glog.Error("PathUnescape elebody error", err)
	}
	if bodys == "" {
		bodys = c.FormValue("body")
	}
	glog.Info("ElmCallback-饿了么下行通知参数cmd:", cmd, "body:", bodys)

	//默认返回结果
	arr, body := make(map[string]interface{}), make(map[string]interface{})
	body["errno"] = 0
	body["error"] = "success"

	// appChannel, err := GetAppChannelBySource(source)
	// if err != nil {
	// 	glog.Error("GetAppChannelBySource error ：", source, ""+err.Error())
	// }
	appChannel, retCode := app.GetStoreMasterId(source, app.Channel_ELM)
	if retCode != code.Success {
		glog.Error("app.GetStoreMasterId：", source, app.Channel_ELM, retCode, appChannel)
	}

	///*返回结果 end*/
	switch cmd {
	//饿了么 2.0 不需要订阅用户取消接口，全部统一走逆向消息
	case "order.user.cancel": //订单取消
		errstr := ""
		body, errstr = UserOrderCancel(bodys)
		for i := 0; i < 9; i++ {
			if errstr == "" {
				break
			} else {
				body, errstr = UserOrderCancel(bodys)
			}
			// sean.todo 时间格式
			time.Sleep(2 * 1e9)
		}
	case "order.status.push": //状态推送
		body = OrderStatusPush(bodys, appChannel)
	//部分退款 走逆向消息
	//case "order.partrefund.push": //推送部分退款
	//	body = OrderPartRefund(bodys)
	case "order.create": //创建订单
		body = OrderCreate(bodys, appChannel)
	case "order.deliveryStatus.push": //物流状态推送
		body = OrderDeliveryStatusPush(bodys, appChannel)
	case "order.reverse.push": //2.0逆向消息
		body = OrderReversePush(bodys, appChannel)
	case "sku.create.push": //推送商品创建消息
		glog.Info("ElmCallback-SkuCreatePush饿了么下行通知参数：", bodys, appChannel)
		body = SkuCreatePush(bodys, appChannel)
	case "sku.update.push": //推送商品修改消息
		glog.Info("ElmCallback-SkuUpdatePush饿了么下行通知参数：", bodys, appChannel)
		body = SkuUpdatePush(bodys, appChannel)
	case "sku.delete.push": //推送商品删除消息
		glog.Info("ElmCallback-SkuDeletePush饿了么下行通知参数：", bodys, appChannel)
		body = SkuDeletePush(bodys, appChannel)
	case "order.modify.push": //推送订单修改
		glog.Info("ElmCallback-SkuDeletePush饿了么下行通知参数：", bodys, appChannel)
		body = OrderModify(bodys, appChannel)
	}

	arr = GetReturnPar(body, "resp."+cmd)
	glog.Info("饿了么回调返回参数： " + cmd + " " + kit.JsonEncode(arr))
	return c.JSON(200, arr)
}

// 订单修改
func OrderModify(data string, appChannel int32) map[string]interface{} {
	var logBuild strings.Builder
	logBuild.WriteString("OrderModify 饿了么订单修改推送 ：" + data)
	body := make(map[string]interface{})
	body["errno"] = 1
	body["error"] = "默认失败"
	modeData := dto.OrderModifyRequest{}
	err := json.Unmarshal([]byte(data), &modeData)
	if err != nil {
		glog.Error("OrderModify 数据解码失败", err)
	}

	elmOrderDetailModel := getElmOrderDetailModel(modeData.OrderID, appChannel)

	if modeData.ReceiverPhone != "" {
		elmOrderDetailModel.Data.User.Phone = modeData.ReceiverPhone
	}

	model := new(oc.MtAddOrderRequest)
	model.OrderSn = modeData.OrderID
	model.OrderStatus = 20
	model.OrderStatusChild = 20101
	model.ShopId = elmOrderDetailModel.Data.Shop.Id
	model.ShopName = elmOrderDetailModel.Data.Shop.Name
	model.ReceiverName = elmOrderDetailModel.Data.User.Name
	model.ReceiverState = elmOrderDetailModel.Data.User.Province
	model.ReceiverCity = elmOrderDetailModel.Data.User.City
	model.ReceiverDistrict = elmOrderDetailModel.Data.User.District
	model.ReceiverAddress = elmOrderDetailModel.Data.User.Address
	model.ReceiverPhone = elmOrderDetailModel.Data.User.Phone
	intPayType := elmOrderDetailModel.Data.Order.PayType
	//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
	if intPayType == 2 {
		model.PayType = "Cod"
	} else {
		model.PayType = "NoCod"
	}
	model.ReceiverMobile = elmOrderDetailModel.Data.User.Phone
	model.GjpStatus = "Payed"
	model.Total = elmOrderDetailModel.Data.Order.UserFee          //用户实付金额，单位：分
	model.Privilege = elmOrderDetailModel.Data.Order.DiscountFee  //优惠总金额，单位：分
	model.PackingCost = elmOrderDetailModel.Data.Order.PackageFee //包装费，单位：分
	model.Freight = elmOrderDetailModel.Data.Order.SendFee        //配送费 ，单位：分
	model.IsPay = 1
	ctime := elmOrderDetailModel.Data.Order.CreateTime
	model.CreateTime = kit.GetTimeNow(time.Unix(cast.ToInt64(ctime), 0))
	longitude, err := strconv.ParseFloat(elmOrderDetailModel.Data.User.Coord.Longitude, 64)
	model.Longitude = longitude //经度
	latitude, err := strconv.ParseFloat(elmOrderDetailModel.Data.User.Coord.Latitude, 64)
	model.Latitude = latitude //纬度
	model.PickupCode = elmOrderDetailModel.Data.Order.PickUpCode
	model.LogisticsCode = elmOrderDetailModel.Data.Order.DeliveryParty
	sendTime := elmOrderDetailModel.Data.Order.SendTime
	model.ExpectedTime = kit.GetTimeNow(time.Unix(sendTime, 0)) //预计送达时间
	//订单类型1普通订单(默认),2预订订单,3门店自提,4拼团订单,5门店配送
	if elmOrderDetailModel.Data.Order.SendImmediately == 1 {
		model.OrderType = 1
	} else {
		model.OrderType = 2
	}
	model.PayTime = kit.GetTimeNow()
	model.PaySn = ""
	var invoiceData map[string]string
	invoiceData = make(map[string]string)
	invoiceData["发票抬头"] = elmOrderDetailModel.Data.Order.InvoiceTitle
	invoiceData["纳税人识别号"] = elmOrderDetailModel.Data.Order.TaxerId
	model.Invoice = kit.JsonEncode(invoiceData)
	if elmOrderDetailModel.Data.Order.Remark == "0" {
		model.BuyerMemo = ""
	} else {
		model.BuyerMemo = elmOrderDetailModel.Data.Order.Remark
	}
	//1快递 2外卖 3自提
	if elmOrderDetailModel.Data.Order.BusinessType == "0" {
		model.DeliveryType = 2
	} else if elmOrderDetailModel.Data.Order.BusinessType == "1" {
		model.DeliveryType = 3
	} else {
		model.DeliveryType = 1
	}

	model.AppChannel = appChannel
	ocClient := oc.GetOrderServiceClient()
	var grpcRes *oc.MtAddOrderResponse

	//加入context渠道信息
	grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 3, UserAgent: 6, AppChannel: int(appChannel)}}
	ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

	//修改为订单信息修改
	grpcRes, err = ocClient.Cart.MtUpdateOrder(ctx, model)
	if err != nil {
		body["errno"] = 1
		body["error"] = err.Error()
		logBuild.WriteString(" 修改订单信息报错 OrderModify ：" + err.Error())
		glog.Info(logBuild.String()) // 记录日志
		return body
	}
	if grpcRes.Code != 200 {
		body["errno"] = 1
		body["error"] = "失败"
		logBuild.WriteString(" 发起部分退款报错 OrderModify ：" + grpcRes.Message + grpcRes.Error)
		glog.Info(logBuild.String()) // 记录日志
		return body
	}

	body["errno"] = 0
	body["error"] = ""

	return body
}

// 发起部分退款
func AfterSaleApply(data string) map[string]interface{} {
	var logBuild strings.Builder
	logBuild.WriteString("AfterSaleApply 发起部分退款 OrderPartreFund ：" + data)
	body := make(map[string]interface{})
	body["errno"] = 1
	body["error"] = "默认失败"
	modeData := dto.ELMOPartreFund{}
	err := json.Unmarshal([]byte(data), &modeData)
	if err != nil {
		glog.Error("AfterSaleApply 数据解码失败", err)
	}

	model := oc.RefundOrderApplyRequest{}
	model.ExternalOrderId = modeData.OrderId
	//model.RefundOrderSn = modedata.RefundId
	model.OldRefundSn = modeData.RefundId
	model.Reason = modeData.Reason
	model.RefundType = 1
	//是否部分退款通过2.0接口判断是否还有剩余金额没退来判断
	model.FullRefund = modeData.FullRefund
	ApplyOpUserType := ""

	if modeData.Type == 1 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "商家"
	} else if modeData.Type == 2 {
		model.ApplyOpUserType = "1"
		ApplyOpUserType = "用户"
	} else if modeData.Type == 3 {
		model.ApplyOpUserType = "3"
		ApplyOpUserType = "客服"
	} else if modeData.Type == 4 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "系统"
	} else if modeData.Type == 5 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "物流"
	} else if modeData.Type == 6 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "风控"
	}
	if len(modeData.Photos) > 0 {
		model.Pictures = kit.JsonEncode(modeData.Photos)
	}

	model.ResType = ApplyOpUserType + "申请售后单"
	model.OperationType = ApplyOpUserType + "申请售后单"
	model.Reason = modeData.Reason
	model.OrderFrom = 3
	model.ChannelId = 3
	//配送费平台补贴
	activityPtAmount := modeData.DeliveryFee.ShopRate
	for _, x := range modeData.RefundProducts {
		item := oc.RefundOrderGoodsData{}
		item.SubBizOrderId = x.SubBizOrderId
		item.SkuId = x.CustomSkuId
		item.Quantity = x.Number
		item.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(int64(x.TotalRefund)))
		item.GoodsName = x.Name
		item.RefundPrice = float32(x.TotalRefund) / float32(x.Number*100)
		item.RefundRealityPrice = float32(x.TotalRefund) / float32(x.Number*100)
		model.RefundOrderGoodsData = append(model.RefundOrderGoodsData, &item)
		//商品平台补贴  2.0接口直接全部赋值进去了
		//activityPtAmount += x.ShopEleRefund
	}
	//平台补贴，包含商品的和配送费的
	model.ActivityPtAmount = float32(activityPtAmount) / 100
	model.RefundAmount = float32(kit.FenToYuan(int64(modeData.RefundPrice)))

	//本次退款的配送费
	model.DeliveryPrice = float32(modeData.DeliveryPrice) / 100

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.ROC.RefundOrderApply(kit.SetTimeoutCtx(context.Background()), &model)
	if err != nil {
		body["errno"] = 1
		body["error"] = err.Error()
		logBuild.WriteString(" 发起部分退款报错 OrderPartreFund ：" + err.Error())
		glog.Info(logBuild.String()) // 记录日志
		return body
	}
	if grpcRes.Code != 200 {
		body["errno"] = 1
		body["error"] = "失败"
		logBuild.WriteString(" 发起部分退款报错 OrderPartreFund ：" + grpcRes.Message + grpcRes.Error)
		glog.Info(logBuild.String()) // 记录日志
		return body
	}

	body["errno"] = 0
	body["error"] = ""

	return body
}

// 应答部分退款
func AfterSaleAnserw(data string) map[string]interface{} {
	var logbuild strings.Builder
	logbuild.WriteString(" 部分退款应答 OrderPartreFund ：" + data)
	body := make(map[string]interface{})
	body["errno"] = 1
	body["error"] = "默认失败"
	modeData := dto.ELMOPartreFund{}
	err := json.Unmarshal([]byte(data), &modeData)
	if err != nil {
		glog.Error("AfterSaleAnserw 数据解码失败", err)
	}
	model := oc.RefundOrderApplyRequest{}
	model.ExternalOrderId = modeData.OrderId
	//model.RefundOrderSn = modedata.RefundId
	model.OldRefundSn = modeData.RefundId
	model.RefundType = 1
	model.FullRefund = 2
	ApplyOpUserType := ""

	if modeData.Type == 1 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "商家"
	} else if modeData.Type == 2 {
		model.ApplyOpUserType = "1"
		ApplyOpUserType = "用户"
	} else if modeData.Type == 3 {
		model.ApplyOpUserType = "3"
		ApplyOpUserType = "客服"
	} else if modeData.Type == 4 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "系统"
	} else if modeData.Type == 5 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "物流"
	} else if modeData.Type == 6 {
		model.ApplyOpUserType = "2"
		ApplyOpUserType = "风控"
	}
	if len(modeData.Photos) > 0 {
		model.Pictures = kit.JsonEncode(modeData.Photos)
	}

	model.ResType = ApplyOpUserType + "申请售后单"
	model.OperationType = ApplyOpUserType + "申请售后单"
	model.Reason = modeData.Reason
	model.OrderFrom = 3
	model.ChannelId = 3
	//配送费平台补贴
	activityPtAmount := modeData.DeliveryFee.ShopRate
	for _, x := range modeData.RefundProducts {
		item := oc.RefundOrderGoodsData{}
		item.SubBizOrderId = x.SubBizOrderId
		item.SkuId = x.CustomSkuId
		item.Quantity = x.Number
		item.GoodsName = x.Name
		item.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(int64(x.TotalRefund)))
		item.RefundPrice = float32(x.TotalRefund) / float32(x.Number*100)
		item.RefundRealityPrice = float32(x.TotalRefund) / float32(x.Number*100)
		//商品平台补贴
		activityPtAmount += x.ShopEleRefund
		model.RefundOrderGoodsData = append(model.RefundOrderGoodsData, &item)
	}
	//本次退款的配送费
	model.DeliveryPrice = float32(modeData.DeliveryPrice) / 100
	model.RefundAmount = float32(kit.FenToYuan(int64((modeData.RefundPrice))))
	//说明是商户发起的部分退款，需要自己推送一个发起，再推送应答

	ocClient := oc.GetOrderServiceClient()

	//是要是原来状态是申请，现在直接变成成功，那就直接添加申请单和直接审核通过
	if (modeData.Status == 20 && modeData.LastRefundStatus == 0) || (modeData.Type == 1 && modeData.Status == 20) {
		//if (modeData.Type == 1 && modeData.Status == 20) || (modeData.Type == 4 && modeData.Status == 20 && modeData.LastRefundStatus == 0) || (modeData.Type == 2 && modeData.Status == 20 && modeData.LastRefundStatus == 0) {
		grpcRes, err := ocClient.ROC.RefundOrderApply(kit.SetTimeoutCtx(context.Background()), &model)
		if err != nil {
			body["errno"] = 1
			body["error"] = err.Error()
			logbuild.WriteString(" 发起部分退款报错 OrderPartreFund ：" + err.Error())
			glog.Info(logbuild.String()) // 记录日志
			return body
		}
		if grpcRes.Code != 200 {
			body["errno"] = 1
			body["error"] = "失败"
			logbuild.WriteString(" 发起部分退款报错 OrderPartreFund ：" + grpcRes.Message + grpcRes.Error)
			glog.Info(logbuild.String()) // 记录日志
			return body
		}
	}

	//应答model
	InAnswer := oc.RefundOrderAnswerRequest{}
	InAnswer.ExternalOrderId = modeData.OrderId
	InAnswer.RefundOrderSn = modeData.RefundId
	InAnswer.ActivityPtAmount = kit.FenToYuan(activityPtAmount)
	InAnswer.Reason = modeData.Reason
	//操作类型
	OperationType := ""
	if modeData.Status == 30 {
		OperationType = "用户申请仲裁,客服介入"
		InAnswer.ResultType = 3
	} else if modeData.Status == 50 {
		OperationType = "商家拒绝"
		InAnswer.ResultType = 3
	}

	if modeData.Reason_type != "" && ApplyOpUserType == "用户" {
		switch modeData.Reason_type {
		case "1103":
			OperationType = "用户撤销"
			InAnswer.ResultType = 2
		case "1104":
			OperationType = "商家拒绝用户退单,用户未申请仲裁,系统自动退单失败"
			InAnswer.ResultType = 2
		case "1302":
			OperationType = "客服仲裁退款失败"
			InAnswer.ResultType = 2
		case "1301":
			OperationType = "客服仲裁退款成功"
			InAnswer.ResultType = 1
		case "1202":
			OperationType = "商家同意"
			InAnswer.ResultType = 1
		case "1304":
			OperationType = "用户申请仲裁,客服未处理,系统自动退单成功"
			InAnswer.ResultType = 1
		case "1203":
			OperationType = "商家超时未处理,系统自动退单成功"
			InAnswer.ResultType = 1
		}

	}
	if OperationType == "" {
		if modeData.Status == 20 {
			OperationType = "商家同意"
			InAnswer.ResultType = 1
		}
		if modeData.Status == 40 {
			OperationType = "关闭"
			InAnswer.ResultType = 2
		}

	}
	InAnswer.OperationType = OperationType
	InAnswer.ResultTypeNote = OperationType

	//应答
	grpcRes, err := ocClient.ROC.RefundOrderAnswer(kit.SetTimeoutCtx(context.Background()), &InAnswer)
	if err != nil {
		body["errno"] = 1
		body["error"] = err.Error()
		logbuild.WriteString(" 部分退款应答报错 AfterSaleAnserw ：" + err.Error())
		glog.Info(logbuild.String()) // 记录日志
		return body
	}
	if grpcRes.Code != 200 {
		body["errno"] = 1
		body["error"] = grpcRes.Message + grpcRes.Error
		logbuild.WriteString(" 部分退款应答报错 AfterSaleAnserw ：" + grpcRes.Message + grpcRes.Error)
		glog.Info(logbuild.String()) // 记录日志
		return body
	}

	body["errno"] = 0
	body["error"] = ""

	return body
}

// 接口描述：平台向合作方推送订单完成前用户全单取消申请和订单完成后用户全单退款申请相关消息。
func OrderPartRefund(data string) map[string]interface{} {
	var logbuild strings.Builder
	glog.Info("OrderPartRefund 进入部分退款" + data) // 记录日志
	body := make(map[string]interface{})

	modeData := dto.ELMOPartreFund{}
	err := json.Unmarshal([]byte(data), &modeData)
	if err != nil {
		glog.Info("OrderPartRefund 退款数据解码失败", err)
	}

	lockCard := "lock:elmreturn:" + modeData.OrderId
	redisConn := utils.GetRedisConn()
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		glog.Info("退款 lock:elmreturn"+modeData.OrderId+"存在：", data)
		body["errno"] = 1
		body["error"] = "redis 锁还没放开"
		return body
	}
	defer redisConn.Del(lockCard)

	//申请
	if modeData.Status == 10 {
		body = AfterSaleApply(data)
	} else { //应答
		body = AfterSaleAnserw(data)
	}

	glog.Info(logbuild.String()) // 记录日志
	return body
}

// sean.todo 错误返回
// 接口描述：平台向合作方推送订单完成前用户全单取消申请和订单完成后用户全单退款申请相关消息。
func UserOrderCancel(data string) (map[string]interface{}, string) {
	var logbuild strings.Builder
	logbuild.WriteString(" 发起全额退款 UserOrderCancel ：" + data)
	body := make(map[string]interface{})
	body["errno"] = 1
	body["error"] = "默认失败"
	glog.Info("UserOrderCancel 发起全额退款 请求参数", data)
	modedata := dto.ELMOrderCancel{}
	err := json.Unmarshal([]byte(data), &modedata)
	if err != nil {
		glog.Info("OrderPartRefund 退款数据解码失败", err)
	}
	if modedata.CancelType == "1" && modedata.Type == "60" {
		body["errno"] = 0
		body["error"] = ""
		return body, ""
	}

	lockCard := "lock:elmreturn:" + modedata.OrderId
	redisConn := utils.GetRedisConn()
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		glog.Info("退款 lock:elmreturn"+modedata.OrderId+"存在：", data)
		body["errno"] = 1
		body["error"] = "redis 锁还没放开"
		return body, "redis 锁还没放开"
	}
	defer redisConn.Del(lockCard)

	//申请MODE
	model := oc.RefundOrderApplyRequest{}
	model.ExternalOrderId = modedata.OrderId
	//model.RefundOrderSn = fmt.Sprintf("%d", modedata.RefundOrderId)
	model.OldRefundSn = cast.ToString(modedata.RefundOrderId)
	model.Reason = modedata.CancelReason
	model.RefundType = 1
	model.FullRefund = 1
	model.ApplyOpUserType = "1"
	if len(modedata.Pictures) > 0 {
		model.Pictures = kit.JsonEncode(modedata.Pictures)
	}
	model.Reason = modedata.CancelReason
	model.OrderFrom = 3
	model.ChannelId = 3
	OperationType := ""
	switch modedata.Type {
	case "10":
		OperationType = "用户申请售后单"
	case "20":
		OperationType = "客服介入"
	case "30":
		OperationType = "客服拒绝"
	case "40":
		OperationType = "客服同意"
	case "50":
		OperationType = "商户拒绝"
	case "60":
		OperationType = "商户同意"
	case "70":
		OperationType = "申请失效"
	}
	model.ResType = OperationType
	model.OperationType = OperationType

	//应答model
	InAnswer := oc.RefundOrderAnswerRequest{}
	InAnswer.ExternalOrderId = modedata.OrderId
	InAnswer.RefundOrderSn = fmt.Sprintf("%d", modedata.RefundOrderId)
	InAnswer.OperationType = OperationType
	InAnswer.ResultTypeNote = OperationType

	ocClient := oc.GetOrderServiceClient()

	if modedata.Type == "10" {
		grpcRes, err := ocClient.ROC.RefundOrderApply(kit.SetTimeoutCtx(context.Background()), &model)
		if err != nil {
			body["errno"] = 1
			body["error"] = err.Error()
			logbuild.WriteString(" 发起整单退款报错 UserOrderCancel ：" + err.Error())
			glog.Info(logbuild.String()) // 记录日志
			return body, err.Error()
		}
		if grpcRes.Code != 200 {
			body["errno"] = 1
			body["error"] = grpcRes.Message + grpcRes.Error
			logbuild.WriteString(" 发起整单退款报错 UserOrderCancel ：" + grpcRes.Message + grpcRes.Error)
			glog.Info(logbuild.String()) // 记录日志
			return body, grpcRes.Message + grpcRes.Error
		}

	} else { //如果不是申请
		//消息类型。10:发起申请,20:客服介入,
		//30:客服拒绝,40:客服同意,
		//50:商户拒绝,60:商户同意,70:申请失效
		if modedata.Type == "30" || modedata.Type == "70" { //拒绝
			InAnswer.Reason = modedata.RefuseReason
			InAnswer.ResultType = 2
			//同意
		} else if modedata.Type == "40" || modedata.Type == "60" {
			InAnswer.ResultType = 1
		} else { //记录日志
			InAnswer.ResultType = 3
			if modedata.Type == "50" {
				InAnswer.Reason = modedata.RefuseReason
			}
		}

		grpcRes, err := ocClient.ROC.RefundOrderAnswer(kit.SetTimeoutCtx(context.Background()), &InAnswer)
		if err != nil {
			body["errno"] = 1
			body["error"] = err.Error()
			logbuild.WriteString(" 整单退款应答报错 UserOrderCancel ：" + err.Error())
			glog.Info(logbuild.String()) // 记录日志
			return body, err.Error()
		}
		if grpcRes.Code != 200 {
			body["errno"] = 1
			body["error"] = grpcRes.Message + grpcRes.Error
			logbuild.WriteString(" 整单退款应答 UserOrderCancel ：" + grpcRes.Message + grpcRes.Error)
			glog.Info(logbuild.String()) // 记录日志
			return body, grpcRes.Message + grpcRes.Error
		}
	}

	body["errno"] = 0
	body["error"] = ""
	return body, ""
}

// 商家或者系统取消订单不需要同意直接取消的
func SystemOrderCancel(mode dto.ELMOrderStatus) map[string]interface{} {
	body := make(map[string]interface{})
	body["errno"] = 0
	body["error"] = "success"
	var logbuild strings.Builder

	logbuild.WriteString("【推送取消订单】接收饿了么参数：" + kit.JsonEncode(mode)) // 请求参数

	var param oc.CancelOrderRequest
	param.OrderSn = mode.OrderId
	param.CancelReason = mode.Reason
	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.CancelOrder(kit.SetTimeoutCtx(context.Background()), &param)
	if err != nil {
		body["errno"] = 1
		body["error"] = err.Error()
		glog.Error("【推送取消订单】饿了么取消报错, " + err.Error()) // 记录日志
		return body
	}
	if r.Code != 200 {
		body["errno"] = 1
		body["error"] = r.Message
		glog.Error("【推送取消订单】饿了么取消报错, ", kit.JsonEncode(r)) // 记录日志
		return body
	}
	body["errno"] = 0
	body["error"] = ""
	glog.Info(logbuild.String()) // 记录日志
	return body
}

// OrderStatusPush
// 接口描述：平台推送订单当前状态给合作方。
// 目前推送的状态有：5、订单确认； 7、骑士已接单开始取餐（此时可通过订单详情接口获取骑士手机号); 8、骑士已取餐正在配送; 9、订单完成; 10、订单取消; 15、订单退款；推送地址和创建订单地址相同。。
func OrderStatusPush(data string, appChannel int32) map[string]interface{} {
	bodydata := make(map[string]interface{})
	modedata := dto.ELMOrderStatus{}

	//默认返回值
	bodydata["errno"] = 0
	bodydata["error"] = "success"
	err := json.Unmarshal([]byte(data), &modedata)
	if err != nil {
		glog.Info("OrderPartRefund 退款数据解码失败", err)
	}
	redisConn := utils.GetRedisConn()

	//todo 处理业务
	switch modedata.Status {
	case 10: //订单取消
		//lockCard := "lock:elmreturn" + modedata.OrderId
		//defer redisConn.Del(lockCard)
		//lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
		//if !lockRes {
		//	bodydata["errno"] = 1
		//	bodydata["error"] = "redis 锁还没放开"
		//	glog.Info("退款 lock:elmreturn"+modedata.OrderId+"存在：", data)
		//} else {
		//	bodydata = SystemOrderCancel(modedata)
		//}
		glog.Info("取消订单通知 OrderStatusPush ：", data+" "+kit.JsonEncode(bodydata))
	case 5: //订单确认
		lockCard := "lock:ElmOrderConfirm" + modedata.OrderId
		defer redisConn.Del(lockCard)
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
		if !lockRes {
			bodydata["errno"] = 1
			bodydata["error"] = "redis 锁还没放开"
			glog.Info("订单确认 lock:ElmOrderConfirm"+modedata.OrderId+"存在：", data)
		} else {
			bodydata = ElmOrderConfirmCallback(modedata)
		}
	case 9: //订单完成
		lockCard := "lock:ElmOrderCompleted" + modedata.OrderId
		defer redisConn.Del(lockCard)
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
		if !lockRes {
			bodydata["errno"] = 1
			bodydata["error"] = "redis 锁还没放开"
			glog.Info("退款 lock:ElmOrderCompleted"+modedata.OrderId+"存在：", data)
		} else {
			bodydata = ElmOrderCompletedCallback(modedata)
		}
	case 7, 8: //物流变化
		elmOrderDetailModel := getElmOrderDetailModel(modedata.OrderId, appChannel)

		model := new(oc.ElmDeliveryNodeRequest)
		random := rand.Intn(899) + 100
		deliveryId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
		intDeliveryId, _ := strconv.ParseInt(deliveryId, 10, 64)
		model.DeliveryId = intDeliveryId
		model.OrderSn = elmOrderDetailModel.Data.Order.OrderId
		intStatus := 30
		if modedata.Status == 8 {
			intStatus = 50
		}
		model.Status = int32(intStatus)
		model.CourierPhone = elmOrderDetailModel.Data.Order.DeliveryPhone
		if model.CourierPhone != "" {
			model.CourierName = "饿了么骑手"
		}
		model.CreateTime = kit.GetTimeNow()

		ocClient := oc.GetOrderServiceClient()
		var grpcRes *oc.BaseResponse
		grpcRes, err = ocClient.RPC.ElmDeliveryNode(kit.SetTimeoutCtx(context.Background()), model)
		if err != nil {
			glog.Error(elmOrderDetailModel.Data.Order.OrderId, ", 饿了么订单物流状态推送：", err.Error())
			bodydata["errno"] = 1
			bodydata["error"] = "饿了么订单物流状态推送" + err.Error()
			return bodydata
		}
		if grpcRes.Code != 200 {
			glog.Error(elmOrderDetailModel.Data.Order.OrderId, "饿了么订单物流状态推送：", kit.JsonEncode(grpcRes)) // 记录日志
		}
		glog.Info("饿了么订单物流状态推送成功："+elmOrderDetailModel.Data.Order.OrderId+"", modedata.Status) // 记录日志
	}

	return bodydata
}

// OrderDeliveryStatusPush
// order.deliveryStatus.push
// 接口描述：平台推送订单物流状态给合作方。 目前推送的状态有：0:无配送状态；1:待请求配送;2:生成运单; 3:请求配送; 4:等待分配骑士;
// 7:骑士接单;8:骑士取餐;15:配送取消;16:配送完成;17:配送异常;18:自行配送;19:不再配送;20:配送拒单;21:骑士到店。
func OrderDeliveryStatusPush(data string, appChannel int32) map[string]interface{} {
	glog.Info("饿了么订单物流状态推送 OrderDeliveryStatusPush ：", data, ",appChannel", appChannel)
	bodyData := make(map[string]interface{})
	modeData := dto.ElmOrderDeliveryStatus{}
	_ = json.Unmarshal([]byte(data), &modeData)
	//默认返回值
	bodyData["errno"] = 0
	bodyData["error"] = "success"
	if modeData.Status == "7" || modeData.Status == "8" || modeData.Status == "15" || modeData.Status == "16" || modeData.Status == "17" {
		etClient := et.GetExternalClient()
		defer etClient.Close()

		orderDetaiModel := new(et.ELMOrderDetailRequest)
		orderId := modeData.OrderId
		orderDetaiModel.OrderId = orderId
		orderDetaiModel.AppChannel = appChannel
		glog.Info("饿了么订单物流状态推送，查询饿了么详情参数：" + orderId + "")

		grpcResEt, err := etClient.ELMORDER.GetELMOrderDetail(etClient.Ctx, orderDetaiModel)
		if err != nil {
			glog.Error("饿了么订单物流状态推送，查询饿了么详情参数结果："+orderId+"", err.Error())
			return bodyData
		}
		if grpcResEt.Code != 200 {
			glog.Error("饿了么订单物流状态推送，查询饿了么详情参数结果："+orderId+"", grpcResEt.Error)
		}

		var elmOrderDetailModel dto.OrderGetBody
		err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.OrderBody), &elmOrderDetailModel)
		if err != nil {
			glog.Error("饿了么订单物流状态推送：", orderId, err.Error())
		}

		model := new(oc.ElmDeliveryNodeRequest)
		random := rand.Intn(899) + 100
		deliveryId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
		intDeliveryId, _ := strconv.ParseInt(deliveryId, 10, 64)
		model.DeliveryId = intDeliveryId
		model.OrderSn = orderId
		intStatus, _ := strconv.ParseInt(modeData.Status, 10, 32)
		model.Status = int32(intStatus)
		model.CourierPhone = elmOrderDetailModel.Data.Order.DeliveryPhone
		if model.CourierPhone != "" {
			model.CourierName = "饿了么骑手"
		}
		model.CreateTime = kit.GetTimeNow()

		ocClient := oc.GetOrderServiceClient()
		var grpcRes *oc.BaseResponse
		grpcRes, err = ocClient.RPC.ElmDeliveryNode(kit.SetTimeoutCtx(context.Background()), model)
		if err != nil {
			glog.Error(orderId, ", 饿了么订单物流状态推送：", err.Error())
			return bodyData
		}
		if grpcRes.Code != 200 {
			glog.Error(orderId, "饿了么订单物流状态推送：", kit.JsonEncode(grpcRes)) // 记录日志
		}
		glog.Info("饿了么订单物流状态推送成功："+orderId+"", modeData.Status) // 记录日志
		return bodyData
	} else {
		return bodyData
	}
}

// OrderCreate
// 创建订单 接口描述：平台通过source的下行接口地址将订单号推送给合作方，合作方再去拉取订单详情。
func OrderCreate(data string, appChannel int32) map[string]interface{} {
	glog.Info("饿了么创建订单OrderCreate ：", data, "，appChannel:", appChannel)

	body := make(map[string]interface{})
	modedata := dto.ELMOrderCreate{}
	err := json.Unmarshal([]byte(data), &modedata)
	if err != nil {
		glog.Error("创建订单OrderCreate ：", modedata.OrderId, ""+err.Error())
		body["errno"] = 1
		body["error"] = "body格式不正确"
		body["data"] = data
		return body
	}

	redisConn := utils.GetRedisConn()

	lockCard := "lock:elmOrderCreate" + modedata.OrderId
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Minute).Val()
	if !lockRes {
		glog.Info("创建订单OrderCreate1 lock:elmOrderCreate"+modedata.OrderId+"存在：", data)
		body["errno"] = 1
		body["error"] = "订单正在处理中"
		body["data"] = data
		return body
	}
	defer redisConn.Del(lockCard)

	//判断第三方订单号是否存在
	ok := oldOrderSnIsExist(modedata.OrderId)
	if ok {
		body["errno"] = 0
		body["error"] = "success"
		body["data"] = data
		return body
	}

	pushOrderInitialData(modedata.OrderId, 3, data)

	elmOrderDetailModel := getElmOrderDetailModel(modedata.OrderId, appChannel)

	result, resultOrderSn := "fail", ""

	//是否降级;1:是,0:否; 如降级最多重试10次
	if elmOrderDetailModel.Data.Order.DownFlag == 0 {
		result, resultOrderSn = ElmOrderPush(elmOrderDetailModel)
		eLMSourceModel := dto.ELMSource{
			SourceOrderId: resultOrderSn,
		}
		if result == "ok" {
			body["errno"] = 0
			body["error"] = "success"
			body["data"] = eLMSourceModel
		} else {
			body["errno"] = 1
			body["error"] = "error"
			body["data"] = eLMSourceModel
		}
		return body
	} else {
		for i := 0; i <= 10; i++ {
			elmOrderDetailModel = getElmOrderDetailModel(modedata.OrderId, appChannel)
			result, resultOrderSn = ElmOrderPush(elmOrderDetailModel)
			if result == "ok" {
				break
			}
		}
		if result == "ok" {
			body["errno"] = 0
			body["error"] = "success"
			body["data"] = data
		} else {
			body["errno"] = 1
			body["error"] = "error"
			body["data"] = data
		}
		return body
	}
}

// 获取订单详情
func getElmOrderDetailModel(orderId string, appChannel int32) dto.OrderGetBody {
	etClient := et.GetExternalClient()
	defer etClient.Close()

	var elmOrderDetailModel dto.OrderGetBody

	orderDetailModel := new(et.ELMOrderDetailRequest)
	orderDetailModel.OrderId = orderId
	glog.Infof("getElmOrderDetailModel 推送饿了么订单到订单中心，查询饿了么详情参数："+orderId+",appChannel:%s", appChannel)

	orderDetailModel.AppChannel = appChannel

	grpcResEt, err := etClient.ELMORDER.GetELMOrderDetail(etClient.Ctx, orderDetailModel)
	if err != nil {
		glog.Error("getElmOrderDetailModel 推送饿了么订单到订单中心，查询饿了么详情参数结果："+orderId+" ", err.Error())
		return elmOrderDetailModel
	}
	if grpcResEt.Code != 200 {
		glog.Error("getElmOrderDetailModel 推送饿了么订单到订单中心，查询饿了么详情参数结果："+orderId+" ", kit.JsonEncode(grpcResEt))
		return elmOrderDetailModel
	}

	err = json.Unmarshal(kit.JsonEncodeByte(grpcResEt.OrderBody), &elmOrderDetailModel)
	if err != nil {
		glog.Error("getElmOrderDetailModel 推送饿了么订单到订单中心，查询饿了么详情参数结果 ：", orderId, ""+err.Error())
	}
	return elmOrderDetailModel
}

// ElmOrderPush
// 利用查询订单详情接口推送订单到订单中心
func ElmOrderPush(elmOrderDetailModel dto.OrderGetBody) (string, string) {
	orderId := elmOrderDetailModel.Data.Order.OrderId
	glog.Info("推送饿了么订单收到参数：", kit.JsonEncode(elmOrderDetailModel))

	if elmOrderDetailModel.Data.Order.DownFlag != 0 {
		return "fail", ""
	}

	model := new(oc.MtAddOrderRequest)
	model.OrderSn = orderId
	model.OrderIndex = elmOrderDetailModel.Data.Order.OrderIndex
	model.OrderStatus = 20
	model.OrderStatusChild = 20101
	model.ShopId = elmOrderDetailModel.Data.Shop.Id
	model.ShopName = elmOrderDetailModel.Data.Shop.Name
	model.ReceiverName = elmOrderDetailModel.Data.User.Name
	model.ReceiverState = elmOrderDetailModel.Data.User.Province
	model.ReceiverCity = elmOrderDetailModel.Data.User.City
	model.ReceiverDistrict = elmOrderDetailModel.Data.User.District
	model.ReceiverAddress = elmOrderDetailModel.Data.User.Address
	model.ReceiverPhone = elmOrderDetailModel.Data.User.Phone
	intPayType := elmOrderDetailModel.Data.Order.PayType
	//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
	if intPayType == 2 {
		model.PayType = "Cod"
	} else {
		model.PayType = "NoCod"
	}
	model.ReceiverMobile = elmOrderDetailModel.Data.User.Phone
	model.GjpStatus = "Payed"
	model.Total = elmOrderDetailModel.Data.Order.UserFee          //用户实付金额，单位：分
	model.Privilege = elmOrderDetailModel.Data.Order.DiscountFee  //优惠总金额，单位：分
	model.PackingCost = elmOrderDetailModel.Data.Order.PackageFee //包装费，单位：分
	model.Freight = elmOrderDetailModel.Data.Order.SendFee        //配送费 ，单位：分
	model.IsPay = 1
	ctime := elmOrderDetailModel.Data.Order.CreateTime
	model.CreateTime = kit.GetTimeNow(time.Unix(cast.ToInt64(ctime), 0))
	longitude, err := strconv.ParseFloat(elmOrderDetailModel.Data.User.Coord.Longitude, 64)
	model.Longitude = longitude //经度
	latitude, err := strconv.ParseFloat(elmOrderDetailModel.Data.User.Coord.Latitude, 64)
	model.Latitude = latitude //纬度
	model.PickupCode = elmOrderDetailModel.Data.Order.PickUpCode
	model.LogisticsCode = elmOrderDetailModel.Data.Order.DeliveryParty
	sendTime := elmOrderDetailModel.Data.Order.SendTime
	model.ExpectedTime = kit.GetTimeNow(time.Unix(sendTime, 0)) //预计送达时间
	//订单类型1普通订单(默认),2预订订单,3门店自提,4拼团订单,5门店配送
	if elmOrderDetailModel.Data.Order.SendImmediately == 1 {
		model.OrderType = 1
	} else {
		model.OrderType = 2
	}
	model.PayTime = kit.GetTimeNow()
	model.PaySn = ""
	var invoiceData map[string]string
	invoiceData = make(map[string]string)
	invoiceData["发票抬头"] = elmOrderDetailModel.Data.Order.InvoiceTitle
	invoiceData["纳税人识别号"] = elmOrderDetailModel.Data.Order.TaxerId
	model.Invoice = kit.JsonEncode(invoiceData)
	if elmOrderDetailModel.Data.Order.Remark == "0" {
		model.BuyerMemo = ""
	} else {
		model.BuyerMemo = elmOrderDetailModel.Data.Order.Remark
	}
	//1快递 2外卖 3自提
	if elmOrderDetailModel.Data.Order.BusinessType == "0" {
		model.DeliveryType = 2
	} else if elmOrderDetailModel.Data.Order.BusinessType == "1" {
		model.DeliveryType = 3
	} else {
		model.DeliveryType = 1
	}
	//判断饿了么订单来源saas
	storeMasterId, retCode := services.GetAppChannelByStoreId(model.ShopId)
	if retCode != code.Success {
		glog.Error("external-ui:MtUserInfoPhoneNumber", "GetAppChannelByStoreId,", kit.JsonEncode(model), retCode)
		return "fail", ""
	}
	//优惠信息
	//var activeModels []dto.OrdersPrivilegeActive
	if len(elmOrderDetailModel.Data.Discount) > 0 {
		for _, item := range elmOrderDetailModel.Data.Discount {
			var discount dto.OrdersPrivilegeActive
			discount.Actdetailid = cast.ToInt(item.ActivityId)
			discount.Mtcharge = kit.FenToYuan(int64(item.BaiduRate))
			discount.Poicharge = kit.FenToYuan(int64(item.ShopRate))
			discount.Reducefee = kit.FenToYuan(int64(item.Fee))
			discount.Remark = item.Desc
			if item.Type == "jian" {
				discount.Activetype = 401
			} else if item.Type == "xin" {
				discount.Activetype = 402
			} else if item.Type == "zeng" {
				discount.Activetype = 403
			} else if item.Type == "coupon" {
				discount.Activetype = 404
			} else if item.Type == "cashgift_used" {
				discount.Activetype = 405
			} else if item.Type == "g_te" {
				discount.Activetype = 406
			} else if item.Type == "g_zhe" {
				discount.Activetype = 407
			} else if item.Type == "g_jian" {
				discount.Activetype = 408
			} else if item.Type == "g_reduce" {
				discount.Activetype = 409
			} else if item.Type == "g_maizeng" {
				discount.Activetype = 4010
			} else if strings.Contains(item.Desc, "配送") {
				//自己定义的一个类型配送优惠
				discount.Activetype = 4011
			} else {
				discount.Activetype = 100000
			}
			//	activeModels = append(activeModels, discount)
			//优惠信息新表
			orderPromotionItem := oc.OrderPromotionModel{}
			orderPromotionItem.PromotionType = int32(discount.Activetype)
			orderPromotionItem.PromotionTitle = item.Desc
			orderPromotionItem.PoiCharge = item.ShopRate
			orderPromotionItem.PtCharge = item.BaiduRate
			orderPromotionItem.PromotionFee = item.Fee
			model.OrderPromotion = append(model.OrderPromotion, &orderPromotionItem)
		}
	}

	//平台服务费
	model.ServiceCharge = elmOrderDetailModel.Data.Order.Commission
	//订单商品
	var goodsTotal int32 = 0
	var weightTotal int32 = 0
	var skus []int32
	SkuPayTotalMap := make(map[string]int32)
	if len(elmOrderDetailModel.Data.Products) > 0 {
		//计算sku总金额
		for _, item := range elmOrderDetailModel.Data.Products {
			SkuPayTotalMap[item.CustomSkuId] += item.ProductFee - item.ProductSubsidy.Discount + item.ProductSubsidy.BaiduRate
		}

		for _, item := range elmOrderDetailModel.Data.Products {
			var product oc.OrderProductModel
			if storeMasterId == 12 {
				product.Sku = item.CustomSkuSpecId
			} else {
				product.Sku = item.CustomSkuId
			}
			product.ProductType = 1
			product.ProductId = item.BaiduProductId
			product.SubBizOrderId = item.SubBizOrderId
			product.ProductName = item.ProductName
			product.Price = item.ProductPrice
			product.Number = item.ProductAmount
			product.Privilege = item.ProductSubsidy.ShopRate                                         //商家优惠金额
			product.PrivilegePt = item.ProductSubsidy.BaiduRate                                      //平台优惠金额
			product.PrivilegeTotal = item.ProductSubsidy.Discount                                    //总优惠金额
			product.PayPrice = (item.ProductFee - item.ProductSubsidy.Discount) / item.ProductAmount //单个商品实际支付单价
			product.PaymentTotal = item.ProductFee - item.ProductSubsidy.Discount
			product.SkuPayTotal = SkuPayTotalMap[item.CustomSkuId]
			product.MarkingPrice = item.ProductPrice
			model.OrderProductModel = append(model.OrderProductModel, &product)
			goodsTotal += item.ProductFee //不减优惠
			weightTotal += item.TotalWeight
			skus = append(skus, cast.ToInt32(product.Sku))
		}

		model.GoodsTotal = goodsTotal
		model.TotalWeight = weightTotal
	}

	productIdMap := make(map[int32]int32)
	if len(skus) > 0 {
		client := pc.GetDcChannelProductClient()
		defer client.Close()
		var req pc.GetProductIdBySkuIdRequest
		req.SkuId = skus
		//判断饿了么订单来源saas
		if storeMasterId == 12 {
			req.IsSaas = 1
		}
		outProduct, errProduct := client.RPC.GetProductIdBySkuId(client.Ctx, &req)
		if errProduct != nil {
			glog.Error("查询GetProductIdBySkuId失败："+errProduct.Error(), orderId)
			return "fail", ""
		}
		productIdMap = outProduct.Data
	}

	if len(model.OrderProductModel) > 0 {
		for _, item := range model.OrderProductModel {
			intSkuId, _ := strconv.ParseInt(item.Sku, 10, 32)
			if val, ok := productIdMap[int32(intSkuId)]; ok {
				item.ProductId = fmt.Sprintf("%d", val)
			}
		}
	}

	//支付信息
	var payInfo oc.OrderPayInfoModel
	payInfo.OrderSn = model.OrderSn
	payInfo.PaySn = ""
	payInfo.PayMode = 5
	payInfo.PayAmount = model.Total
	payInfo.PayTime = model.PayTime
	model.PayInfo = append(model.PayInfo, &payInfo)

	ocClient := oc.GetOrderServiceClient()
	//加入context渠道信息
	grpcContext := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 3, UserAgent: 6}}
	ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

	glog.Info("ElmOrderPush 推送饿了么订单到订单中心，推送参数：" + kit.JsonEncode(model))
	grpcRes, err := ocClient.Cart.MtSubmitOrder(ctx, model)
	if err != nil {
		glog.Error("ElmOrderPush 推送饿了么订单到订单中心调用接口："+err.Error(), "，", orderId)
		return "fail", ""
	}
	if grpcRes.Code != 200 {
		glog.Error("ElmOrderPush 推送饿了么订单到订单中心调用接口返回: ", orderId, ", ", kit.JsonEncode(grpcRes)) // 记录日志
		return "fail", ""
	}

	glog.Infof("ElmOrderPush 推送饿了么订单到订单中心结果成功: %v，%v", orderId, kit.JsonEncode(grpcRes)) // 记录日志
	return "ok", grpcRes.OrderSn

}

// 获取需要返回的公用参数
func GetReturnPar(body map[string]interface{}, cmd string) map[string]interface{} {
	arr := make(map[string]interface{})

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	arr["cmd"] = cmd
	arr["version"] = "3"
	arr["timestamp"] = timestamp
	arr["ticket"] = utils.Md5ToUUID(timestamp)
	arr["source"] = config.GetString("ElmSource")
	arr["body"] = body
	sign := utils.RequestSign(arr)
	arr["sign"] = sign

	return arr
}

// ElmOrderConfirmCallback 订单确认
func ElmOrderConfirmCallback(mode dto.ELMOrderStatus) map[string]interface{} {
	body := map[string]interface{}{
		"errno": 0,
		"error": "",
	}

	glog.Info("饿了么回调确认订单接收饿了么参数：" + kit.JsonEncode(mode))

	param := &oc.MtAcceptOrderRequest{
		OrderSn: mode.OrderId,
		Status:  "4",
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.MtAcceptOrder(kit.SetTimeoutCtx(context.Background()), param)
	if err != nil {
		glog.Error("饿了么回调确认订单推送ordercenter报错："+err.Error(), mode.OrderId)
		body["errno"] = 1
		body["error"] = err.Error()
		return body
	}
	if r.Code != 200 {
		glog.Error("饿了么回调确认订单推送ordercenter报错："+kit.JsonEncode(r), mode.OrderId)
		body["errno"] = 1
		body["error"] = r.Message + "，" + r.Error
		return body
	}

	glog.Info("饿了么回调确认订单推送ordercenter成功", mode.OrderId)
	return body
}

// ElmOrderCompletedCallback 订单完成
func ElmOrderCompletedCallback(mode dto.ELMOrderStatus) map[string]interface{} {
	body := make(map[string]interface{})
	body["errno"] = 0
	body["error"] = "success"
	glog.Info("饿了么回调订单完成接收饿了么参数：" + kit.JsonEncode(mode))

	var param oc.AccomplishOrderRequest
	param.OrderSn = mode.OrderId
	param.ConfirmTime = kit.GetTimeNow()

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.AccomplishOrder(kit.SetTimeoutCtx(context.Background()), &param)
	if err != nil {
		body["errno"] = 1
		body["error"] = err.Error()
		glog.Error(mode.OrderId, ", 饿了么回调订单完成推送ordercenter报错, ", err.Error())
		return body
	}
	if r.Code != 200 {
		body["errno"] = 1
		body["error"] = r.Message
		glog.Error(mode.OrderId, ", 饿了么回调订单完成推送ordercenter报错, ", kit.JsonEncode(r))
		return body
	}
	body["errno"] = 0
	body["error"] = ""
	glog.Info("饿了么回调订单完成推送ordercenter成功" + mode.OrderId)
	return body
}

func OrderReversePush(data string, appChannel int32) map[string]interface{} {
	var logbuild strings.Builder

	defer func() {
		glog.Info(logbuild.String()) // 记录日志
	}()

	glog.Info("OrderReversePush 进入退款" + data) // 记录日志
	body := make(map[string]interface{})
	//data = `{"platform_shop_id":"6824006998089739852","event_id":"2941850435943956","cur_reverse_event":{"refund_status":50,"refund_reason_desc":"\u5546\u5bb6\u8d85\u65f6\u672a\u63a5\u5355","return_goods_status":0,"operator_role":40,"last_return_goods_status":0,"reason_code":7902,"event_id":"2941850435943956","last_refund_status":0,"refund_order_id":1905697306943956,"occur_time":1680592195000,"reason_content":"","order_id":"*******************","image_list":[]},"refund_order_id":1905697306943956,"order_id":"*******************"}`
	modeData := dto.ELMReversePush{}
	err := json.Unmarshal([]byte(data), &modeData)
	if err != nil {
		glog.Info("OrderReversePush 退款数据解码失败", err)
	}
	glog.Info("OrderReversePush 退款数据解码：", modeData)
	lockCard := "lock:elmreturn:" + modeData.OrderId
	redisConn := utils.GetRedisConn()
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		glog.Info("退款 lock:elmreturn"+modeData.OrderId+"存在：", data)
		body["errno"] = 1
		body["error"] = "redis 锁还没放开"
		return body
	}
	defer redisConn.Del(lockCard)

	//查询逆向单退款详情，组装数据
	etClient := et.GetExternalClient()
	defer etClient.Close()
	grpcResEt, err := etClient.ELMORDER.ElmOrderReverseQuery(etClient.Ctx, &et.ElmOrderReverseQueryRequest{OrderId: modeData.OrderId, AppChannel: appChannel})
	if err != nil {
		glog.Info("退款 查询逆向单order.reverse.query失败："+modeData.OrderId+"：", err)
		body["errno"] = 1
		body["error"] = "查询逆向单order.reverse.query失败！"
		return body
	}
	logbuild.WriteString(" 获取到锁 modeData.OrderId ")
	mData := dto.ELMOPartreFund{}
	mData.OrderId = modeData.OrderId
	mData.RefundId = cast.ToString(modeData.RefundOrderId)
	mData.Status = modeData.CurReverseEvent.RefundStatus
	if modeData.CurReverseEvent.RefundStatus == 20 {
		mData.Status = 50
	} else if modeData.CurReverseEvent.RefundStatus == 50 {
		mData.Status = 20
	}
	if modeData.CurReverseEvent.RefundStatus == 60 {
		mData.Status = 40
	}
	mData.Reason_type = cast.ToString(modeData.CurReverseEvent.ReturnGoodsStatus)
	mData.Reason = modeData.CurReverseEvent.RefundReasonDesc
	mData.AdditionReason = modeData.CurReverseEvent.ReasonContent
	mData.Photos = modeData.CurReverseEvent.ImageList

	mData.FullRefund = 2
	for _, v := range grpcResEt.ReverseOrderList {

		if v.RefundOrderId == cast.ToString(modeData.RefundOrderId) {
			//判断是否全部退款
			if v.IsRefundAll == 1 {
				mData.FullRefund = 1
			}
			mData.DeliveryFee = dto.DeliveryFeeInfo{
				ShopRate: int32(v.DiscountDetail.PlatformDiscountAmount),
			}
			//统计退款金额的时候也只能统计这个订单的
			mData.RefundPrice = cast.ToInt32(v.ApplyRefundUserAmount)

			for _, val := range v.SubReverseOrderList {
				//只要查询这一次的退款数据
				if cast.ToString(val.RefundOrderId) == cast.ToString(modeData.RefundOrderId) && val.VirtualType == "0" {
					mData.RefundProducts = append(mData.RefundProducts, dto.RefundProducts{
						CustomSkuId:   val.CustomSkuId,
						Number:        cast.ToInt32(val.ApplyQuantity),
						Name:          val.SkuName,
						GmIds:         val.GiftRelatedSubBizOrderIdList,
						IsFreeGift:    cast.ToInt32(val.FreeGift),
						SkuId:         cast.ToString(val.PlatformSkuId),
						Upc:           val.Upc,
						SubBizOrderId: val.SubBizOrderId,
						TotalRefund:   cast.ToInt32(val.ApplyRefundUserAmount),
					})

				}
				//只要查询这一次的退款数据配送费
				if cast.ToString(val.RefundOrderId) == cast.ToString(modeData.RefundOrderId) && val.VirtualType == "4" {
					mData.DeliveryPrice += cast.ToInt32(val.ApplyRefundUserAmount)
				}

			}
			break
		}
	}

	switch modeData.CurReverseEvent.OperatorRole {
	case 10:
		mData.Type = 2
	case 20:
		mData.Type = 1
	case 30:
		mData.Type = 3
	case 40:
		mData.Type = 4
	case 50:
		mData.Type = 5
	case 60:
		mData.Type = 6
	}
	mData.LastRefundStatus = modeData.CurReverseEvent.LastRefundStatus
	newdata, _ := json.Marshal(mData)
	//|| (modeData.CurReverseEvent.RefundStatus == 50 && modeData.CurReverseEvent.LastRefundStatus == 0 && modeData.CurReverseEvent.OperatorRole == 10)
	//modeData.CurReverseEvent.RefundStatus == 50 && modeData.CurReverseEvent.LastRefundStatus == 0 && modeData.CurReverseEvent.OperatorRole == 10
	//用户发起的极速退款
	logbuild.WriteString(" 进入退款逻辑 " + string(newdata))
	if modeData.CurReverseEvent.RefundStatus == 10 { //申请
		body = AfterSaleApply(string(newdata))
	} else { //应答
		body = AfterSaleAnserw(string(newdata))
	}

	return body
}

func SkuCreatePush(data string, appChannel int32) map[string]interface{} {
	glog.Info("接收到消息推送-饿了么商家端操作创建商品：", data)
	// 定义返回数据
	ret := make(map[string]interface{})

	// 解析data，获取到参数
	var skuCreatePushDto dto.ElmSkuCreatePush
	err := json.Unmarshal([]byte(data), &skuCreatePushDto)
	if err != nil {
		glog.Info("SkuCreatePush 解析data失败", err)
	}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	// 操作删除，不允许商家端创建商品
	var skuCreateReq = et.UpdateElmShopSkuPriceRequest{
		ShopId:     skuCreatePushDto.ShopId,
		Skuid:      skuCreatePushDto.SkuId,
		AppChannel: appChannel,
	}
	res, err := etClient.ELMPRODUCT.DeleteElmShopSku(etClient.Ctx, &skuCreateReq)
	if err != nil || res.Code != 200 {
		// 记录错误日志
		glog.Error("接收到消息推送-饿了么商家端操作创建商品错误：", kit.JsonEncode(skuCreateReq), err.Error())
		ret["errno"] = -1
		ret["error"] = err.Error()
	}

	ret["errno"] = 0
	ret["error"] = "success"
	return ret
}

func SkuUpdatePush(data string, appChannel int32) map[string]interface{} {
	glog.Info("接收到消息推送-饿了么商家端操作更新商品：", data)
	// 定义返回数据
	ret := make(map[string]interface{})

	// 解析data，获取到参数
	skuUpdatePushDto := dto.ElmSkuUpdatePush{}
	err := json.Unmarshal([]byte(data), &skuUpdatePushDto)
	if err != nil {
		glog.Info("SkuUpdatePush 解析data失败", err)
	}

	pcClient := pc.GetDcChannelProductClient()
	defer pcClient.Close()

	diff := dto.SkuUpdatePushDiff{}
	err = json.Unmarshal([]byte(skuUpdatePushDto.DiffContents), &diff)
	if err != nil {
		glog.Info("SkuUpdatePush 解析DiffContents失败", err)
	}
	result := diff.Status.Result
	origin := diff.Status.Origin
	if result == origin {
		glog.Error("接收到消息推送-饿了么商家端操作更新商品，检查到上下架状态没有变更：", kit.JsonEncode(skuUpdatePushDto))
		ret["errno"] = 0
		ret["error"] = "success"
		return ret
	}
	var elmDownReq = pc.ElmDownProductReq{
		ShopId:      cast.ToString(skuUpdatePushDto.ShopId),
		SkuId:       skuUpdatePushDto.CustomSkuId,
		AppChannel:  appChannel,
		OperateType: cast.ToInt32(result),
	}

	// 回调操作：
	// 1、上架操作，数据库没有找到对应的上架商品：调第三方下架接口
	// 1、下架操作，我方有对应商品并且是上架状态：我方下架
	_, err = pcClient.RPC.ElmDownProduct(pcClient.Ctx, &elmDownReq)
	if err != nil {
		//记录错误日志
		glog.Error("接收到消息推送-饿了么商家端操作删除商品错误：", kit.JsonEncode(elmDownReq), err.Error())
		ret["errno"] = -1
		ret["error"] = err.Error()
		return ret
	}

	ret["errno"] = 0
	ret["error"] = "success"
	return ret
}

func SkuDeletePush(data string, appChannel int32) map[string]interface{} {
	glog.Info("接收到消息推送-饿了么商家端操作删除商品：", data)
	// 定义返回数据
	ret := make(map[string]interface{})

	// 解析data，获取到参数
	var skuDelPushDto dto.ElmSkuDelPush
	err := json.Unmarshal([]byte(data), &skuDelPushDto)
	if err != nil {
		glog.Info("SkuDeletePush 解析data失败", err)
	}

	pcClient := pc.GetDcChannelProductClient()
	defer pcClient.Close()

	var skuDelPushReq = pc.ElmDelProductReq{
		ShopId: skuDelPushDto.ShopId,
		SkuId:  skuDelPushDto.CustomSkuId,
	}
	_, err = pcClient.RPC.ElmDelProduct(pcClient.Ctx, &skuDelPushReq)
	if err != nil {
		//记录错误日志
		glog.Error("接收到消息推送-饿了么商家端操作删除商品错误：", kit.JsonEncode(skuDelPushReq), err.Error())
		ret["errno"] = -1
		ret["error"] = err.Error()
	}

	ret["errno"] = 0
	ret["error"] = "success"
	return ret
}
