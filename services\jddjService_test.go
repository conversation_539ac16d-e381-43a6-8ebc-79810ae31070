package services

import (
	"external-ui/dto"
	"reflect"
	"testing"
)

func TestGetJddjAfsServiceByBillId(t *testing.T) {
	type args struct {
		billId string
	}
	tests := []struct {
		name    string
		args    args
		want    dto.AfsService
		wantErr bool
	}{
		{name: "GetJddjAfsServiceByBillId"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := GetJddjAfsServiceByBillId("35633976", 1)
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("GetJddjAfsServiceByBillId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("GetJddjAfsServiceByBillId() = %v, want %v", got, tt.want)
			}
		})
	}
}
