module external-ui

go 1.18

require (
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.4.2
	github.com/labstack/echo/v4 v4.10.2
	github.com/labstack/gommon v0.4.0
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.0.1
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.22
	github.com/shopspring/decimal v1.2.0
	github.com/spf13/cast v1.3.1
	github.com/swaggo/echo-swagger v1.4.0
	github.com/swaggo/swag v1.16.1
	github.com/tricobbler/echo-tool v0.0.0-**************-b7604d1d0f6d
	github.com/tricobbler/rp-kit v0.0.0-**************-bb22d9ecb4aa
	go.elastic.co/apm/module/apmechov4 v1.8.0
	golang.org/x/exp v0.0.0-**************-509febef88a4
	golang.org/x/net v0.11.0
	google.golang.org/genproto v0.0.0-**************-24fa4b261c55
	google.golang.org/grpc v1.32.0
	google.golang.org/protobuf v1.23.0
)

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/armon/go-radix v1.0.0 // indirect
	github.com/elastic/go-sysinfo v1.1.1 // indirect
	github.com/elastic/go-windows v1.0.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.6 // indirect
	github.com/go-openapi/jsonreference v0.20.2 // indirect
	github.com/go-openapi/spec v0.20.9 // indirect
	github.com/go-openapi/swag v0.22.4 // indirect
	github.com/go-xorm/xorm v0.7.9 // indirect
	github.com/golang-jwt/jwt v3.2.2+incompatible // indirect
	github.com/google/uuid v1.1.2 // indirect
	github.com/joeshaw/multierror v0.0.0-**************-69b34d4ec901 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/prometheus/procfs v0.0.3 // indirect
	github.com/santhosh-tekuri/jsonschema v1.2.4 // indirect
	github.com/swaggo/files/v2 v2.0.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	go.elastic.co/apm v1.8.0 // indirect
	go.elastic.co/apm/module/apmhttp v1.8.0 // indirect
	go.elastic.co/fastjson v1.0.0 // indirect
	golang.org/x/crypto v0.10.0 // indirect
	golang.org/x/sys v0.9.0 // indirect
	golang.org/x/text v0.10.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/tools v0.10.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	howett.net/plist v0.0.0-************31-591f970eefbb // indirect
	xorm.io/builder v0.3.6 // indirect
	xorm.io/core v0.7.2-0.20190928055935-90aeac8d08eb // indirect
)
