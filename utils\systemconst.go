package utils

import (
	"github.com/limitedlee/microservice/common/config"
	"strconv"
)

const (
	//from content-type
	ContentTypeToForm = "application/x-www-form-urlencoded"
	//json content-type
	//ContentTypeToJson = "application/json;charset=UTF-8"

	//byte content-type
	//ContentTypeToByte = "multipart/form-data"

	HttpPost="POST"

	HttpGet="GET"
)

var(
	//美团对接地址
	MtUrl = config.GetString("MtUrl")
	//美团 app secret
	MtAppSecret = config.GetString("MtAppSecret")//"7989c5bdfe4e4a49f8b60079a70e7738"
	//美团分配给APP方的id
	//MtAppId = config.GetString("MtAppId")//int64(4889)
	MtAppId , _ = strconv.ParseInt( config.GetString("MtAppId"), 10, 64)
)

var (
	//美配对接地址
	MpUrl = config.GetString("MpUrl")//"https://peisongopen.meituan.com/api/"
	//美配 app secret
	MpAppSecret =config.GetString("MpAppSecret")//"v[X=$/zXed&nc<79gA;BN._FE)P`U#X4O<k$OE2G)B7{zhF6)e4h!IR1pk?%fGr;" //"v[X=$/zXed&nc<79gA;BN._FE)P`U#X4O<k$OE2G)B7{zhF6)e4h!IR1pk?%fGr;"

	MpAppKey = config.GetString("MpAppKey")//"d15816c2dc244a438d99d27587ee1a14"
)

var (
	//美配自由达 app secret
	MpFreeAppSecret =config.GetString("mp-free-secret")//"GdD!kfXL6T9$h!wk|RcFd]:<7n/|%Qz0hP54Xy]OKWk0FdH//RQSt:+NM9Ja<8l?"

	MpFreeAppKey = config.GetString("mp-free-app-key")//"8627dd29f7594617a761ef9b9f3a5ca9"
)
var (
	//饿了么对接地址
	ElmUrl = config.GetString("ElmUrl")//"https://api-be.ele.me/"
	//饿了么secret
	ElmSecret = config.GetString("ElmSecret")
    //饿了么合作方账号
	ElmSource = config.GetString("ElmSource")//"60948"
)


var (
	//闪送地址
	//IShanSongUrl    =  config.GetString("IssUrl")//测试："http://open.s.bingex.com" 生产：http://open.ishansong.com
	//闪送appId
	IShanSongClientId   =  config.GetString("IssClientId")// 测试："ssEVZY1OAkBUiTa7q"
	//闪送秘钥
	IShanSongAppSecret = config.GetString("IssAppSecret")//测试："iLwrJhwC9bMWKzfyIpxNH286yhsAS7Yt"
	//商家id
	IShanSongShopId =config.GetString("IssShopId") //测试"20000000000001103"
)
