package route

import (
	"external-ui/controller"
	"external-ui/middleware"
	"github.com/labstack/echo/v4"
)

func mtCallbackGroup(e *echo.Group) {
	g := e.Group("/mt-callback", middleware.MTLog("美团"))
	//推送已支付订单
	g.POST("/paid-orders", controller.PaidOrders)
	g.POST("/shop-status", controller.SynchronizeShopStatus)
	//推送已确认订单
	g.POST("/confirm-orders", controller.OrderConfirmCallback)
	//推送已完成订单
	g.POST("/completed-orders", controller.OrderCompletedCallback)
	// 用户或客服取消订单
	g.GET("/cancel-order", controller.SendOrderCencelHandle)
	// 催单
	g.POST("/reminder-callback", controller.ReminderCallback)
	//隐私号降级
	g.POST("/privacy-demotion-callback", controller.PrivacyDemotionCallback)
	//推送全额退款信息
	g.GET("/order-refund-all-callback", controller.OrderRefundALLCallback)
	//推送部分退款信息
	g.GET("/order-refund-portion-callback", controller.OrderRefundPortionCallback)

	//APP方URL 推送客服赔付商家责任订单信息
	g.POST("/order-compensation-callback", controller.CompensationCallback)
	//美团配送的配送状态推送
	g.POST("/order-logisticsstatus-callback", controller.PushMtLogisticsOrderStatus)
	//美团配送的异常状态推送
	g.POST("/order-logisticsexception-callback", controller.MtLogisticsExceptionCall)
	// 用户或客服取消订单
	g.POST("/cancel-order", controller.SendOrderCencelHandle)
	//推送全额退款信息
	g.POST("/order-refund-all-callback", controller.OrderRefundALLCallback)
	//推送部分退款信息
	g.POST("/order-refund-portion-callback", controller.OrderRefundPortionCallback)

	//订单修改
	g.POST("/order-update-callback", controller.OrderUpdateCallback)

	//推送非接口操作更新商品的信息回调URL
	g.POST("/product-update-callback", controller.ProductUpdateCallBack)
	g.GET("/product-update-callback", controller.ProductUpdateCallBack)

	//推送非接口操作创建商品的信息回调URL
	g.POST("/product-new-callback", controller.ProductNewCallBack)
	g.GET("/product-new-callback", controller.ProductNewCallBack)

	//[消息推送]美团商家端操作删除的商品APP方URL
	g.POST("/product-del-callback", controller.ProductDelCallback)

	//[消息推送]美团商家自配送状态变更回调
	g.POST("/self-delivery", controller.SelfDelivery)
}
