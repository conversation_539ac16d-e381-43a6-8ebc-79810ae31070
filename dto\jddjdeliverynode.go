package dto
type JddjDeliveryNode struct {
	//订单号
	OrderSn string `json:"orderId"`
	//标准状态代码，可选值为0：待调度；20：已接单；30：已取货；50：已送达；99：已取消
	//京东到家配送状态 10 等待抢单 20 已抢单 21 配送员取消抢单，等待重新抢单 22 更换配送员
	//23 配送员已到店 25 取货失败 26 取货失败审核驳回 27 取货失败待审核 28 骑士异常上报
	//29 异常上报已处理 30 取货完成 35 投递失败 40 已完成
	Status string `json:"deliveryStatus"`
	//配送员
	CourierName string `json:"deliveryManName"`
	//配送员手机号
	CourierPhone string `json:"deliveryManPhone"`
	//取消原因 失败类型 1 厂家直送拒收 2 厂家直送需退发货 3 厂家直送已退发货 10 等待商家确认收货 11 商家确认收货
	//GTF_1 门店商品缺货 GTF_2 商家未拣货 GTF_5 门店未营业 GTF_6 等待时间长 GTF_7 恶意订单 GTF_8 达达客服取消 DTF_1 配送距离超过5公里
	//DTF_2 联系收货人失败超过3次 DTF_3 客户要求改期配送 DTF_4 客户无理由拒收 DTF_5 质量问题 DTF_6 缺件/少件 DTF_7 订单超时客户拒收 DTF_8 其他
	//DTF_9 恶意刷单 DTF_10 客户更改配送地址 20066 包装不规范/雪糕类无保温袋
	CancelReason string `json:"failType"`
	//	操作时间
	CreateTime string `json:"opTime"`
}

