package services

import (
	"external-ui/dto"
	"fmt"
	"testing"
)

func TestWmCallback_WmProductCreatCallBack(t *testing.T) {
	type args struct {
		c *dto.WeiMengCallBackMsg
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试微盟商品创建消息推送",
			args: args{
				c: &dto.WeiMengCallBackMsg{
					Id:      "0255756c-a948-40ef-ab70-e189f3e90669",
					Topic:   "weimob_shop.encrypt",
					Event:   "storeChange",
					Wid:     "100540577",
					BosId:   4000173417455,
					Sign:    "a83afa1dcc0cb155381d2940d3953e31",
					MsgBody: "{\"vid\":\"6015466316767\",\"goodsId\":102238516254767}",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data := WmProductUpdateCallBack(tt.args.c, "")
			fmt.Println("测试微盟商品创建消息推送: ", data)
		})
	}
}
