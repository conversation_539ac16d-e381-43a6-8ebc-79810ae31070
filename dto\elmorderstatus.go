package dto

type ELMOrderStatus struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	Status int32 `json:"status" form:"status" query:"status"`
	//消息类型。10:发起申请,20:客服介入,
	//30:客服拒绝,40:客服同意,
	//50:商户拒绝,60:商户同意,70:申请失效
	Type int32 `json:"type" form:"type" query:"type"`
	//申请取消原因
	Reason string `json:"reason" form:"reason" query:"reason"`
	//申请取消附加原因
	ResponsibleParty string `json:"responsible_party" form:"responsible_party" query:"responsible_party"`
}
