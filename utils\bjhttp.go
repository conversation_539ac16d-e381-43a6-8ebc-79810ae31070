package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"io/ioutil"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

//dataJson : 数据对象转化成json字符串
func BJHttpPost(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap() {
		req.Header.Set(k, v)
	}

	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap() map[string]string {
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	apiStr := RandStringRunes(16)
	//apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s",
		config.GetString("Product_price_sync_app_secret"),
		apiStr,
		config.GetString("Product_price_sync_app_id"),
		Timestamp,
		config.GetString("Product_price_sync_app_secret"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["apiId"] = config.GetString("Product_price_sync_app_id")
	arr["apiSecre"] = config.GetString("Product_price_sync_app_secret")
	arr["apiStr"] = apiStr
	arr["timestamp"] = Timestamp
	arr["sign"] = md5sign
	return arr
}

func RandStringRunes(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}
