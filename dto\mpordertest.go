package dto
type Mpordertest struct {
	//配送活动标识
	DeliveryId int64 `json:"delivery_id" form:"delivery_id" query:"delivery_id"`
	//美团配送内部订单id，最长不超过32个字符
	MtPeisongId string `json:"mt_peisong_id" form:"mt_peisong_id" query:"mt_peisong_id"`
    //配送服务代码：飞速达:4002，快速达:4011，及时达:4012，集中送:4013，自由达:4014') INT(11)
	DeliveryServiceCode  int  `json:"delivery_service_code" form:"delivery_service_code" query:"delivery_service_code"`
}
