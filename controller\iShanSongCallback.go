package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/oc"
	"external-ui/utils"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// IssPushShopStatus
//@Summary 订单状态回调
// @Tags 订单状态回调
// @Accept json
// @Produce json
// @Param product body dto.IssOrderStatus true " "
// @Success 200 {object} dto.IssOrderStatusResponse
// @Failure 400 {object} dto.IssOrderStatusResponse
// @Router /external/iss-callback/order-status [POST]
func IssPushShopStatus(c echo.Context) error {
	model := new(dto.IssOrderStatus)

	result := new(dto.IssOrderStatusResponse)
	result.Status = http.StatusOK
	result.Msg = "回调成功"

	byteReq, err := ioutil.ReadAll(c.Request().Body)
	glog.Info("IShanSongCallBack:IssPushShopStatus,回调请求参数：" + string(byteReq))

	if err != nil {
		result.Status = http.StatusBadRequest
		result.Msg = "参数读取失败"
		glog.Error("IShanSongCallBack:IssPushShopStatus,参数读取失败")
		return c.JSON(http.StatusBadRequest, result)
	}
	err = json.Unmarshal(byteReq, &model)

	if err != nil {
		result.Status = http.StatusBadRequest
		result.Msg = "参数json解析失败"
		glog.Error("IShanSongCallBack:IssPushShopStatus,参数json解析失败")
		return c.JSON(http.StatusBadRequest, result)
	}

	var params oc.DeliveryNodeRequest
	params.DeliveryType = 1
	params.OrderSn = model.OrderNo
	params.CourierName = model.Courier.Name
	params.CourierPhone = model.Courier.Mobile
	params.CancelReason = model.AbortReason
	params.CreateTime = kit.GetTimeNow()

	limitKey := "limit-rate:iss-order-status-callback:" + model.OrderNo + "-" + cast.ToString(model.Status)
	redisConn := utils.GetRedisConn()

	//使用redis做一个简单的限流 不允许同一个订单同一个状态更新太频繁 qps:5秒内只允许一个
	if redisConn.Get(limitKey).Val() != "" {
		result.Status = http.StatusBadRequest
		result.Msg = "同个订单的同个状态更新太频繁"
		return c.JSON(http.StatusBadRequest, result)
	}

	redisConn.Set(limitKey, "1", 5*time.Second)
	//第三方的配送订单号，因为复用美配的处理逻辑，
	//美配 0 派单中 15骑手已到店 20骑手已接单 30骑手已取货 50已送达 99骑手已取消
	//闪送状态 20：派单中 30：取货中 40：闪送中 50：已完成 60：已取消
	params.MtPeisongId = model.IssOrderNo //配送id的适配
	//将闪送的状态对应到美团的状态中
	switch model.Status {
	case 20:
		params.Status = 0
	case 30:
		params.Status = 20
	case 40:
		//闪送中的单子如果取消的话 ，需要闪送员进行确认 确认之后才能真正的取消，如果没有确认的啥情况下 回调回来的单子状态还会是40
		//闪送员确认之后，将货物送回商家，商家点击确认货物送回之后，闪送才会回调已取消的状态给我们
		//因为等待闪送员确认取消的这个状态，闪送没有特殊的状态与回调，所以此处通过EstimateDeliveryTimeTip+status状态进行判断，
		//不一定可靠，后续可以跟闪送沟通，一个更加可靠的判断条件
		CourierCancelConfirmTip := config.GetString("CourierCancelConfirmTip")
		params.Status = 30
		if model.Courier.EstimateDeliveryTimeTip == CourierCancelConfirmTip {
			params.Status = 45 //等待闪送员确认订单取消
		}
	case 50:
		params.Status = 50
		//闪送的取消
		//自动取消 30分钟无人接单，30分钟没有完成支付
		//商家取消
		//骑手取消，商家，闪送30分钟，则进行取消操作
	case 60:
		params.Status = 99
	default:
		params.Status = 1999 //
	}
	params.Longitude = model.Courier.Longitude
	params.Latitude = model.Courier.Latitude

	// storeMasterId, err := GetAppChannelByOrderSn(params.OrderSn)
	// if err != nil {
	// 	glog.Error("IssPushShopStatus", "GetAppChannelByOrderSn", params.OrderSn, err)
	// 	result.Status = http.StatusBadRequest
	// 	result.Msg = "处理失败"
	// 	//如果是我们处理失败 则要删除限流的key 让闪送接下来能够持续的请求
	// 	redisConn.Del(limitKey)
	// 	return c.JSON(http.StatusBadRequest, result)
	// }
	// params.StoreMasterId = storeMasterId

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.DeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("IShanSongCallBack:IssPushShopStatus 调DeliveryNode,闪送订单："+model.IssOrderNo, r)
	if err != nil || r.Code != 200 {
		result.Status = http.StatusBadRequest
		result.Msg = "处理失败"
		//如果是我们处理失败 则要删除限流的key 让闪送接下来能够持续的请求
		redisConn.Del(limitKey)
		return c.JSON(http.StatusBadRequest, result)
	}
	return c.JSON(http.StatusOK, result)

}

func Get() {
	fmt.Println(123)
}
