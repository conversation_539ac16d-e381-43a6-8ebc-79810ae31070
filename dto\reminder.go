package dto

/// 推送催单消息（必接）
type ReminderRequest struct {
	// 订单号
	Order_id	int64  `json:"order_id" form:"order_id" query:"order_id"`
	// 订单状态推送订单当前的状态。订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消
	Status	int         `json:"status" form:"status" query:"status"`
	// 订单配送方式，商家可在开放平台的【附录】文档中对照查看不同logistics_code对应的描述，
	// 如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等。 如商家想了解自己门店的配送方式以及如何区分等情况，请咨询美团品牌经理
	Logistics_code	string   `json:"logistics_code" form:"logistics_code" query:"logistics_code"`
	// 美团配送订单状态code，目前美团配送状态值有：
	// 0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。
	Logistics_status	int `json:"logistics_status" form:"logistics_status" query:"logistics_status"`
	// 用户催单id，用户发起1次催单后：(1)如商家未回复催单且商品一直未送达，用户每隔10分钟可再发起一次催单；每次催单的remind_id不相同。
	// (2)如商家回复过催单，则用户想再次催单时，仅支持用户通过拨打商家电话联系的方式。
	Remind_id	int64  `json:"remind_id" form:"remind_id" query:"remind_id"`
	// 用户催单时间，为10位秒级时间戳。如用户发起了多次催单，此字段信息会推送多个催单时间。
	Remind_time	string  `json:"remind_time" form:"remind_time" query:"remind_time"`
	// 催单消息发送时间，为13位毫秒级的时间戳。
	Timestamp	int64  `json:"timestamp" form:"timestamp" query:"timestamp"`
}
