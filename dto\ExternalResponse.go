package dto
type ExternalResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//json 格式数据
	Data string `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	//外部接口返回错误码（例如美配，美团）
	ExternalCode         string   `protobuf:"bytes,5,opt,name=external_code,json=externalCode,proto3" json:"external_code"`
}