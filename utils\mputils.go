package utils

import (
	"bytes"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	kit "github.com/tricobbler/rp-kit"

	logger "github.com/maybgit/glog"

	//"github.com/limitedlee/microservice/common/logger"
	"io/ioutil"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"errors"

	proto "external-ui/proto/et"
)

func Mp(jsonStr []byte, url string, deliveryServiceCode int) (*proto.ExternalResponse, error) {

	str := url + "美配请求参数："
	logger.Info(str, string(jsonStr))

	res := new(proto.ExternalResponse)
	res.Code = 200
	res.Message = "Success"

	result, err := HttpPostToMp(string(jsonStr), url, deliveryServiceCode)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
	}

	var mapResult map[string]interface{}
	err = json.Unmarshal(result, &mapResult)
	if err != nil {
		res.Code = 500
		res.Error = err.Error()
		return res, nil
	}

	resCode := mapResult["code"].(float64)
	v := int32(math.Floor(resCode + 0.5))
	if v > 0 {
		res.Code = 400
		res.ExternalCode = fmt.Sprintf("%d", v) //resCode.(float64)
		res.Message = mapResult["message"].(string)
	} else {
		res.Data = kit.JsonEncode(mapResult["data"])
	}
	str1 := url + "美配返回参数："
	logger.Info(str1, res)
	return res, nil
}

func HttpPostToMp(data string, url string, deliveryServiceCode int) ([]byte, error) {
	isFree := false
	if deliveryServiceCode == 4014 {
		isFree = true
	}

	bodyData := SetMpPostData(data, isFree)
	reader := bytes.NewReader([]byte(bodyData))

	url = MpUrl + url
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}

	request.Header.Set("Content-Type", ContentTypeToForm) //"application/json;charset=UTF-8"

	//解决错误 "certificate signed by unknown authority" 取消 https 鉴权
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := http.Client{Transport: tr}
	//client := http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}
	if resp.StatusCode != 200 {
		logger.Error(err.Error())
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}
	return respBytes, nil
}

//设置美配请求参数
//配送开放平台API请求参数分为两种。访问API接口时需要同时提供两种参数。
//系统参数：必传，包含appkey，签名，时间戳，接口版本信息
//签名算法
//为了防止API调用过程中被黑客恶意篡改，调用任何一个API都需要携带签名，开放平台服务端会根据请求参数，对签名进行验证，签名不合法的请求将会被拒绝。
//1）将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
//2）将参数以参数1值1参数2值2...的顺序拼接，例如a=&c=3&b=1，变为b1c3，参数使用utf-8编码
//3）按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
//4）对加密前的字符串进行sha1加密并转为小写字符串，得到签名
//5）将得到的签名赋给sign作为请求的参数
func SetMpPostData(data string, isFree bool) string {
	mapInterface := make(map[string]interface{})
	if len(data) > 0 {
		mapInterface = JsonToMap(data)
	}
	timestamp := time.Now().Unix()

	secret := MpAppSecret
	if isFree {
		mapInterface["appkey"] = MpFreeAppKey
		secret = MpFreeAppSecret
	} else {
		mapInterface["appkey"] = MpAppKey
	}

	mapInterface["timestamp"] = timestamp
	mapInterface["version"] = "1.0"

	// 初始化排序数组
	var array []string
	for key := range mapInterface {
		array = append(array, key)
	}
	// 按照字母编码排序
	sort.Strings(array)

	//拼接签名字符串
	var signStr strings.Builder
	signStr.WriteString(secret)

	var arrayData []string

	for i := 0; i < len(array); i++ {
		for key, item := range mapInterface {
			if array[i] == key {
				typeData := ""
				switch data := item.(type) {
				case string:
					typeData = data
				case bool:
					typeData = strconv.FormatBool(data)
				case int64:
					typeData = strconv.FormatInt(data, 10)
				case float64:
					isInt := IsInt(data)
					if isInt {
						typeData = strconv.FormatInt(int64(data), 10)
					} else {
						typeData = strconv.FormatFloat(data, 'f', -1, 64)
					}
				case []string:
					typeData = kit.JsonEncode(item)
				case []interface{}:
					typeData = kit.JsonEncode(item)
				case map[string]interface{}:
					typeData = kit.JsonEncode(item)
				case json.Number:
					typeData = string(data)
				}

				if len(typeData) > 0 {
					signStr.WriteString(key)
					signStr.WriteString(typeData)
					arrayData = append(arrayData, key+"="+typeData)
				}

			}
		}
	}
	//生成签名 参数
	sign := Sha1(signStr.String())
	//Post 请求需要的字符串
	var strData strings.Builder

	for i := 0; i < len(arrayData); i++ {
		if i != 0 {
			strData.WriteString("&")
		}
		strData.WriteString(arrayData[i])
	}
	strData.WriteString("&sign=" + sign)
	return strData.String()
}

//对字符串进行SHA1哈希
func Sha1(data string) string {
	sha1 := sha1.New()
	sha1.Write([]byte(data))
	return hex.EncodeToString(sha1.Sum([]byte("")))
}

func IsInt(data float64) bool {
	b := math.Floor(data)
	result := false
	if b == data {
		result = true
	}
	return result

}

//JsonToMap  Json 转换成 Map
func JsonToMap(jsonStr string) map[string]interface{} {
	var mapResult map[string]interface{}
	dec := json.NewDecoder(strings.NewReader(jsonStr))
	dec.UseNumber()
	dec.Decode(&mapResult)

	return mapResult
}
