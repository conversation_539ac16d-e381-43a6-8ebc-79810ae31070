// Code generated by protoc-gen-go. DO NOT EDIT.
// source: order/order.proto

package order

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type OriginOrderSaveRequest struct {
	OriginOrderSn string `protobuf:"bytes,1,opt,name=origin_order_sn,json=originOrderSn,proto3" json:"origin_order_sn,omitempty"`
	OrderTime     string `protobuf:"bytes,2,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	OrderJson     string `protobuf:"bytes,3,opt,name=order_json,json=orderJson,proto3" json:"order_json,omitempty"`
	//  订单来源 1阿闻到家,2美团,3饿了么,4京东到家 5物竞天择 6子龙医疗门店 7魔鬼鱼 8:微盟 9:支付宝小程序
	OrderSource          int32    `protobuf:"varint,4,opt,name=order_source,json=orderSource,proto3" json:"order_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OriginOrderSaveRequest) Reset()         { *m = OriginOrderSaveRequest{} }
func (m *OriginOrderSaveRequest) String() string { return proto.CompactTextString(m) }
func (*OriginOrderSaveRequest) ProtoMessage()    {}
func (*OriginOrderSaveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{0}
}

func (m *OriginOrderSaveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OriginOrderSaveRequest.Unmarshal(m, b)
}
func (m *OriginOrderSaveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OriginOrderSaveRequest.Marshal(b, m, deterministic)
}
func (m *OriginOrderSaveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OriginOrderSaveRequest.Merge(m, src)
}
func (m *OriginOrderSaveRequest) XXX_Size() int {
	return xxx_messageInfo_OriginOrderSaveRequest.Size(m)
}
func (m *OriginOrderSaveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OriginOrderSaveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OriginOrderSaveRequest proto.InternalMessageInfo

func (m *OriginOrderSaveRequest) GetOriginOrderSn() string {
	if m != nil {
		return m.OriginOrderSn
	}
	return ""
}

func (m *OriginOrderSaveRequest) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *OriginOrderSaveRequest) GetOrderJson() string {
	if m != nil {
		return m.OrderJson
	}
	return ""
}

func (m *OriginOrderSaveRequest) GetOrderSource() int32 {
	if m != nil {
		return m.OrderSource
	}
	return 0
}

type OriginOrderRefundSaveRequest struct {
	OriginOrderSn  string `protobuf:"bytes,1,opt,name=origin_order_sn,json=originOrderSn,proto3" json:"origin_order_sn,omitempty"`
	OriginRefundSn string `protobuf:"bytes,2,opt,name=origin_refund_sn,json=originRefundSn,proto3" json:"origin_refund_sn,omitempty"`
	OrderTime      string `protobuf:"bytes,3,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	OrderJson      string `protobuf:"bytes,4,opt,name=order_json,json=orderJson,proto3" json:"order_json,omitempty"`
	//订单来源 1阿闻到家,2美团,3饿了么,4京东到家 5物竞天择 6子龙医疗门店 7魔鬼鱼 8:微盟 9:支付宝小程序
	OrderSource          int32    `protobuf:"varint,5,opt,name=order_source,json=orderSource,proto3" json:"order_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OriginOrderRefundSaveRequest) Reset()         { *m = OriginOrderRefundSaveRequest{} }
func (m *OriginOrderRefundSaveRequest) String() string { return proto.CompactTextString(m) }
func (*OriginOrderRefundSaveRequest) ProtoMessage()    {}
func (*OriginOrderRefundSaveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{1}
}

func (m *OriginOrderRefundSaveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OriginOrderRefundSaveRequest.Unmarshal(m, b)
}
func (m *OriginOrderRefundSaveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OriginOrderRefundSaveRequest.Marshal(b, m, deterministic)
}
func (m *OriginOrderRefundSaveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OriginOrderRefundSaveRequest.Merge(m, src)
}
func (m *OriginOrderRefundSaveRequest) XXX_Size() int {
	return xxx_messageInfo_OriginOrderRefundSaveRequest.Size(m)
}
func (m *OriginOrderRefundSaveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OriginOrderRefundSaveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OriginOrderRefundSaveRequest proto.InternalMessageInfo

func (m *OriginOrderRefundSaveRequest) GetOriginOrderSn() string {
	if m != nil {
		return m.OriginOrderSn
	}
	return ""
}

func (m *OriginOrderRefundSaveRequest) GetOriginRefundSn() string {
	if m != nil {
		return m.OriginRefundSn
	}
	return ""
}

func (m *OriginOrderRefundSaveRequest) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *OriginOrderRefundSaveRequest) GetOrderJson() string {
	if m != nil {
		return m.OrderJson
	}
	return ""
}

func (m *OriginOrderRefundSaveRequest) GetOrderSource() int32 {
	if m != nil {
		return m.OrderSource
	}
	return 0
}

//微盟取消订单请求参数
type WeiMengOrderCancelRequest struct {
	OriginOrderSn        string   `protobuf:"bytes,1,opt,name=origin_order_sn,json=originOrderSn,proto3" json:"origin_order_sn,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengOrderCancelRequest) Reset()         { *m = WeiMengOrderCancelRequest{} }
func (m *WeiMengOrderCancelRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderCancelRequest) ProtoMessage()    {}
func (*WeiMengOrderCancelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{2}
}

func (m *WeiMengOrderCancelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderCancelRequest.Unmarshal(m, b)
}
func (m *WeiMengOrderCancelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderCancelRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderCancelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderCancelRequest.Merge(m, src)
}
func (m *WeiMengOrderCancelRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderCancelRequest.Size(m)
}
func (m *WeiMengOrderCancelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderCancelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderCancelRequest proto.InternalMessageInfo

func (m *WeiMengOrderCancelRequest) GetOriginOrderSn() string {
	if m != nil {
		return m.OriginOrderSn
	}
	return ""
}

type WeiMengOrderRefundCancelRequest struct {
	RefundSn             string   `protobuf:"bytes,1,opt,name=refund_sn,json=refundSn,proto3" json:"refund_sn,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengOrderRefundCancelRequest) Reset()         { *m = WeiMengOrderRefundCancelRequest{} }
func (m *WeiMengOrderRefundCancelRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderRefundCancelRequest) ProtoMessage()    {}
func (*WeiMengOrderRefundCancelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{3}
}

func (m *WeiMengOrderRefundCancelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderRefundCancelRequest.Unmarshal(m, b)
}
func (m *WeiMengOrderRefundCancelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderRefundCancelRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderRefundCancelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderRefundCancelRequest.Merge(m, src)
}
func (m *WeiMengOrderRefundCancelRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderRefundCancelRequest.Size(m)
}
func (m *WeiMengOrderRefundCancelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderRefundCancelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderRefundCancelRequest proto.InternalMessageInfo

func (m *WeiMengOrderRefundCancelRequest) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

//获取单据类
type WeiMengAddOrderRequest struct {
	// 渠道订单号(推送给oms平台的推送方订单，如阿闻订单号)
	ChannelOrderSn string `protobuf:"bytes,1,opt,name=channel_order_sn,json=channelOrderSn,proto3" json:"channel_order_sn,omitempty"`
	// 交易单号（在美团等第三方没有对接oms时 取美团订单号阿闻 就取阿闻的订单号）
	TradeOrderSn string `protobuf:"bytes,2,opt,name=trade_order_sn,json=tradeOrderSn,proto3" json:"trade_order_sn,omitempty"`
	// 订单状态：0已取消,1(默认)待审核,2待审核,3待出库 4审核拒绝 5全部出库 6取消
	OrderStatus int32 `protobuf:"varint,3,opt,name=order_status,json=orderStatus,proto3" json:"order_status,omitempty"`
	// 店铺id(财务编码)
	ShopId string `protobuf:"bytes,4,opt,name=shop_id,json=shopId,proto3" json:"shop_id,omitempty"`
	// 商户名称
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name,omitempty"`
	// 发货仓库id
	DeliveryWarehouseCode string `protobuf:"bytes,6,opt,name=delivery_warehouse_code,json=deliveryWarehouseCode,proto3" json:"delivery_warehouse_code,omitempty"`
	// 客户id 阿闻订单取阿闻的顾客id
	MemberId string `protobuf:"bytes,7,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	// 客户名称
	MemberName string `protobuf:"bytes,8,opt,name=member_name,json=memberName,proto3" json:"member_name,omitempty"`
	// 实际支付金额
	PayTotal int32 `protobuf:"varint,9,opt,name=pay_total,json=payTotal,proto3" json:"pay_total,omitempty"`
	//商品实际支付金额
	GoodsTotal int32 `protobuf:"varint,10,opt,name=goods_total,json=goodsTotal,proto3" json:"goods_total,omitempty"`
	// 总优惠金额(分)
	Privilege int32 `protobuf:"varint,11,opt,name=privilege,proto3" json:"privilege,omitempty"`
	// 平台惠金额(分)
	PlatformPrivilege int32 `protobuf:"varint,12,opt,name=platform_privilege,json=platformPrivilege,proto3" json:"platform_privilege,omitempty"`
	// 物流费(分)
	Freight int32 `protobuf:"varint,13,opt,name=freight,proto3" json:"freight,omitempty"`
	//商家运费优惠金额
	FreightPrivilege int32 `protobuf:"varint,14,opt,name=freight_privilege,json=freightPrivilege,proto3" json:"freight_privilege,omitempty"`
	// 包装费(分)
	PackingFee int32 `protobuf:"varint,15,opt,name=packing_fee,json=packingFee,proto3" json:"packing_fee,omitempty"`
	// 订单类型1B2C订单(默认) 2:预定订单
	OrderType int32 `protobuf:"varint,16,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"`
	// 配送类型,1快递,2外卖,3自提,4同城送
	DeliveryType int32 `protobuf:"varint,17,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	// 交易类型 1付款发货 2货到付款
	PayType int32 `protobuf:"varint,18,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`
	// 是否是虚拟订单，0否1是
	IsVirtual int32 `protobuf:"varint,19,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual,omitempty"`
	// 下单日期
	OrderTime string `protobuf:"bytes,20,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	//收件人
	ReceiverName string `protobuf:"bytes,21,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name,omitempty"`
	//收件省
	ReceiverProvince string `protobuf:"bytes,22,opt,name=receiver_province,json=receiverProvince,proto3" json:"receiver_province,omitempty"`
	//收件市
	ReceiverCity string `protobuf:"bytes,23,opt,name=receiver_city,json=receiverCity,proto3" json:"receiver_city,omitempty"`
	//收件区
	ReceiverDistrict string `protobuf:"bytes,24,opt,name=receiver_district,json=receiverDistrict,proto3" json:"receiver_district,omitempty"`
	//收件地址
	ReceiverAddress string `protobuf:"bytes,25,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address,omitempty"`
	//收件电话
	ReceiverPhone string `protobuf:"bytes,26,opt,name=receiver_phone,json=receiverPhone,proto3" json:"receiver_phone,omitempty"`
	// 收货地址纬度
	Latitude float32 `protobuf:"fixed32,27,opt,name=latitude,proto3" json:"latitude,omitempty"`
	// 收货地址经度
	Longitude float32 `protobuf:"fixed32,28,opt,name=longitude,proto3" json:"longitude,omitempty"`
	//买家留言
	BuyerMemo string `protobuf:"bytes,29,opt,name=buyer_memo,json=buyerMemo,proto3" json:"buyer_memo,omitempty"`
	// 备注
	Note string `protobuf:"bytes,30,opt,name=note,proto3" json:"note,omitempty"`
	//用户下单时间
	UserSubmitTime string `protobuf:"bytes,31,opt,name=user_submit_time,json=userSubmitTime,proto3" json:"user_submit_time,omitempty"`
	//订单商品
	OrderProduct []*OrderProduct `protobuf:"bytes,32,rep,name=order_product,json=orderProduct,proto3" json:"order_product,omitempty"`
	//订单优惠
	OrderPromotion []*OrderPromotion `protobuf:"bytes,33,rep,name=order_promotion,json=orderPromotion,proto3" json:"order_promotion,omitempty"`
	//订单支付
	OrderPay []*OrderPay `protobuf:"bytes,34,rep,name=order_pay,json=orderPay,proto3" json:"order_pay,omitempty"`
	// vid用于区分门店的标识字段
	Vid                  string   `protobuf:"bytes,35,opt,name=Vid,proto3" json:"Vid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengAddOrderRequest) Reset()         { *m = WeiMengAddOrderRequest{} }
func (m *WeiMengAddOrderRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengAddOrderRequest) ProtoMessage()    {}
func (*WeiMengAddOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{4}
}

func (m *WeiMengAddOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengAddOrderRequest.Unmarshal(m, b)
}
func (m *WeiMengAddOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengAddOrderRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengAddOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengAddOrderRequest.Merge(m, src)
}
func (m *WeiMengAddOrderRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengAddOrderRequest.Size(m)
}
func (m *WeiMengAddOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengAddOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengAddOrderRequest proto.InternalMessageInfo

func (m *WeiMengAddOrderRequest) GetChannelOrderSn() string {
	if m != nil {
		return m.ChannelOrderSn
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetTradeOrderSn() string {
	if m != nil {
		return m.TradeOrderSn
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetDeliveryWarehouseCode() string {
	if m != nil {
		return m.DeliveryWarehouseCode
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetPayTotal() int32 {
	if m != nil {
		return m.PayTotal
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetGoodsTotal() int32 {
	if m != nil {
		return m.GoodsTotal
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetPrivilege() int32 {
	if m != nil {
		return m.Privilege
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetPlatformPrivilege() int32 {
	if m != nil {
		return m.PlatformPrivilege
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetFreight() int32 {
	if m != nil {
		return m.Freight
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetFreightPrivilege() int32 {
	if m != nil {
		return m.FreightPrivilege
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetPackingFee() int32 {
	if m != nil {
		return m.PackingFee
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetOrderType() int32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverProvince() string {
	if m != nil {
		return m.ReceiverProvince
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverCity() string {
	if m != nil {
		return m.ReceiverCity
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverDistrict() string {
	if m != nil {
		return m.ReceiverDistrict
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetReceiverPhone() string {
	if m != nil {
		return m.ReceiverPhone
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetLatitude() float32 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetLongitude() float32 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *WeiMengAddOrderRequest) GetBuyerMemo() string {
	if m != nil {
		return m.BuyerMemo
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetUserSubmitTime() string {
	if m != nil {
		return m.UserSubmitTime
	}
	return ""
}

func (m *WeiMengAddOrderRequest) GetOrderProduct() []*OrderProduct {
	if m != nil {
		return m.OrderProduct
	}
	return nil
}

func (m *WeiMengAddOrderRequest) GetOrderPromotion() []*OrderPromotion {
	if m != nil {
		return m.OrderPromotion
	}
	return nil
}

func (m *WeiMengAddOrderRequest) GetOrderPay() []*OrderPay {
	if m != nil {
		return m.OrderPay
	}
	return nil
}

func (m *WeiMengAddOrderRequest) GetVid() string {
	if m != nil {
		return m.Vid
	}
	return ""
}

type OrderProduct struct {
	// 货号
	ItemNum string `protobuf:"bytes,1,opt,name=item_num,json=itemNum,proto3" json:"item_num,omitempty"`
	// 商品原单价
	MarkingPrice int32 `protobuf:"varint,2,opt,name=marking_price,json=markingPrice,proto3" json:"marking_price,omitempty"`
	// 外部商品id 用于区分相同商品不同记录
	ChannelProductId int64 `protobuf:"varint,3,opt,name=channel_product_id,json=channelProductId,proto3" json:"channel_product_id,omitempty"`
	// 优惠后的单价
	PayPrice int32 `protobuf:"varint,4,opt,name=pay_price,json=payPrice,proto3" json:"pay_price,omitempty"`
	// 优惠前金额 marking_price * number
	Total int32 `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	// 实付金额
	PaymentTotal int32 `protobuf:"varint,6,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total,omitempty"`
	// 优惠金额  优惠前金额-实付金额
	PrivilegeTotal int32 `protobuf:"varint,7,opt,name=privilege_total,json=privilegeTotal,proto3" json:"privilege_total,omitempty"`
	// sku实付总金额
	SkuPayTotal int32 `protobuf:"varint,8,opt,name=sku_pay_total,json=skuPayTotal,proto3" json:"sku_pay_total,omitempty"`
	// 数量
	Number int32 `protobuf:"varint,9,opt,name=number,proto3" json:"number,omitempty"`
	// 是否赠品 0 否 1是
	IsFree               int32    `protobuf:"varint,10,opt,name=is_free,json=isFree,proto3" json:"is_free,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderProduct) Reset()         { *m = OrderProduct{} }
func (m *OrderProduct) String() string { return proto.CompactTextString(m) }
func (*OrderProduct) ProtoMessage()    {}
func (*OrderProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{5}
}

func (m *OrderProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderProduct.Unmarshal(m, b)
}
func (m *OrderProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderProduct.Marshal(b, m, deterministic)
}
func (m *OrderProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderProduct.Merge(m, src)
}
func (m *OrderProduct) XXX_Size() int {
	return xxx_messageInfo_OrderProduct.Size(m)
}
func (m *OrderProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderProduct.DiscardUnknown(m)
}

var xxx_messageInfo_OrderProduct proto.InternalMessageInfo

func (m *OrderProduct) GetItemNum() string {
	if m != nil {
		return m.ItemNum
	}
	return ""
}

func (m *OrderProduct) GetMarkingPrice() int32 {
	if m != nil {
		return m.MarkingPrice
	}
	return 0
}

func (m *OrderProduct) GetChannelProductId() int64 {
	if m != nil {
		return m.ChannelProductId
	}
	return 0
}

func (m *OrderProduct) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *OrderProduct) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *OrderProduct) GetPaymentTotal() int32 {
	if m != nil {
		return m.PaymentTotal
	}
	return 0
}

func (m *OrderProduct) GetPrivilegeTotal() int32 {
	if m != nil {
		return m.PrivilegeTotal
	}
	return 0
}

func (m *OrderProduct) GetSkuPayTotal() int32 {
	if m != nil {
		return m.SkuPayTotal
	}
	return 0
}

func (m *OrderProduct) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *OrderProduct) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

type OrderPay struct {
	// 交易类型 1付款发货 2货到付款
	PayType int32 `protobuf:"varint,1,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`
	// 支付单号
	PaySn string `protobuf:"bytes,2,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn,omitempty"`
	// 支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
	PayMode int32 `protobuf:"varint,3,opt,name=pay_mode,json=payMode,proto3" json:"pay_mode,omitempty"`
	//支付状态 1未支付 2已支付
	PayStatus int32 `protobuf:"varint,4,opt,name=pay_status,json=payStatus,proto3" json:"pay_status,omitempty"`
	// 支付时间
	PayTime int64 `protobuf:"varint,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	// 实际支付金额
	PayAmount int32 `protobuf:"varint,6,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount,omitempty"`
	//币种 1人民币 2美元
	Currency             int32    `protobuf:"varint,7,opt,name=currency,proto3" json:"currency,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderPay) Reset()         { *m = OrderPay{} }
func (m *OrderPay) String() string { return proto.CompactTextString(m) }
func (*OrderPay) ProtoMessage()    {}
func (*OrderPay) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{6}
}

func (m *OrderPay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPay.Unmarshal(m, b)
}
func (m *OrderPay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPay.Marshal(b, m, deterministic)
}
func (m *OrderPay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPay.Merge(m, src)
}
func (m *OrderPay) XXX_Size() int {
	return xxx_messageInfo_OrderPay.Size(m)
}
func (m *OrderPay) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPay.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPay proto.InternalMessageInfo

func (m *OrderPay) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *OrderPay) GetPaySn() string {
	if m != nil {
		return m.PaySn
	}
	return ""
}

func (m *OrderPay) GetPayMode() int32 {
	if m != nil {
		return m.PayMode
	}
	return 0
}

func (m *OrderPay) GetPayStatus() int32 {
	if m != nil {
		return m.PayStatus
	}
	return 0
}

func (m *OrderPay) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *OrderPay) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *OrderPay) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

type OrderPromotion struct {
	// 优惠活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id,omitempty"`
	// 活动编码
	PromotionCode string `protobuf:"bytes,2,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	// 优惠活动类型
	PromotionType int32 `protobuf:"varint,3,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type,omitempty"`
	// 活动名称
	PromotionTitle string `protobuf:"bytes,4,opt,name=promotion_title,json=promotionTitle,proto3" json:"promotion_title,omitempty"`
	// 商家优惠
	PoiCharge int32 `protobuf:"varint,5,opt,name=poi_charge,json=poiCharge,proto3" json:"poi_charge,omitempty"`
	// 平台优惠
	PtCharge int32 `protobuf:"varint,6,opt,name=pt_charge,json=ptCharge,proto3" json:"pt_charge,omitempty"`
	//总优惠金额(商家加平台总和)
	PromotionFee int32 `protobuf:"varint,7,opt,name=promotion_fee,json=promotionFee,proto3" json:"promotion_fee,omitempty"`
	//开始时间
	StartTime string `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime              int32    `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderPromotion) Reset()         { *m = OrderPromotion{} }
func (m *OrderPromotion) String() string { return proto.CompactTextString(m) }
func (*OrderPromotion) ProtoMessage()    {}
func (*OrderPromotion) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{7}
}

func (m *OrderPromotion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPromotion.Unmarshal(m, b)
}
func (m *OrderPromotion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPromotion.Marshal(b, m, deterministic)
}
func (m *OrderPromotion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPromotion.Merge(m, src)
}
func (m *OrderPromotion) XXX_Size() int {
	return xxx_messageInfo_OrderPromotion.Size(m)
}
func (m *OrderPromotion) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPromotion.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPromotion proto.InternalMessageInfo

func (m *OrderPromotion) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *OrderPromotion) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *OrderPromotion) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *OrderPromotion) GetPromotionTitle() string {
	if m != nil {
		return m.PromotionTitle
	}
	return ""
}

func (m *OrderPromotion) GetPoiCharge() int32 {
	if m != nil {
		return m.PoiCharge
	}
	return 0
}

func (m *OrderPromotion) GetPtCharge() int32 {
	if m != nil {
		return m.PtCharge
	}
	return 0
}

func (m *OrderPromotion) GetPromotionFee() int32 {
	if m != nil {
		return m.PromotionFee
	}
	return 0
}

func (m *OrderPromotion) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *OrderPromotion) GetEndTime() int32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type CommonResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 返回信息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonResponse) Reset()         { *m = CommonResponse{} }
func (m *CommonResponse) String() string { return proto.CompactTextString(m) }
func (*CommonResponse) ProtoMessage()    {}
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{8}
}

func (m *CommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResponse.Unmarshal(m, b)
}
func (m *CommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResponse.Marshal(b, m, deterministic)
}
func (m *CommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResponse.Merge(m, src)
}
func (m *CommonResponse) XXX_Size() int {
	return xxx_messageInfo_CommonResponse.Size(m)
}
func (m *CommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResponse proto.InternalMessageInfo

func (m *CommonResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommonResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type AddOrderResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//oms订单号
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn,omitempty"`
	// 返回信息
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddOrderResponse) Reset()         { *m = AddOrderResponse{} }
func (m *AddOrderResponse) String() string { return proto.CompactTextString(m) }
func (*AddOrderResponse) ProtoMessage()    {}
func (*AddOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{9}
}

func (m *AddOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrderResponse.Unmarshal(m, b)
}
func (m *AddOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrderResponse.Marshal(b, m, deterministic)
}
func (m *AddOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrderResponse.Merge(m, src)
}
func (m *AddOrderResponse) XXX_Size() int {
	return xxx_messageInfo_AddOrderResponse.Size(m)
}
func (m *AddOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrderResponse proto.InternalMessageInfo

func (m *AddOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AddOrderResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *AddOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type WeiMOrderRefundAddRequest struct {
	//    售后单号。可以通过商家后台售后列表或 weimob_shop/rights/list/search 接口获取该 ID。
	RightsId int64 `protobuf:"varint,1,opt,name=rightsId,proto3" json:"rightsId,omitempty"`
	//    订单编号。可以通过 weimob_shop/order/list/search 接口获取该 ID。
	OrderNo int64 `protobuf:"varint,2,opt,name=orderNo,proto3" json:"orderNo,omitempty"`
	//    售后通知信息
	RightsInfo *RightsInfo `protobuf:"bytes,3,opt,name=rightsInfo,proto3" json:"rightsInfo,omitempty"`
	// 第三方的推送消息
	PushMsh              string   `protobuf:"bytes,4,opt,name=pushMsh,proto3" json:"pushMsh,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMOrderRefundAddRequest) Reset()         { *m = WeiMOrderRefundAddRequest{} }
func (m *WeiMOrderRefundAddRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMOrderRefundAddRequest) ProtoMessage()    {}
func (*WeiMOrderRefundAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{10}
}

func (m *WeiMOrderRefundAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMOrderRefundAddRequest.Unmarshal(m, b)
}
func (m *WeiMOrderRefundAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMOrderRefundAddRequest.Marshal(b, m, deterministic)
}
func (m *WeiMOrderRefundAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMOrderRefundAddRequest.Merge(m, src)
}
func (m *WeiMOrderRefundAddRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMOrderRefundAddRequest.Size(m)
}
func (m *WeiMOrderRefundAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMOrderRefundAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMOrderRefundAddRequest proto.InternalMessageInfo

func (m *WeiMOrderRefundAddRequest) GetRightsId() int64 {
	if m != nil {
		return m.RightsId
	}
	return 0
}

func (m *WeiMOrderRefundAddRequest) GetOrderNo() int64 {
	if m != nil {
		return m.OrderNo
	}
	return 0
}

func (m *WeiMOrderRefundAddRequest) GetRightsInfo() *RightsInfo {
	if m != nil {
		return m.RightsInfo
	}
	return nil
}

func (m *WeiMOrderRefundAddRequest) GetPushMsh() string {
	if m != nil {
		return m.PushMsh
	}
	return ""
}

type RightsInfo struct {
	//    销售组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
	Vid int64 `protobuf:"varint,1,opt,name=vid,proto3" json:"vid,omitempty"`
	//    服务组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
	ProcessVid int64 `protobuf:"varint,2,opt,name=processVid,proto3" json:"processVid,omitempty"`
	//售后类型。支持的类型包括：1-退货退款；2-退款；5-退换货。
	RightsType int64 `protobuf:"varint,3,opt,name=rightsType,proto3" json:"rightsType,omitempty"`
	//    售后状态。支持的类型包括：1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中。
	RightsStatus int64 `protobuf:"varint,4,opt,name=rightsStatus,proto3" json:"rightsStatus,omitempty"`
	//    售后方式。支持的类型包括：1-买家申请售后；2-商家取消订单；5-收银台退款；6-外部平台商家售后；7-优惠券到期自动退款；8-系统自动售后；9-商家发起售后；10-付费券发券失败发起售后；20-买家取消订单。
	RightsCauseType int64 `protobuf:"varint,5,opt,name=rightsCauseType,proto3" json:"rightsCauseType,omitempty"`
	//    售后来源。支持的类型包括：0-系统内部订单；100-历史导入订单；101-友朋；102-芸智；103-乐美；104-博申；201-分销市场供货商；401-全渠道导入订单。
	RightsSource int64 `protobuf:"varint,6,opt,name=rightsSource,proto3" json:"rightsSource,omitempty"`
	//    退款类型。支持的类型包括：1-线上退款；2-线下退款；99-无需退款。
	RefundType           int64    `protobuf:"varint,7,opt,name=refundType,proto3" json:"refundType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RightsInfo) Reset()         { *m = RightsInfo{} }
func (m *RightsInfo) String() string { return proto.CompactTextString(m) }
func (*RightsInfo) ProtoMessage()    {}
func (*RightsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{11}
}

func (m *RightsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsInfo.Unmarshal(m, b)
}
func (m *RightsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsInfo.Marshal(b, m, deterministic)
}
func (m *RightsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsInfo.Merge(m, src)
}
func (m *RightsInfo) XXX_Size() int {
	return xxx_messageInfo_RightsInfo.Size(m)
}
func (m *RightsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RightsInfo proto.InternalMessageInfo

func (m *RightsInfo) GetVid() int64 {
	if m != nil {
		return m.Vid
	}
	return 0
}

func (m *RightsInfo) GetProcessVid() int64 {
	if m != nil {
		return m.ProcessVid
	}
	return 0
}

func (m *RightsInfo) GetRightsType() int64 {
	if m != nil {
		return m.RightsType
	}
	return 0
}

func (m *RightsInfo) GetRightsStatus() int64 {
	if m != nil {
		return m.RightsStatus
	}
	return 0
}

func (m *RightsInfo) GetRightsCauseType() int64 {
	if m != nil {
		return m.RightsCauseType
	}
	return 0
}

func (m *RightsInfo) GetRightsSource() int64 {
	if m != nil {
		return m.RightsSource
	}
	return 0
}

func (m *RightsInfo) GetRefundType() int64 {
	if m != nil {
		return m.RefundType
	}
	return 0
}

type WeiMengOrderRefundAddRequest struct {
	// 渠道退款号
	ChannelRefundSn string `protobuf:"bytes,1,opt,name=channel_refund_sn,json=channelRefundSn,proto3" json:"channel_refund_sn,omitempty"`
	//下单时间
	OrderTime string `protobuf:"bytes,2,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	//正向订单号
	ChannelOrderSn string `protobuf:"bytes,5,opt,name=channel_order_sn,json=channelOrderSn,proto3" json:"channel_order_sn,omitempty"`
	// 退款类型 1退款 2退货退款
	RefundType int32 `protobuf:"varint,7,opt,name=refund_type,json=refundType,proto3" json:"refund_type,omitempty"`
	// 退款金额
	RefundAmount int32 `protobuf:"varint,9,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// 退款原因
	RefundReason string `protobuf:"bytes,10,opt,name=refund_reason,json=refundReason,proto3" json:"refund_reason,omitempty"`
	// 备注
	Note string `protobuf:"bytes,11,opt,name=note,proto3" json:"note,omitempty"`
	// 退换货方式
	DeliveryType int32 `protobuf:"varint,12,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	//是否全部退款 是 true ; 否 false
	OrderRefundProduct   *OrderRefundProduct `protobuf:"bytes,13,opt,name=order_refund_product,json=orderRefundProduct,proto3" json:"order_refund_product,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *WeiMengOrderRefundAddRequest) Reset()         { *m = WeiMengOrderRefundAddRequest{} }
func (m *WeiMengOrderRefundAddRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderRefundAddRequest) ProtoMessage()    {}
func (*WeiMengOrderRefundAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{12}
}

func (m *WeiMengOrderRefundAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderRefundAddRequest.Unmarshal(m, b)
}
func (m *WeiMengOrderRefundAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderRefundAddRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderRefundAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderRefundAddRequest.Merge(m, src)
}
func (m *WeiMengOrderRefundAddRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderRefundAddRequest.Size(m)
}
func (m *WeiMengOrderRefundAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderRefundAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderRefundAddRequest proto.InternalMessageInfo

func (m *WeiMengOrderRefundAddRequest) GetChannelRefundSn() string {
	if m != nil {
		return m.ChannelRefundSn
	}
	return ""
}

func (m *WeiMengOrderRefundAddRequest) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *WeiMengOrderRefundAddRequest) GetChannelOrderSn() string {
	if m != nil {
		return m.ChannelOrderSn
	}
	return ""
}

func (m *WeiMengOrderRefundAddRequest) GetRefundType() int32 {
	if m != nil {
		return m.RefundType
	}
	return 0
}

func (m *WeiMengOrderRefundAddRequest) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *WeiMengOrderRefundAddRequest) GetRefundReason() string {
	if m != nil {
		return m.RefundReason
	}
	return ""
}

func (m *WeiMengOrderRefundAddRequest) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *WeiMengOrderRefundAddRequest) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *WeiMengOrderRefundAddRequest) GetOrderRefundProduct() *OrderRefundProduct {
	if m != nil {
		return m.OrderRefundProduct
	}
	return nil
}

type OrderRefundProduct struct {
	// 退款商品货号
	ItemNum string `protobuf:"bytes,1,opt,name=item_num,json=itemNum,proto3" json:"item_num,omitempty"`
	// 是否赠品 0 否 1是
	IsFree int32 `protobuf:"varint,2,opt,name=is_free,json=isFree,proto3" json:"is_free,omitempty"`
	// 退款数量
	RefundNumber int32 `protobuf:"varint,3,opt,name=refund_number,json=refundNumber,proto3" json:"refund_number,omitempty"`
	// 退款价格
	RefundPrice int32 `protobuf:"varint,4,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price,omitempty"`
	// 退款金额
	RefundAmount int32 `protobuf:"varint,5,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	//退款商品记录id
	ItemId               int64    `protobuf:"varint,6,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderRefundProduct) Reset()         { *m = OrderRefundProduct{} }
func (m *OrderRefundProduct) String() string { return proto.CompactTextString(m) }
func (*OrderRefundProduct) ProtoMessage()    {}
func (*OrderRefundProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{13}
}

func (m *OrderRefundProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderRefundProduct.Unmarshal(m, b)
}
func (m *OrderRefundProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderRefundProduct.Marshal(b, m, deterministic)
}
func (m *OrderRefundProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderRefundProduct.Merge(m, src)
}
func (m *OrderRefundProduct) XXX_Size() int {
	return xxx_messageInfo_OrderRefundProduct.Size(m)
}
func (m *OrderRefundProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderRefundProduct.DiscardUnknown(m)
}

var xxx_messageInfo_OrderRefundProduct proto.InternalMessageInfo

func (m *OrderRefundProduct) GetItemNum() string {
	if m != nil {
		return m.ItemNum
	}
	return ""
}

func (m *OrderRefundProduct) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

func (m *OrderRefundProduct) GetRefundNumber() int32 {
	if m != nil {
		return m.RefundNumber
	}
	return 0
}

func (m *OrderRefundProduct) GetRefundPrice() int32 {
	if m != nil {
		return m.RefundPrice
	}
	return 0
}

func (m *OrderRefundProduct) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *OrderRefundProduct) GetItemId() int64 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type OrderRefundAddResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//oms退款单号
	RefundSn string `protobuf:"bytes,2,opt,name=refund_sn,json=refundSn,proto3" json:"refund_sn,omitempty"`
	// 返回信息
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderRefundAddResponse) Reset()         { *m = OrderRefundAddResponse{} }
func (m *OrderRefundAddResponse) String() string { return proto.CompactTextString(m) }
func (*OrderRefundAddResponse) ProtoMessage()    {}
func (*OrderRefundAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fa47a2077d8980ed, []int{14}
}

func (m *OrderRefundAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderRefundAddResponse.Unmarshal(m, b)
}
func (m *OrderRefundAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderRefundAddResponse.Marshal(b, m, deterministic)
}
func (m *OrderRefundAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderRefundAddResponse.Merge(m, src)
}
func (m *OrderRefundAddResponse) XXX_Size() int {
	return xxx_messageInfo_OrderRefundAddResponse.Size(m)
}
func (m *OrderRefundAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderRefundAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderRefundAddResponse proto.InternalMessageInfo

func (m *OrderRefundAddResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderRefundAddResponse) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

func (m *OrderRefundAddResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func init() {
	proto.RegisterType((*OriginOrderSaveRequest)(nil), "order.OriginOrderSaveRequest")
	proto.RegisterType((*OriginOrderRefundSaveRequest)(nil), "order.OriginOrderRefundSaveRequest")
	proto.RegisterType((*WeiMengOrderCancelRequest)(nil), "order.WeiMengOrderCancelRequest")
	proto.RegisterType((*WeiMengOrderRefundCancelRequest)(nil), "order.WeiMengOrderRefundCancelRequest")
	proto.RegisterType((*WeiMengAddOrderRequest)(nil), "order.WeiMengAddOrderRequest")
	proto.RegisterType((*OrderProduct)(nil), "order.OrderProduct")
	proto.RegisterType((*OrderPay)(nil), "order.OrderPay")
	proto.RegisterType((*OrderPromotion)(nil), "order.OrderPromotion")
	proto.RegisterType((*CommonResponse)(nil), "order.CommonResponse")
	proto.RegisterType((*AddOrderResponse)(nil), "order.AddOrderResponse")
	proto.RegisterType((*WeiMOrderRefundAddRequest)(nil), "order.WeiMOrderRefundAddRequest")
	proto.RegisterType((*RightsInfo)(nil), "order.RightsInfo")
	proto.RegisterType((*WeiMengOrderRefundAddRequest)(nil), "order.WeiMengOrderRefundAddRequest")
	proto.RegisterType((*OrderRefundProduct)(nil), "order.OrderRefundProduct")
	proto.RegisterType((*OrderRefundAddResponse)(nil), "order.OrderRefundAddResponse")
}

func init() { proto.RegisterFile("order/order.proto", fileDescriptor_fa47a2077d8980ed) }

var fileDescriptor_fa47a2077d8980ed = []byte{
	// 1715 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x6e, 0x24, 0x49,
	0x11, 0x9e, 0x76, 0xbb, 0xff, 0xa2, 0xff, 0xdc, 0x39, 0xe3, 0x99, 0xb2, 0x67, 0xbc, 0xf6, 0xd4,
	0xc0, 0x62, 0xd8, 0xdd, 0x59, 0x61, 0x24, 0xc4, 0x69, 0x24, 0xe3, 0xd5, 0x0a, 0x83, 0xec, 0xb5,
	0xca, 0xde, 0x19, 0x09, 0x0e, 0xad, 0x72, 0x55, 0xba, 0x3b, 0x99, 0xae, 0xca, 0x22, 0xab, 0xca,
	0xa8, 0xef, 0x9c, 0x79, 0x04, 0x24, 0x6e, 0x1c, 0x79, 0x09, 0x6e, 0x08, 0x09, 0x9e, 0x84, 0x47,
	0x40, 0x19, 0x91, 0x59, 0x3f, 0xdd, 0x6d, 0x23, 0xd0, 0x5e, 0x5a, 0x1d, 0x5f, 0x44, 0x46, 0x46,
	0x46, 0x66, 0x7e, 0x11, 0x59, 0x30, 0x91, 0x2a, 0xe4, 0xea, 0x4b, 0xfc, 0x7d, 0x9b, 0x28, 0x99,
	0x49, 0xd6, 0x42, 0xc1, 0xfd, 0x73, 0x03, 0x9e, 0x7f, 0xa3, 0xc4, 0x4c, 0xc4, 0xdf, 0x68, 0xf9,
	0xda, 0xbf, 0xe7, 0x1e, 0xff, 0x5d, 0xce, 0xd3, 0x8c, 0x7d, 0x0a, 0x63, 0x89, 0x9a, 0x29, 0x9a,
	0x4e, 0xd3, 0xd8, 0x69, 0x1c, 0x35, 0x8e, 0x7b, 0xde, 0x50, 0x56, 0x06, 0xc4, 0xec, 0x00, 0x80,
	0x0c, 0x32, 0x11, 0x71, 0x67, 0x0b, 0x4d, 0x7a, 0x88, 0xdc, 0x88, 0x88, 0x97, 0xea, 0xdf, 0xa6,
	0x32, 0x76, 0x9a, 0x15, 0xf5, 0x2f, 0x53, 0x19, 0xb3, 0xd7, 0x30, 0x30, 0xee, 0x65, 0xae, 0x02,
	0xee, 0x6c, 0x1f, 0x35, 0x8e, 0x5b, 0x5e, 0x1f, 0xb1, 0x6b, 0x84, 0xdc, 0x7f, 0x36, 0xe0, 0x55,
	0x25, 0x46, 0x8f, 0xdf, 0xe5, 0x71, 0xf8, 0xff, 0x44, 0x7a, 0x0c, 0x3b, 0xc6, 0x4e, 0xa1, 0x0f,
	0x6d, 0x48, 0xf1, 0x8e, 0x08, 0x37, 0xae, 0x57, 0xd7, 0xd4, 0x7c, 0x7c, 0x4d, 0xdb, 0xff, 0x6d,
	0x4d, 0xad, 0xf5, 0x35, 0x9d, 0xc1, 0xde, 0x07, 0x2e, 0x2e, 0x78, 0x3c, 0xc3, 0xe0, 0xce, 0xfc,
	0x38, 0xe0, 0x8b, 0xff, 0x71, 0x3d, 0xee, 0x3b, 0x38, 0xac, 0x3a, 0xa1, 0xe8, 0xeb, 0xae, 0x5e,
	0x42, 0xaf, 0x5c, 0x2b, 0x39, 0xe9, 0x2a, 0xb3, 0x4a, 0xf7, 0x8f, 0x00, 0xcf, 0x8d, 0x83, 0xd3,
	0x30, 0x34, 0x3e, 0x68, 0xdc, 0x31, 0xec, 0x04, 0x73, 0x3f, 0x8e, 0xf9, 0x62, 0x35, 0x86, 0x91,
	0xc1, 0x6d, 0x52, 0xbf, 0x07, 0xa3, 0x4c, 0xf9, 0x21, 0x2f, 0xed, 0x28, 0xa5, 0x03, 0x44, 0xad,
	0x55, 0x99, 0x92, 0xcc, 0xcf, 0xf2, 0x14, 0x53, 0x5a, 0xa4, 0x04, 0x21, 0xf6, 0x02, 0x3a, 0xe9,
	0x5c, 0x26, 0x53, 0x11, 0x9a, 0x8c, 0xb6, 0xb5, 0x78, 0x1e, 0xea, 0x35, 0xa0, 0x22, 0xf6, 0x23,
	0xca, 0x65, 0xcf, 0xeb, 0x6a, 0xe0, 0xd2, 0x8f, 0x38, 0xfb, 0x29, 0xbc, 0x08, 0xf9, 0x42, 0xdc,
	0x73, 0xb5, 0x9c, 0xfe, 0xde, 0x57, 0x7c, 0x2e, 0xf3, 0x94, 0x4f, 0x03, 0x19, 0x72, 0xa7, 0x8d,
	0xa6, 0xbb, 0x56, 0xfd, 0xc1, 0x6a, 0xcf, 0x64, 0xc8, 0xb5, 0xd3, 0x88, 0x47, 0xb7, 0x5c, 0xe9,
	0xf9, 0x3a, 0xe4, 0x94, 0x80, 0xf3, 0x90, 0x1d, 0x42, 0xdf, 0x28, 0x71, 0xce, 0x2e, 0xaa, 0x81,
	0x20, 0x9c, 0xf5, 0x25, 0xf4, 0x12, 0x7f, 0x39, 0xcd, 0x64, 0xe6, 0x2f, 0x9c, 0x1e, 0xae, 0xa5,
	0x9b, 0xf8, 0xcb, 0x1b, 0x2d, 0xeb, 0xd1, 0x33, 0x29, 0xc3, 0xd4, 0xa8, 0x01, 0xd5, 0x80, 0x10,
	0x19, 0xbc, 0x82, 0x5e, 0xa2, 0xc4, 0xbd, 0x58, 0xf0, 0x19, 0x77, 0xfa, 0xa8, 0x2e, 0x01, 0xf6,
	0x05, 0xb0, 0x64, 0xe1, 0x67, 0x77, 0x52, 0x45, 0xd3, 0xd2, 0x6c, 0x80, 0x66, 0x13, 0xab, 0xb9,
	0x2a, 0xcc, 0x1d, 0xe8, 0xdc, 0x29, 0x2e, 0x66, 0xf3, 0xcc, 0x19, 0xa2, 0x8d, 0x15, 0xd9, 0x67,
	0x30, 0x31, 0x7f, 0x2b, 0x7e, 0x46, 0x68, 0xb3, 0x63, 0x14, 0xa5, 0x9b, 0x43, 0xe8, 0x27, 0x7e,
	0xf0, 0x51, 0xc4, 0xb3, 0xe9, 0x1d, 0xe7, 0xce, 0x98, 0x82, 0x36, 0xd0, 0xd7, 0xbc, 0x72, 0xe6,
	0xb3, 0x65, 0xc2, 0x9d, 0x1d, 0x8a, 0x9a, 0xae, 0xc4, 0x32, 0xe1, 0xec, 0x0d, 0x0c, 0x8b, 0x7d,
	0x40, 0x8b, 0x09, 0x5a, 0x0c, 0x2c, 0x88, 0x46, 0x7b, 0xd0, 0xc5, 0xb4, 0x69, 0x3d, 0xa3, 0x60,
	0x75, 0xd6, 0xb4, 0xea, 0x00, 0x40, 0xa4, 0xd3, 0x7b, 0xa1, 0xb2, 0xdc, 0x5f, 0x38, 0x4f, 0xc9,
	0xbd, 0x48, 0xdf, 0x13, 0xb0, 0x72, 0x21, 0x9f, 0xad, 0x5e, 0xc8, 0x37, 0x30, 0x54, 0x3c, 0xe0,
	0x7a, 0x26, 0xda, 0xb2, 0x5d, 0x3a, 0x83, 0x16, 0xc4, 0x4d, 0xfb, 0x0c, 0x26, 0x85, 0x51, 0xa2,
	0xe4, 0xbd, 0x88, 0x03, 0xee, 0x3c, 0x47, 0xc3, 0x1d, 0xab, 0xb8, 0x32, 0x78, 0xcd, 0x63, 0x20,
	0xb2, 0xa5, 0xf3, 0xa2, 0xee, 0xf1, 0x4c, 0x64, 0xcb, 0x9a, 0xc7, 0x50, 0xa4, 0x99, 0x12, 0x41,
	0xe6, 0x38, 0x75, 0x8f, 0x5f, 0x19, 0x9c, 0xfd, 0x10, 0x0a, 0x6c, 0xea, 0x87, 0xa1, 0xe2, 0x69,
	0xea, 0xec, 0xa1, 0xed, 0xd8, 0xe2, 0xa7, 0x04, 0xb3, 0xef, 0xc3, 0xa8, 0x8c, 0x74, 0x2e, 0x63,
	0xee, 0xec, 0xd3, 0xfd, 0x2f, 0xc2, 0xd4, 0x20, 0xdb, 0x87, 0xee, 0xc2, 0xcf, 0x44, 0x96, 0x87,
	0xdc, 0x79, 0x79, 0xd4, 0x38, 0xde, 0xf2, 0x0a, 0x59, 0x9f, 0xb1, 0x85, 0x8c, 0x67, 0xa4, 0x7c,
	0x85, 0xca, 0x12, 0xd0, 0xe9, 0xbc, 0xcd, 0x97, 0x5c, 0x4d, 0x23, 0x1e, 0x49, 0xe7, 0x80, 0xd2,
	0x89, 0xc8, 0x05, 0x8f, 0x24, 0x63, 0xb0, 0x1d, 0xcb, 0x8c, 0x3b, 0x9f, 0xa0, 0x02, 0xff, 0x6b,
	0x46, 0xc8, 0x53, 0x7d, 0x81, 0xf3, 0xdb, 0x48, 0x64, 0xb4, 0x0f, 0x87, 0xc4, 0x08, 0x1a, 0xbf,
	0x46, 0x18, 0x37, 0xe3, 0x67, 0x30, 0xa4, 0xbd, 0x4a, 0x94, 0x0c, 0xf3, 0x20, 0x73, 0x8e, 0x8e,
	0x9a, 0xc7, 0xfd, 0x93, 0xa7, 0x6f, 0xa9, 0xfe, 0x20, 0x25, 0x5c, 0x91, 0xca, 0x23, 0x56, 0x30,
	0x12, 0x7b, 0xa7, 0x89, 0xcf, 0x8c, 0x8c, 0x64, 0x26, 0x64, 0xec, 0xbc, 0xc6, 0xb1, 0xbb, 0x2b,
	0x63, 0x49, 0xa9, 0x69, 0xbb, 0x2a, 0xb3, 0xcf, 0xa1, 0x67, 0xc6, 0xfb, 0x4b, 0xc7, 0xc5, 0x91,
	0xe3, 0xda, 0x48, 0x7f, 0xe9, 0x75, 0xa5, 0xf9, 0xc7, 0x76, 0xa0, 0xf9, 0x5e, 0x84, 0xce, 0x1b,
	0x5c, 0x84, 0xfe, 0xeb, 0xfe, 0x6b, 0x0b, 0x06, 0xd5, 0xf0, 0xf4, 0x81, 0x15, 0x19, 0x8f, 0xa6,
	0x71, 0x1e, 0x19, 0xfa, 0xeb, 0x68, 0xf9, 0x32, 0x8f, 0xf4, 0x01, 0x89, 0x7c, 0x85, 0x17, 0x26,
	0x51, 0x22, 0xa0, 0xca, 0xd7, 0xf2, 0x06, 0x06, 0xbc, 0xd2, 0x18, 0xfb, 0x1c, 0x98, 0xa5, 0x51,
	0x93, 0x0c, 0x4d, 0x37, 0x9a, 0xfc, 0x9a, 0x9e, 0x25, 0x58, 0x33, 0x17, 0x11, 0x9d, 0xbe, 0x1e,
	0xe4, 0x6e, 0xbb, 0x60, 0x15, 0x72, 0xf5, 0x0c, 0x5a, 0xc4, 0x27, 0x54, 0x4d, 0x48, 0xd0, 0x51,
	0x24, 0xfe, 0x32, 0xe2, 0x71, 0x66, 0xd8, 0xa6, 0x4d, 0x51, 0x18, 0x90, 0xf8, 0xe6, 0x07, 0x30,
	0x2e, 0x08, 0xc0, 0x98, 0x75, 0xd0, 0x6c, 0x54, 0xc0, 0x64, 0xe8, 0xc2, 0x30, 0xfd, 0x98, 0x4f,
	0x4b, 0x6a, 0xeb, 0x12, 0x4d, 0xa7, 0x1f, 0xf3, 0x2b, 0xcb, 0x6e, 0xcf, 0xa1, 0x1d, 0xe7, 0x9a,
	0x08, 0x0d, 0xef, 0x19, 0x49, 0xd3, 0xb7, 0x48, 0xa7, 0x77, 0x8a, 0x73, 0xc3, 0x78, 0x6d, 0x91,
	0x7e, 0xad, 0x38, 0x77, 0xff, 0xde, 0x80, 0xae, 0xcd, 0x7e, 0x8d, 0x01, 0x1a, 0x75, 0x06, 0xd8,
	0x85, 0xb6, 0x56, 0x15, 0x05, 0xa4, 0x95, 0xf8, 0xcb, 0xeb, 0xd8, 0x8e, 0x88, 0x34, 0xa3, 0x37,
	0x8b, 0x11, 0x17, 0x92, 0x4e, 0x31, 0x8e, 0xa0, 0x92, 0xb2, 0x6d, 0x88, 0xd4, 0x5f, 0x9a, 0x82,
	0x62, 0xe7, 0x12, 0xa6, 0x6c, 0x34, 0x69, 0x2e, 0x53, 0xc0, 0xb5, 0xca, 0x8f, 0x64, 0x1e, 0x67,
	0x26, 0x67, 0x7a, 0xe4, 0x29, 0x02, 0xfa, 0x62, 0x05, 0xb9, 0x52, 0x3c, 0x0e, 0x96, 0x26, 0x53,
	0x85, 0xec, 0xfe, 0x6d, 0x0b, 0x46, 0xf5, 0x63, 0xa8, 0x8b, 0x5b, 0x71, 0x60, 0xf5, 0xfe, 0xd2,
	0xc2, 0xfa, 0x05, 0x76, 0x1e, 0xea, 0x1b, 0x5d, 0x9a, 0x60, 0x75, 0xa2, 0x45, 0x0e, 0x0b, 0x14,
	0xab, 0x52, 0xcd, 0x0c, 0x93, 0x44, 0x4b, 0x2e, 0xcd, 0x30, 0x55, 0xb8, 0xa1, 0x85, 0x99, 0xc8,
	0x16, 0xdc, 0x94, 0xcc, 0x72, 0xf4, 0x8d, 0x46, 0x71, 0x9d, 0x52, 0x4c, 0x83, 0xb9, 0xaf, 0x66,
	0xb6, 0x0f, 0xe9, 0x25, 0x52, 0x9c, 0x21, 0x80, 0x07, 0x2e, 0xb3, 0xda, 0xb6, 0x39, 0x70, 0x99,
	0x51, 0xea, 0xa3, 0x55, 0x4c, 0xa2, 0x6b, 0x42, 0xc7, 0x1c, 0x2d, 0x0b, 0x9a, 0xaa, 0x90, 0x66,
	0xbe, 0x32, 0x7c, 0x40, 0x85, 0xb2, 0x87, 0x08, 0xe6, 0x79, 0x0f, 0xba, 0x3c, 0x0e, 0x49, 0x49,
	0xc7, 0xa5, 0xc3, 0xe3, 0x50, 0xab, 0xdc, 0x77, 0x30, 0x3a, 0x93, 0x51, 0x24, 0x63, 0x8f, 0xa7,
	0x89, 0x8c, 0x53, 0xae, 0x59, 0x07, 0x33, 0x43, 0xe9, 0xc3, 0xff, 0xba, 0xba, 0x45, 0x3c, 0x4d,
	0xfd, 0x99, 0x4d, 0x98, 0x15, 0xdd, 0xdf, 0xc0, 0x4e, 0xd9, 0xb4, 0x3c, 0xe2, 0x61, 0x0f, 0xba,
	0x2b, 0x9d, 0x49, 0x47, 0x9a, 0xa6, 0xa4, 0xe2, 0xbc, 0x59, 0x77, 0xfe, 0xa7, 0x06, 0xf5, 0x67,
	0x95, 0xbe, 0xea, 0x34, 0x0c, 0x6d, 0x73, 0xb4, 0x0f, 0x5d, 0xa5, 0xab, 0x67, 0x7a, 0x4e, 0x7b,
	0xdd, 0xf4, 0x0a, 0x59, 0xfb, 0x44, 0xf7, 0x97, 0x12, 0x67, 0x6b, 0x7a, 0x56, 0x64, 0x3f, 0x06,
	0x30, 0x56, 0xf1, 0x9d, 0xc4, 0x09, 0xfb, 0x27, 0x13, 0xc3, 0x4e, 0x5e, 0xa1, 0xf0, 0x2a, 0x46,
	0xda, 0x59, 0x92, 0xa7, 0xf3, 0x8b, 0x74, 0x6e, 0xf6, 0xd7, 0x8a, 0xee, 0xbf, 0x1b, 0x00, 0xe5,
	0x20, 0x4d, 0x65, 0xf7, 0xc2, 0x06, 0xa3, 0xff, 0xb2, 0x4f, 0x00, 0x12, 0x25, 0x03, 0x9e, 0xa6,
	0x9a, 0xe3, 0x28, 0x94, 0x0a, 0xa2, 0xf5, 0x34, 0xd1, 0x8d, 0x3d, 0x65, 0x4d, 0xaf, 0x82, 0x30,
	0x17, 0x06, 0x24, 0x5d, 0x97, 0xb7, 0xab, 0xe9, 0xd5, 0x30, 0x76, 0x0c, 0x63, 0x92, 0xcf, 0xfc,
	0x3c, 0xe5, 0xe8, 0x88, 0xee, 0xd9, 0x2a, 0x5c, 0xf1, 0x46, 0x1d, 0x71, 0xbb, 0xe6, 0x0d, 0x31,
	0x8c, 0x08, 0x33, 0x8d, 0x8e, 0x3a, 0x26, 0xa2, 0x02, 0x71, 0xff, 0xd0, 0x84, 0x57, 0xeb, 0xed,
	0x6e, 0x65, 0x5b, 0x7e, 0x04, 0x13, 0x4b, 0xb6, 0xab, 0x3d, 0xef, 0xd8, 0x28, 0x1e, 0x68, 0xf0,
	0xd7, 0x1e, 0x2d, 0x9b, 0xda, 0xdf, 0xd6, 0xc6, 0xf6, 0xf7, 0x10, 0xfa, 0x66, 0xb2, 0xcc, 0x86,
	0xdd, 0xaa, 0x86, 0x4d, 0x8d, 0x04, 0x1a, 0x18, 0xb6, 0xa1, 0x7b, 0x30, 0x20, 0xd0, 0x10, 0x4e,
	0x69, 0xa4, 0xb8, 0xaf, 0xdf, 0x14, 0x60, 0xbb, 0x0d, 0x0d, 0x7a, 0x88, 0x15, 0x55, 0xb9, 0x5f,
	0xa9, 0xca, 0x6b, 0x6d, 0xd7, 0x60, 0x43, 0xdb, 0xf5, 0x2b, 0x78, 0x46, 0xab, 0x30, 0x73, 0xd8,
	0xba, 0x3c, 0xc4, 0x33, 0xb8, 0x57, 0xad, 0x90, 0x94, 0x20, 0x5b, 0x9d, 0x99, 0x5c, 0xc3, 0xdc,
	0x7f, 0x34, 0x80, 0xad, 0x9b, 0x3e, 0x56, 0x29, 0x2b, 0x95, 0x61, 0xab, 0x5a, 0x19, 0x2a, 0xab,
	0x36, 0x15, 0xa5, 0x59, 0x4d, 0xcd, 0x25, 0xd5, 0x95, 0xd7, 0x30, 0x28, 0xc2, 0x2e, 0xeb, 0xa2,
	0x49, 0x3a, 0x95, 0xc6, 0xb5, 0x14, 0xb7, 0x36, 0xa4, 0x58, 0x47, 0xa1, 0x03, 0x14, 0xa1, 0x39,
	0x7d, 0x6d, 0x2d, 0x9e, 0x87, 0x6e, 0xa0, 0x5f, 0xc0, 0xf5, 0xf3, 0xf4, 0x08, 0x9d, 0xd4, 0x1e,
	0x54, 0x5b, 0xf5, 0x07, 0xd5, 0xc3, 0x84, 0x72, 0xf2, 0x97, 0x26, 0x3c, 0xad, 0x1e, 0xde, 0x6b,
	0xae, 0xee, 0x75, 0xe8, 0x5f, 0x99, 0xda, 0x78, 0x1a, 0x86, 0xec, 0xc0, 0x6c, 0xc4, 0xe6, 0x27,
	0xd9, 0xfe, 0x0b, 0xa3, 0x5e, 0x65, 0x3d, 0xf7, 0x09, 0xfb, 0x05, 0xf4, 0x2b, 0xcf, 0x48, 0x76,
	0x54, 0x77, 0xb4, 0xfe, 0xc2, 0xdc, 0xb7, 0xfd, 0x54, 0x9d, 0x81, 0xdd, 0x27, 0xec, 0xda, 0x14,
	0xb7, 0x22, 0x19, 0x35, 0x67, 0x1b, 0xef, 0xdd, 0xfe, 0xc1, 0xfa, 0x01, 0xaa, 0x64, 0xd1, 0x7d,
	0xc2, 0xde, 0xc3, 0xa4, 0xa2, 0xfb, 0x36, 0x09, 0xfd, 0x8c, 0x7f, 0x17, 0x7e, 0x6f, 0x6a, 0x7e,
	0xcd, 0xe2, 0x3f, 0xdd, 0xb0, 0xf8, 0x0d, 0x2f, 0xe3, 0x07, 0x53, 0x70, 0xf2, 0xd7, 0x86, 0x69,
	0x02, 0xed, 0x1e, 0x9d, 0xc3, 0x78, 0xe5, 0x13, 0x09, 0x2b, 0x43, 0xdb, 0xf4, 0xe9, 0xe4, 0xe1,
	0xf4, 0x7e, 0x0b, 0xbb, 0x1b, 0xbf, 0x64, 0xb0, 0x37, 0xeb, 0x0e, 0xd7, 0xbe, 0x73, 0x3c, 0xe8,
	0xf6, 0xe7, 0x4f, 0x7f, 0x3d, 0x79, 0xfb, 0xa5, 0x8c, 0xd2, 0x2f, 0xf0, 0xdb, 0x0e, 0x7d, 0xe7,
	0xb9, 0x6d, 0xa3, 0xf0, 0x93, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xfb, 0x37, 0x21, 0x7c, 0xfd,
	0x11, 0x00, 0x00,
}
