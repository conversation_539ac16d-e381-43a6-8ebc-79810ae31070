package services

import (
	"encoding/json"
	"errors"
	"external-ui/dto"
	"external-ui/oms-proto/pc"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

/*
创建商品消息
weimob_shop.goods/create
https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=2481&id=3288&isold=2
*/
func WmProductCreatCallBack(callBackData *dto.WeiMengCallBackMsg, strBody string) error {
	glog.Info("WmProductCreatCallBack: ", kit.JsonEncode(callBackData), " strBody: ", strBody)

	body := callBackData.MsgBody
	if len(body) <= 0 {
		return nil
	}
	var msgBody = dto.MsgBody{}
	err := json.Unmarshal([]byte(body), &msgBody)
	if err != nil {
		glog.Error("WmProductCreatCallBack格式化异常： ", err.Error())
		return err
	}
	productClient := pc.GetOmsProductClient()

	vo := pc.WmProductCreatOrUpdateVo{
		Type:     1,
		GooidsId: cast.ToString(msgBody.GoodsId),
		Vid:      msgBody.Vid,
	}

	addRes, err := productClient.Product.WmProductCreateOrUpdateCallBack(productClient.Ctx, &vo)
	if err != nil {
		glog.Error("调用oms商品新增信息异常： ", err.Error(), "  vo参数:", kit.JsonEncode(vo))
		return errors.New("调用oms商品新增信息异常")
	}
	glog.Info("addRes: ", addRes)

	return nil
}

// 修改商品消息推送 weimob_shop.goods/update
//https://doc.weimobcloud.com/detail?menuId=19&childMenuId=18&tag=2481&id=2612&isold=2
func WmProductUpdateCallBack(callBackData *dto.WeiMengCallBackMsg, strBody string) error {

	glog.Info("WmProductUpdateCallBack: ", kit.JsonEncode(callBackData), " strBody: ", strBody)

	body := callBackData.MsgBody
	if len(body) <= 0 {
		return nil
	}
	var msgBody = dto.MsgBody{}
	err := json.Unmarshal([]byte(body), &msgBody)
	if err != nil {
		glog.Error("WmProductUpdateCallBack格式化异常： ", err.Error())
		return err
	}
	productClient := pc.GetOmsProductClient()

	vo := pc.WmProductCreatOrUpdateVo{
		Type:     2,
		GooidsId: cast.ToString(msgBody.GoodsId),
		Vid:      msgBody.Vid,
	}

	addRes, err := productClient.Product.WmProductCreateOrUpdateCallBack(productClient.Ctx, &vo)
	if err != nil {
		glog.Error("调用oms商品编辑信息异常： ", err.Error(), "  vo参数:", kit.JsonEncode(vo))
		return errors.New("调用oms商品编辑信息异常")
	}
	glog.Info("addRes: ", addRes)
	return nil
}
