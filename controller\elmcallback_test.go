package controller

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"

	"github.com/labstack/echo/v4"
)

func createMockContext(params map[string][]string) echo.Context {
	e := echo.New()
	q := make(url.Values)
	for k, v := range params {
		for _, item := range v {
			q.Add(k, item)
		}
	}
	req := httptest.NewRequest(http.MethodPost, "/?"+q.Encode(), strings.NewReader(""))
	rec := httptest.NewRecorder()
	return e.NewContext(req, rec)
}

func TestElmCallback(t *testing.T) {
	type args struct {
		c echo.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试饿了么订单创建回调",
			args: args{
				c: createMockContext(map[string][]string{
					"body":      {`{"platform_shop_id":"6824006998089739852","order_id":"4056390177923669549"}`},
					"cmd":      {"order.create"},
					"encrypt":  {""},
					"sign":     {"469E395630DD3579F858F987694C91B1"},
					"source":   {"28547977"},
					"ticket":   {"E1F7B460-AE9E-4334-A704-6950299320D0"},
					"timestamp": {"1737642584"},
					"version":   {"3"},
				}),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if err := ElmCallback(tt.args.c); (err != nil) != tt.wantErr {
				t.Errorf("ElmCallback() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAfterSaleApply(t *testing.T) {
	type args struct {
		data string
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				data: "{\"platform_shop_id\":\"100000288783\",\"delivery_fee\":{\"shop_rate\":0,\"user_rate\":10},\"reason\":\"\\u6211\\u4e0d\\u60f3\\u8981\\u4e86\",\"addition_reason\":\"\\u5b85\\u7537\\u5973\\u795e\\u5728\\u5417\\u5728\\u5417\",\"refund_products\":[{\"gm_ids\":[],\"is_free_gift\":0,\"number\":1,\"product_feature\":[],\"total_refund\":100,\"custom_sku_id\":\"1023527001\",\"name\":\"248-A8\\u5b9e\\u7269\\u5546\\u54c1F3FIB\",\"upc\":\"1624880263851\",\"shop_ele_refund\":0,\"sku_id\":\"16256467622229743\",\"sub_biz_order_id\":\"1949519953479673536\"}],\"type\":2,\"order_id\":\"2155297502344306207\",\"photos\":[],\"refund_id\":\"666975524673635\",\"is_refund_all\":1,\"refund_price\":110,\"status\":10}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AfterSaleApply(tt.args.data); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AfterSaleApply() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderReversePush(t *testing.T) {
	type args struct {
		data       string
		appChannel int32
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		// TODO: Add test cases.
		{name: "111"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := OrderReversePush("", 1); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderReversePush() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSkuUpdatePush(t *testing.T) {
	type args struct {
		data       string
		appChannel int32
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				data:       `{"store_id":353163459,"category_name":"\u8fdb\u53e3\u72d7\u7cae","updateAppKey":"xy_eb","item_id":846180448325,"updateUserName":"xinruitest","updateReason":"\u65b0\u96f6\u552e\u8fd0\u8425\u4e2d\u5fc3\/\u997f\u4e86\u4e48\u96f6\u552e\u5546\u5bb6\u7248\u5355\u4e2a\u9875\u9762\u8bf7\u6c42","upc":"202410150001","sku_id":"17294984912284342","updateTime":1729564151541,"category_list":[{"category_name":"\u8fdb\u53e3\u72d7\u7cae","category_id":166866724022266,"rank":55}],"shop_id":"6824006998089739852","category_id":280447256,"cat3_id":201228844,"custom_sku_id":"1051504001","name":"\u561f\u561f\u53e3\u7cae","diffContents":"{\"status\":{\"origin\":\"1\",\"result\":\"0\"}}","seller_id":2209517220622}`,
				appChannel: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SkuUpdatePush(tt.args.data, tt.args.appChannel); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SkuUpdatePush() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSkuCreatePush(t *testing.T) {
	type args struct {
		data       string
		appChannel int32
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				data:       `{"store_id":353163459,"createAppKey":"xy_eb","item_id":846390600313,"upc":"wm59966646082263","createUserName":"xinruitest","sku_id":"17295677322264259","category_list":[{"category_name":"\u4e3b\u7cae\u4e13\u533a03","category_id":166866724522301}],"shop_id":"6824006998089739852","category_id":280332382,"createTime":1729567732867,"cat3_id":201229332,"name":"\u5546\u54c1a","seller_id":2209517220622}`,
				appChannel: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SkuCreatePush(tt.args.data, tt.args.appChannel); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SkuCreatePush() = %v, want %v", got, tt.want)
			}
		})
	}
}
