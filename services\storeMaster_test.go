package services

import "testing"

func TestGetAppChannelByOrderSn(t *testing.T) {
	type args struct {
		ordersn string
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ordersn: "27009853477201341",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetAppChannelByOrderSn(tt.args.ordersn)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAppChannelByOrderSn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.<PERSON>rf("GetAppChannelByOrderSn() got = %v, want %v", got, tt.want)
			}
		})
	}
}
