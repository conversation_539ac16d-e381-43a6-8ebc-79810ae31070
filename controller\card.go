package controller

import (
	"external-ui/proto/cc"
	"external-ui/proto/oc"

	"github.com/labstack/echo/v4"
)

// @Summary 会员卡通过门店开卡（储值卡赠送）
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardNewByStoreReq true " "
// @Success 200 {object} oc.CardNewByStoreRes
// @Failure 400 {object} oc.CardNewByStoreRes
// @Router /external/card/new-by-store [POST]
func CardNewByStore(c echo.Context) error {
	req := new(oc.CardNewByStoreReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardNewByStoreRes{Code: 400, Message: err.Error()})
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.NewByStore(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 检查卡模板是否有效
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CheckCardIdReq true " "
// @Success 200 {object} oc.CheckCardIdRes
// @Failure 400 {object} oc.CheckCardIdRes
// @Router /external/card/check-card-id [POST]
func CardCheckCardId(c echo.Context) error {
	req := new(oc.CheckCardIdReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CheckCardIdRes{Code: 400, Message: err.Error()})
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.CheckCardId(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CheckCardIdRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 查询卡权益价值
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body cc.CardValueReq true " "
// @Success 200 {object} cc.CardValueRes
// @Failure 400 {object} cc.CardValueRes
// @Router /external/card/card-value [GET]
func CardValue(c echo.Context) error {
	req := new(cc.CardValueReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.CardValueRes{Code: 400, Message: err.Error()})
	}

	client := cc.GetCustomerCenterClient()
	if out, err := client.VipCard.CardValue(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.CardValueRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// CardReturnByStore @Summary 会员卡通过门店退卡（储值卡赠送退卡）
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardPayNotifyReq true " "
// @Success 200 {object} oc.CardBaseResponse
// @Failure 400 {object} oc.CardBaseResponse
// @Router /external/card/return-by-store [POST]
func CardReturnByStore(c echo.Context) error {
	req := new(oc.CardPayNotifyReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.CardReturnByStore(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// CardOrderValue
// @Summary 查询会员卡下已使用子龙门店券赠送价值
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body cc.CardOrderValueReq true " "
// @Success 200 {object} cc.CardOrderValueResp
// @Failure 400 {object} cc.CardOrderValueResp
// @Router /external/card/card-order-value [POST]
func CardOrderValue(c echo.Context) error {
	req := new(cc.CardOrderValueReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.CardOrderValueResp{Code: 400, Message: err.Error()})
	}

	client := cc.GetCustomerCenterClient()
	if out, err := client.VipCard.CardOrderValue(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.CardValueRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
