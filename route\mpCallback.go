package route

import (
	"external-ui/controller"
	myMiddleware "external-ui/middleware"
	"github.com/labstack/echo/v4"
)

func mpCallbackGroup(e *echo.Group) {
	g := e.Group("/mp-callback", myMiddleware.MTLog("美配"))
	//订单状态回调
	g.POST("/order-status", controller.PushOrderStatus)
	//订单异常回调
	g.POST("/order-unusual", controller.PushOrderUnusual)
	//门店状态回调
	g.POST("/shop-status", controller.PushShopStatus)
	//配送范围变更回调
	g.POST("/shop-scope", controller.PushShopScope)
}
