package dto

type AfsService struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
	Result  struct {
		AfsDetailList []struct {
			AfsMoney           int `json:"afsMoney"` // 应退商品金额+应退积分金额（单位为分，售后单原始总金额-京豆金额-优惠总金额）
			AfsSkuDiscountList []struct {
				DetailDiscountType int `json:"detailDiscountType"` // 二级优惠类型(优惠码(1:满减;2:立减;3:满折);优惠券(1:满减;2:立减;3:免运费劵;4:运费优惠N元;5满折);满件减(1206:满件减);满件折(1207:满件折))
				DiscountType       int `json:"discountType"`       // 一级优惠类型(1:优惠码;3:优惠劵;4:满减;5:满折;6:首单优惠;7:VIP免运费;8:商家满免运费;10:满件减;11:满件折;12:首单地推满免运费)
				PlatPayMoney       int `json:"platPayMoney "`      // 订单优惠平台承担金额
				VenderPayMoney     int `json:"venderPayMoney "`    // 订单优惠商家承担金额
			} `json:"afsSkuDiscountList"` // 订单级优惠列表
			CashMoney                   int     `json:"cashMoney"`                    // 应退商品金额（单位为分，应退商品金额(实际用户支付金额)=售后单原始总金额-京豆金额-优惠总金额-到家积分金额）
			JdBeansMoney                int     `json:"jdBeansMoney"`                 // 售后单实际退用户京豆支付金额（单位为分）
			MealBoxMoney                int     `json:"mealBoxMoney"`                 // 应退订单餐盒费
			PayPrice                    int     `json:"payPrice"`                     // 单价（该SKU单价）
			PlatPayMoney                int     `json:"platPayMoney "`                // 单品优惠平台承担金额
			PlatformIntegralDeductMoney int     `json:"platformIntegralDeductMoney "` // 该sku此次售后实际退用户积分支付金额
			PromotionType               int     `json:"promotionType"`                // 商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,8:第二件N折)
			SkuCount                    int     `json:"skuCount"`                     // 申请数量（该SKU本次申请售后个数）
			SkuIDIsv                    string  `json:"skuIdIsv"`                     // 商家商品编码
			SkuMoney                    int     `json:"skuMoney"`                     // 该sku该售后单原始总金额（skuMoney=payPrice*skuCount）
			SkuSpecification            string  `json:"skuSpecification"`             // 商品规格，多规格之间用英文分号;分隔
			UpcCode                     string  `json:"upcCode"`                      // 商家商品UPC码
			VenderPayMoney              int     `json:"venderPayMoney "`              // 单品优惠商家承担金额
			VirtualMoney                int     `json:"virtualMoney"`                 // 售后单总优惠金额（单位为分，售后单维度的总优惠金额=SKU维度总优惠金额相加）
			WareID                      int     `json:"wareId"`                       // 到家商品编码
			WareName                    string  `json:"wareName"`                     // 到家商品名称
			Weight                      float64 `json:"weight"`                       // 单个商品重量
		} `json:"afsDetailList"` // 商品列表
		AfsFreight                  int    `json:"afsFreight"`                  // 运费(退货取件费)；在售后单为达达同城送订单时，该字段为0
		AfsMoney                    int    `json:"afsMoney"`                    // 应退商品金额+应退积分金额（单位为分，售后单原始总金额-京豆金额-优惠总金额）
		AfsOrderType                int    `json:"afsOrderType"`                // 售后申请来源：10：到家售后单，20：京东售后单
		AfsServiceOrder             string `json:"afsServiceOrder"`             // 售后单号
		AfsServiceState             int    `json:"afsServiceState"`             // 售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,50:客户取消,60:商家收货审核不通过,70:已解决, 91:直赔,92:直赔成功,93:直赔失败,90:待赔付, 110:待退货,111:取货成功,1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
		ApplyDeal                   string `json:"applyDeal"`                   // 售后单类型（10:仅退款 ,30:直赔，40:退货退款），仅退款/退货退款由用户发起，直赔由商家发起
		ApprovePin                  string `json:"approvePin"`                  // 审核人
		ApproveType                 int    `json:"approveType"`                 // 审核方（1 到家客服，2 商家，3 商家超时自动审核，4 自动取消，5 平台自动审核，6 客服超时自动审核）
		ApprovedDate                int    `json:"approvedDate"`                // 审核时间
		BusinessType                int    `json:"businessType"`                // 订单业务类型(1：京东到家商超，2：京东到家美食，4：京东到家开放仓，5：哥伦布店内订单，6：货柜订单，8：轻松购订单，9：是自助收银，10：超级会员码)
		CarriersNo                  string `json:"carriersNo"`                  // 承运商（9966：众包配送，2938：商家自送,1130:达达同城送）
		CashMoney                   int    `json:"cashMoney"`                   // 应退商品金额（单位为分，应退商品金额(实际用户支付金额)=售后单原始总金额-京豆金额-优惠总金额-到家积分金额）
		ChangeDeliveryReason        string `json:"changeDeliveryReason"`        // 同城送失败转商家自送提示文案
		CreateTime                  int    `json:"createTime"`                  // 创建时间
		CustomerMobilePhone         string `json:"customerMobilePhone"`         // 客户电话
		CustomerName                string `json:"customerName"`                // 客户姓名
		CustomerPin                 string `json:"customerPin"`                 // 下单顾客账号（到家账号）
		DeliveryMan                 string `json:"deliveryMan"`                 // 配送员姓名，商家自送时没有
		DeliveryManNo               string `json:"deliveryManNo"`               // 配送员编号，商家自送时没有
		DeliveryMobile              string `json:"deliveryMobile"`              // 配送员电话，商家自送时没有
		DeliveryNo                  string `json:"deliveryNo"`                  // 运单号（承运商为众包时的退货单才有；承运商为商家自送时没有）
		DeliveryState               string `json:"deliveryState"`               // 运单状态，指众包配送的退货单的运单状态(0 待抢单,-1 取消,10 已抢单,20 配送中,30 已妥投);
		DutyAssume                  int    `json:"dutyAssume"`                  // 责任承担方（1:京东到家，2: 商家，3:物流，4:客户，5:其他）
		ExternalDongCoupon          int    `json:"externalDongCoupon"`          // 东券金额
		ExternalJingCoupon          int    `json:"externalJingCoupon"`          // 京券金额
		ExternalJingDou             int    `json:"externalJingDou"`             // 京豆金额
		ExternalOnlinePayment       int    `json:"externalOnlinePayment"`       // 在线支付金额
		ExternalOrderID             string `json:"externalOrderId"`             // 外部订单号
		ExternalOtherPayment        int    `json:"externalOtherPayment"`        // 其他支付金额
		ExternalServiceOrder        string `json:"externalServiceOrder"`        // 外部售后单号
		GiftCardFreightMoney        int    `json:"giftCardFreightMoney"`        // 礼品卡餐盒费
		GiftCardMealBoxMoney        int    `json:"giftCardMealBoxMoney"`        // 礼品卡抵扣-餐盒费
		GiftCardPackagingMoney      int    `json:"giftCardPackagingMoney"`      // 礼品卡包装费
		GiftCardProductMoney        int    `json:"giftCardProductMoney"`        // 礼品卡货款
		IsLastApply                 int    `json:"isLastApply"`                 // 是否是最后一单,1代表最后一次申请,可以根据这个字段判断是部分退款还是整单退款，1表示整单退完。判断是否整单退款，还需要结合afsServiceState中的状态，只有afsServiceState=32或114，且isLastApply=1才表示原订单整单退完。 例如：一个10元订单，分三次退款，第一次3元，第二次3元，第三次4元，每个售后单都是部分退款，但是第三次退款完成后，isLastApply=1,表示正向订单已经彻底退完
		JdBeansMoney                int    `json:"jdBeansMoney"`                // 售后单实际退用户京豆支付金额（单位为分）
		JdFreightBillInfo           string `json:"jdFreightBillInfo"`           // 京东运费计费，json字符串，需要转换成Map，具体key：freightTotalMoney(Long)：运费总金额，单位（分）
		JdProductBillInfo           string `json:"jdProductBillInfo"`           // 京东货款计费，json字符串，需要转换成Map
		MealBoxMoney                int    `json:"mealBoxMoney"`                // 应退订单餐盒费
		NewOrderID                  string `json:"newOrderId"`                  // 新订单号（ 产生差评之后发起的直赔或者换新业务时候产生的订单号）
		OrderAging                  int    `json:"orderAging"`                  // 订单时效（分钟）
		OrderFreightMoney           int    `json:"orderFreightMoney"`           // 应退运费总金额(用户实际支付的运费金额)
		OrderID                     string `json:"orderId"`                     // 原订单号
		OrderReceivableFreight      int    `json:"orderReceivableFreight"`      // 订单应收运费(未优惠前应付运费)
		OrderSource                 int    `json:"orderSource"`                 // 售后发起方（10:客服，20:用户APP，40:商家，50:用户H5，60:用户RN）
		OrderStatus                 int    `json:"orderStatus"`                 // 原订单状态，指原订单申请售后时的状态
		OrderType                   string `json:"orderType"`                   // 原订单类型 （10000:从门店出的订单）
		PackagingMoney              int    `json:"packagingMoney"`              // 应退包装费金额
		PayType                     int    `json:"payType"`                     // 原订单支付方式（1:货到付款,2:邮局汇款,3:混合支付,4:在线支付,5:公司转账）
		PickupDetail                string `json:"pickupDetail"`                // 取货信息（发错货时填写的取货信息）
		PickupEndTime               int    `json:"pickupEndTime"`               // 预计送达时间结束
		PickupStartTime             int    `json:"pickupStartTime"`             // 预计送达时间开始
		PickwareAddress             string `json:"pickwareAddress"`             // 客户地址
		PlatformIntegralDeductMoney int    `json:"platformIntegralDeductMoney"` // 应退到家积分金额
		QuestionDesc                string `json:"questionDesc"`                // 问题描述（字数限制：400字）
		QuestionPic                 string `json:"questionPic"`                 // 问题图片列表（最多5张）【多个图片之间用英文逗号隔开，解析后，在图片链接前加http://img10.360buyimg.com/o2o/即可访问图片】
		QuestionTypeCid             int    `json:"questionTypeCid"`             // 申请售后原因（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，402:不想要了，502:未在时效内送达）
		RefundUserMmoney            int    `json:"refundUserMmoney"`            // 退款总金额
		SelfPickDiscountMoney       int    `json:"selfPickDiscountMoney"`       // 自提服务费优惠金额
		SelfPickPayMoney            int    `json:"selfPickPayMoney"`            // 自提服务费实付金额
		SelfPickPayableMoney        int    `json:"selfPickPayableMoney"`        // 自提服务费应付金额
		StationID                   string `json:"stationId"`                   // 到家门店编码
		StationName                 string `json:"stationName"`                 // 到家门店名称
		StationNumOutSystem         string `json:"stationNumOutSystem"`         // 商家门店编码
		TongchengFreightMoney       int    `json:"tongchengFreightMoney"`       // 退货单产生的同城送费用
		UpdateTime                  int    `json:"updateTime"`                  // 更新时间
		UserDutyType                int    `json:"userDutyType"`                // 用户责任：1用户责任 0非用户责任
		VirtualMoney                int    `json:"virtualMoney"`                // 售后单总优惠金额（单位为分，售后单维度的总优惠金额=SKU维度总优惠金额相加）
	} `json:"result"`
}

type OrderAfterSaleDetail struct {
	Code   string `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		AfsMoney struct {
			BalanceDeductMoney           int `json:"balanceDeductMoney"`
			CashMoney                    int `json:"cashMoney"`
			DeliveryMoney                int `json:"deliveryMoney"`
			GiftcardTotalMoney           int `json:"giftcardTotalMoney"`
			IntegralDeductMoney          int `json:"integralDeductMoney"`
			MealboxMoney                 int `json:"mealboxMoney"`
			OrderFreightMoney            int `json:"orderFreightMoney"`
			OrderReceiveFreightMoney     int `json:"orderReceiveFreightMoney"`
			PackagingMoney               int `json:"packagingMoney"`
			PlatformIntegralDeductMoney  int `json:"platformIntegralDeductMoney"`
			PlatformIntegralDeductNum    int `json:"platformIntegralDeductNum"`
			PlatformIntegralVirtualMoney int `json:"platformIntegralVirtualMoney"`
			RefundJdpayDiscountMoney     int `json:"refundJdpayDiscountMoney"`
			TotalGoodMoney               int `json:"totalGoodMoney"`
			TotalMoney                   int `json:"totalMoney"`
		} `json:"afsMoney"`
		FreightTips string `json:"freightTips"`
		GiftSkuList []struct {
			PromotionType int    `json:"promotionType"`
			SkuCount      int    `json:"skuCount"`
			SkuID         int    `json:"skuId"`
			SkuName       string `json:"skuName"`
			SkuPrice      int    `json:"skuPrice"`
		} `json:"giftSkuList"`
	} `json:"result"`
}

type ProcessResult struct {
	Code   string `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		ServiceOrder string `json:"serviceOrder"`
	} `json:"result"`
}

type CallBackBase struct {
	BillId    string `json:"billId"`
	StatusId  string `json:"statusId"`
	Timestamp string `json:"timestamp"`
}
type ProductUpdateCall struct {
	//是否有货（可用库存是否大于0）：true 有货，false无货
	Have string `json:"have"`
	//操作人;
	OperPin string `json:"operPin"`
	//操作来源，1:京东LSP, 2:京东医药城, 3:京东厂商直送, 4:京东One, 5:库存中心, 6:订单中心, 7:商品系统, 8:前置仓, 9:门店系统, 10:商家中心, 11:开放平台, 13:捡货系统, 14:批量上下架任务
	OperSource string `json:"operSource"`
	//操作时间，商家需要保存操作时间，当接收到相同的门店商品消息记录时，需按照操作时间先后顺序进行状态更新，也即当收到消息的操作时间小于当前记录的操作时间，不需要做状态更新。
	OperTime string `json:"operTime"`
	//商家编号
	OrgCode string `json:"orgCode"`
	//商品编号
	SkuID string `json:"skuId"`
	//门店编号
	StationNo string `json:"stationNo"`
	//是否缺货(0 不缺货，1 缺货)
	Stockout string `json:"stockout"`
	//门店商品上下架等同于是否可售（0:上架即可售 1:下架即不可售）
	Vendibility string `json:"vendibility"`
}

type AfterSalesDetail struct {
	AfsSeriveOrderList []struct {
		AfsServiceID       int    `json:"afsServiceId"`
		AfsServiceOrder    string `json:"afsServiceOrder"`
		AfsServiceState    int    `json:"afsServiceState"`
		AfsServiceStateStr string `json:"afsServiceStateStr"`
		ApplyDeal          string `json:"applyDeal"`
		ApplyDealStr       string `json:"applyDealStr"`
		ApproveType        int    `json:"approveType"`
		ApproveTypeStr     string `json:"approveTypeStr"`
		ApprovedDate       string `json:"approvedDate"`
		CreateTime         string `json:"createTime"`
		CustomerName       string `json:"customerName"`
		DutyAssume         int    `json:"dutyAssume"`
		DutyAssumeStr      string `json:"dutyAssumeStr"`
		NewOrderID         string `json:"newOrderId"`
		OrderID            string `json:"orderId"`
		OrderSource        int    `json:"orderSource"`
		OrderSourceStr     string `json:"orderSourceStr"`
		StationName        string `json:"stationName"`
		TotalMoney         int    `json:"totalMoney"`
	} `json:"afsSeriveOrderList"`
	Code       int    `json:"code"`
	Msg        string `json:"msg"`
	TotalCount int    `json:"totalCount"`
}
