package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/oc"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"io/ioutil"
	"net/http"
)

func DaDaCallback(c echo.Context) error {
	model := dto.DaDaCallBackMode{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	err := json.Unmarshal(body, &model)
	if err != nil {
		return c.JSON(400, "绑定参数出错")
	}
	glog.Info("DaDaCallback-达达配送回调参数：", model)

	var params oc.DeliveryNodeRequest
	//3达达
	params.DeliveryType = 3
	params.OrderSn = model.OrderID
	params.CourierName = model.DmName
	params.CourierPhone = model.DmMobile
	params.CancelReason = model.CancelReason
	params.CreateTime = kit.GetTimeNow()

	//第三方的配送订单号，因为复用美配的处理逻辑，
	//美配 0 派单中 15骑手已到店 20骑手已接单 30骑手已取货 50已送达 99骑手已取消
	//闪送状态 20：派单中 30：取货中 40：闪送中 50：已完成 60：已取消
	params.MtPeisongId = model.ClientID //配送id的适配
	//将闪送的状态对应到美团的状态中
	switch model.OrderStatus {
	case 1:
		params.Status = 0
	case 2:
		params.Status = 20
	case 3:
		params.Status = 30
	case 100:
		params.Status = 15
	case 4:
		params.Status = 50
	case 5:
		params.Status = 99
	case 9:
		params.Status = 101
	case 10:
		params.Status = 102
	default:
		params.Status = 1999 //
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.DeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("DaDaCallback 调DeliveryNode,达达订单："+model.OrderID, r)
	if err != nil || r.Code != 200 {
		return c.JSON(http.StatusBadRequest, err.Error())
	}

	return c.JSON(200, "ok")
}
