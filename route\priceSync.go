package route

import (
	"external-ui/controller"
	myMiddleware "external-ui/middleware"
	"github.com/labstack/echo/v4"
)

//价格同步
func priceSyncGroup(e *echo.Group) {
	g := e.Group("/product", myMiddleware.PriceSyncAuth())
	g.POST("/price_sync", controller.ProductPriceSync)
	g.POST("/GetProductPrice", controller.GetProductPrice)

	// r1同步价格
	e.POST("/product/r1-price-sync", controller.R1PriceSync, myMiddleware.AwenSignature())
	e.POST("/product/r1-price-sync-sku", controller.R1PriceSyncSku, myMiddleware.AwenSignature())
	// 子龙商品处方药属性同步
	e.POST("/product/zilong-drug-sync", controller.ZiLongDrugSync, myMiddleware.AwenSignature())
}
