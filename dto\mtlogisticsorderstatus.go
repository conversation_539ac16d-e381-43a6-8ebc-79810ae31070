package dto

//美团自己的配送状态回调
type MtLogisticsOrderStatus struct {
	//订单号，数据库中请用bigint(20)存储此字段。
	OrderId int64 `json:"order_id" form:"order_id" query:"order_id"`
	//订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
	WmOrderIdView int64 `json:"wm_order_id_view" form:"wm_order_id_view" query:"wm_order_id_view"`
	//美团配送订单状态code，目前美团配送状态值有：0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。
	LogisticsStatus int32 `json:"logistics_status" form:"logistics_status" query:"logistics_status"`
	//订单配送状态变更为当前状态的时间，推送10位秒级的时间戳。
	Time int32 `json:"time" form:"time" query:"time"`
	//美团配送骑手的姓名，取最新一次指派的骑手信息。
	DispatcherName string `json:"dispatcher_name" form:"dispatcher_name" query:"dispatcher_name"`
	//美团配送骑手的联系电话，取最新一次指派的骑手信息。
	//骑手手机号会以隐私号形式推送，请兼容13812345678和13812345678_123456两种号码格式，最多不超过20位，以便对接隐私号订单。
	DispatcherMobile string `json:"dispatcher_mobile" form:"dispatcher_mobile" query:"dispatcher_mobile"`
}

// 推送美配订单异常配送信息
type MtLogisticsException struct {
	//三方应用id
	AppId string `json:"app_id" form:"app_id" query:"app_id"`
	//三方门店code
	AppPoiCode string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	//订单号
	OrderViewId string `json:"order_view_id" form:"order_view_id" query:"order_view_id"`
	//1）配送异常原因：
	//1.发配送超过15分钟没有骑手抢单
	//2.骑手抢单超时未到店
	//3.骑手到店超时未取货
	//4.预订单预计送达时间前X分钟未取货
	//5.发配送超过30分钟没有骑手抢单
	//2）配送恢复描述
	ExceptionReason string `json:"exception_reason" form:"exception_reason" query:"exception_reason"`
	//配送异常产生时间，恢复时为0
	Ctime string `json:"ctime" form:"ctime" query:"ctime"`
	//配送异常恢复时间，产生时为0
	Utime string `json:"utime" form:"utime" query:"utime"`
}
