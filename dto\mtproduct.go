package dto

type ProductDelCallback []struct {
	AppPoiCode   string         `json:"app_poi_code"`
	Name         string         `json:"name"`
	AppSpuCode   string         `json:"app_spu_code"`
	CategoryList []CategoryList `json:"categoryList"`
	DeleteAppKey string         `json:"deleteAppKey"`
	DeleteTime   int            `json:"deleteTime"`
	OpName       string         `json:"opName"`
	DeleteReason string         `json:"deleteReason"`
}

// CategoryList 代表分类列表中的单个元素
type CategoryList struct {
	CategoryCode string `json:"categoryCode"`
	CategoryName string `json:"categoryName"`
}
