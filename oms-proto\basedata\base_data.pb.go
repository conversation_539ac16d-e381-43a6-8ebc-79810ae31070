// Code generated by protoc-gen-go. DO NOT EDIT.
// source: basedata/base_data.proto

package basedata

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//获取单据类
type CommonRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonRequest) Reset()         { *m = CommonRequest{} }
func (m *CommonRequest) String() string { return proto.CompactTextString(m) }
func (*CommonRequest) ProtoMessage()    {}
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_449723b542c972f2, []int{0}
}

func (m *CommonRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonRequest.Unmarshal(m, b)
}
func (m *CommonRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonRequest.Marshal(b, m, deterministic)
}
func (m *CommonRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonRequest.Merge(m, src)
}
func (m *CommonRequest) XXX_Size() int {
	return xxx_messageInfo_CommonRequest.Size(m)
}
func (m *CommonRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommonRequest proto.InternalMessageInfo

type CommonReply struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 返回信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 通用数据
	Data                 []*CommonData `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommonReply) Reset()         { *m = CommonReply{} }
func (m *CommonReply) String() string { return proto.CompactTextString(m) }
func (*CommonReply) ProtoMessage()    {}
func (*CommonReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_449723b542c972f2, []int{1}
}

func (m *CommonReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonReply.Unmarshal(m, b)
}
func (m *CommonReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonReply.Marshal(b, m, deterministic)
}
func (m *CommonReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonReply.Merge(m, src)
}
func (m *CommonReply) XXX_Size() int {
	return xxx_messageInfo_CommonReply.Size(m)
}
func (m *CommonReply) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonReply.DiscardUnknown(m)
}

var xxx_messageInfo_CommonReply proto.InternalMessageInfo

func (m *CommonReply) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommonReply) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommonReply) GetData() []*CommonData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommonData struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Value                int32    `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	CreateTime           string   `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           string   `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonData) Reset()         { *m = CommonData{} }
func (m *CommonData) String() string { return proto.CompactTextString(m) }
func (*CommonData) ProtoMessage()    {}
func (*CommonData) Descriptor() ([]byte, []int) {
	return fileDescriptor_449723b542c972f2, []int{2}
}

func (m *CommonData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonData.Unmarshal(m, b)
}
func (m *CommonData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonData.Marshal(b, m, deterministic)
}
func (m *CommonData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonData.Merge(m, src)
}
func (m *CommonData) XXX_Size() int {
	return xxx_messageInfo_CommonData.Size(m)
}
func (m *CommonData) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonData.DiscardUnknown(m)
}

var xxx_messageInfo_CommonData proto.InternalMessageInfo

func (m *CommonData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommonData) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *CommonData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *CommonData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CommonData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func init() {
	proto.RegisterType((*CommonRequest)(nil), "basedata.CommonRequest")
	proto.RegisterType((*CommonReply)(nil), "basedata.CommonReply")
	proto.RegisterType((*CommonData)(nil), "basedata.CommonData")
}

func init() { proto.RegisterFile("basedata/base_data.proto", fileDescriptor_449723b542c972f2) }

var fileDescriptor_449723b542c972f2 = []byte{
	// 326 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x52, 0x41, 0x4f, 0xf2, 0x40,
	0x10, 0xfd, 0x5a, 0x28, 0x7c, 0x4c, 0x55, 0x92, 0x0d, 0xe2, 0xc6, 0x8b, 0xa4, 0xa7, 0x5e, 0x2c,
	0x09, 0x1e, 0x8d, 0xd1, 0x00, 0x57, 0xa3, 0x29, 0x9c, 0xbc, 0x90, 0xa1, 0x9d, 0x68, 0x63, 0x97,
	0xad, 0xdb, 0x96, 0xa4, 0xff, 0xc1, 0x5f, 0xe7, 0x2f, 0x32, 0xbb, 0xa5, 0x9a, 0xe8, 0xc9, 0xde,
	0xe6, 0xbd, 0x79, 0xf3, 0xde, 0xec, 0x64, 0x81, 0x6f, 0x31, 0xa7, 0x18, 0x0b, 0x9c, 0xea, 0x62,
	0xa3, 0xab, 0x20, 0x53, 0xb2, 0x90, 0xec, 0x7f, 0xd3, 0xf1, 0x86, 0x70, 0xbc, 0x90, 0x42, 0xc8,
	0x5d, 0x48, 0x6f, 0x25, 0xe5, 0x85, 0x47, 0xe0, 0x36, 0x44, 0x96, 0x56, 0x8c, 0x41, 0x37, 0x92,
	0x31, 0x71, 0x6b, 0x62, 0xf9, 0x4e, 0x68, 0x6a, 0xc6, 0xa1, 0x2f, 0x28, 0xcf, 0xf1, 0x99, 0xb8,
	0x3d, 0xb1, 0xfc, 0x41, 0xd8, 0x40, 0xe6, 0x43, 0x57, 0xbb, 0xf2, 0xce, 0xa4, 0xe3, 0xbb, 0xb3,
	0x51, 0xd0, 0xc4, 0x04, 0xb5, 0xe5, 0x12, 0x0b, 0x0c, 0x8d, 0xc2, 0x7b, 0xb7, 0x00, 0xbe, 0x49,
	0x76, 0x02, 0x76, 0x12, 0x1f, 0x42, 0xec, 0x24, 0x66, 0x23, 0x70, 0xf6, 0x98, 0x96, 0x75, 0x80,
	0x13, 0xd6, 0x80, 0x8d, 0xa1, 0xa7, 0x48, 0xa0, 0x7a, 0xe5, 0x1d, 0x93, 0x7b, 0x40, 0xec, 0x02,
	0xdc, 0x48, 0x11, 0x16, 0xb4, 0x29, 0x12, 0x41, 0xbc, 0x6b, 0x9a, 0x50, 0x53, 0xeb, 0x44, 0x90,
	0x16, 0x94, 0x59, 0xfc, 0x25, 0x70, 0x6a, 0x41, 0x4d, 0x69, 0xc1, 0xec, 0xc3, 0x86, 0xe1, 0x1c,
	0x73, 0xd2, 0xcb, 0xac, 0x48, 0xed, 0x93, 0x88, 0xd8, 0x0d, 0x0c, 0x1e, 0x54, 0x4c, 0x6a, 0x5d,
	0x65, 0xc4, 0xce, 0x7e, 0xbe, 0xe5, 0x70, 0xaf, 0xf3, 0xd3, 0xdf, 0x8d, 0x2c, 0xad, 0xbc, 0x7f,
	0xec, 0x16, 0x5c, 0x33, 0xbe, 0x92, 0xa5, 0x8a, 0xda, 0x18, 0x5c, 0x43, 0xff, 0x11, 0xab, 0x7b,
	0x7d, 0xf1, 0xbf, 0x0f, 0xdf, 0xc1, 0x91, 0x49, 0x5f, 0xbc, 0xe0, 0x6e, 0x47, 0x69, 0x3b, 0x87,
	0x25, 0xa5, 0xc9, 0x9e, 0x54, 0xcb, 0x1d, 0xe6, 0xe3, 0xa7, 0x51, 0x30, 0x95, 0x22, 0xbf, 0x34,
	0x9f, 0x6e, 0xda, 0xa8, 0xb6, 0x3d, 0x83, 0xaf, 0x3e, 0x03, 0x00, 0x00, 0xff, 0xff, 0x50, 0x1d,
	0x56, 0xf5, 0xa0, 0x02, 0x00, 0x00,
}
