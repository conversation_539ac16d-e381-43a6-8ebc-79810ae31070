package services

import (
	"context"
	"errors"
	"external-ui/dto"
	"external-ui/proto/oc"
	kit "github.com/tricobbler/rp-kit"
	"strconv"
)

type CallBackHandleService struct {
}

///APP方URL 推送用户或客服取消订单（必接）
func (gy *CallBackHandleService) OrderCancelHandle(orderInfo dto.SendOrderCancel) error {
	var param oc.CancelOrderRequest
	orderNo := strconv.FormatInt(orderInfo.Order_id, 10)
	param.OrderSn = orderNo
	if len(orderInfo.Reason) > 0 {
		param.CancelReason = orderInfo.Reason
	} else {
		param.CancelReason = gy.GetReasonByCode(orderInfo.Reason_code)
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.CancelOrder(kit.SetTimeoutCtx(context.Background()), &param)
	if err != nil {
		return err
	}

	if r.Code != 200 {
		return errors.New(r.Error + r.Message)
	}
	return nil
}

/// 美团发送的取消原因列表
func (gy *CallBackHandleService) GetReasonByCode(code int) string {
	switch code {
	case 1001:
		return "系统取消，超时未确认"
	case 1002:
		return "系统取消，在线支付订单15分钟未支付"
	case 1101:
		return "用户取消，在线支付中取消"
	case 1102:
		return "用户取消，商家确认前取消"
	case 1103:
		return "用户取消，用户退款取消"
	case 1201:
		return "客服取消，用户下错单"
	case 1202:
		return "客服取消，用户测试"
	case 1203:
		return "客服取消，重复订单"
	case 1204:
		return "客服取消，其他原因"
	default:
		return "其他原因"
	}

}

/// 推送催单消息（必接）
func (gy *CallBackHandleService) ReminderHandle(reminder dto.ReminderRequest) error {
	var param oc.ReminderOrderRequest
	param.RemindTime = reminder.Remind_time
	orderNo := strconv.FormatInt(reminder.Order_id, 10)
	param.OrderSn = orderNo

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.ReminderOrder(kit.SetTimeoutCtx(context.Background()), &param)
	if err != nil {
		return err
	}
	if r.Code != 200 {
		return errors.New(r.Error + r.Message)
	}
	return nil
}
