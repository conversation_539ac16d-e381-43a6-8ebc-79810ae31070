package controller

import (
	"encoding/json"
	"external-ui/dto"
	"external-ui/utils"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

func MiniProgramGetCallback(c echo.Context) error {
	bodyRead := c.QueryParams()
	glog.Info("WxVideoProductCheck 商品回调接收请求参数: ", bodyRead)
	echostr := c.QueryParam("echostr")
	return c.String(200, echostr)
}

// MiniProgramCallback 小程序回调
func MiniProgramCallback(c echo.Context) error {
	params := new(dto.MiniProgramCallbackRequest)
	if err := c.Bind(params); err != nil {
		glog.Error("MiniProgramCallback 接收请求失败，err: ", err)
		return c.JSON(200, "success")
	}
	paramsJson, _ := json.Marshal(params)
	bodyRead := c.QueryParams()
	glog.Info("MiniProgramCallback 商品回调接收请求参数: ", bodyRead, "; body: ", string(paramsJson))

	// 解密成明文
	dataByte := utils.WxAesDecrypt(params.Encrypt)
	if len(dataByte) == 0 {
		glog.Error("MiniProgramCallback 解密失败")
		return c.JSON(200, "success")
	}

	// 将结果返回给BBC
	MiniProgramCallbackBBC(string(dataByte))
	return c.String(200, "success")
}
