package services

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"strings"

	"external-ui/dto"
	"external-ui/proto/et"
	"external-ui/proto/oc"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 创建售后单
func CreateAfterSalesOrder(afterSales string, storeMasterId int32) (strResult string) {
	dtoData, _ := GetJddjAfsServiceByBillId(afterSales, storeMasterId)

	//glog.Info("京东查询售后单返回", afterSales, dtoData)
	//退款售后单状态变化：待审核（10）——退款处理中（30）——退款成功（32）
	//退货售后单状态变化：待审核（10）——待退货（110）—— 取货失败即退货失败（113）/取货成功（111）——1111 退货成功-商品已送至门店——1112商家已确认收货—待退款（112）——退货退款成功（114）
	if dtoData.Success && dtoData.Result.AfsServiceState == 10 {
		var refundType int64
		// 10:仅退款 ,30:直赔，40:退货退款），仅退款/退货退款由用户发起，直赔由商家发起
		if dtoData.Result.ApplyDeal == "10" || dtoData.Result.ApplyDeal == "30" {
			refundType = 1 // 1为退款
		} else if dtoData.Result.ApplyDeal == "40" {
			refundType = 2 // 2为退货
		}

		etClient := et.GetExternalClient()
		defer etClient.Close()

		var isFullRefund int32 = 0
		// 根据订单号查询订单明细

		outOrder, err := etClient.JddjOrder.GetJddjOrderDetail(etClient.Ctx,
			&et.JddjOrderDetailRequest{OrderId: dtoData.Result.OrderID, StoreMasterId: storeMasterId})
		if outOrder.Code == 200 && len(outOrder.JddjData.JddjResult.JddjResultList) > 0 {
			order := outOrder.JddjData.JddjResult.JddjResultList[0]
			// 根据订单号查询售后单信息

			outDetail, _ := etClient.JddjOrder.JddjGetAfsSeriveOrderList(etClient.Ctx,
				&et.JddjOrderDetailRequest{OrderId: dtoData.Result.OrderID, StoreMasterId: storeMasterId})
			if outDetail.Code == 200 && len(outDetail.Data) > 0 {
				detail := dto.AfterSalesDetail{}
				json.Unmarshal([]byte(outDetail.Data), &detail)
				for _, d := range detail.AfsSeriveOrderList {
					// 售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,50:客户取消,60:商家收货审核不通过,70:已解决, 91:直赔,92:直赔成功,
					// 93:直赔失败,90:待赔付, 110:待退货,111:取货成功,1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
					if d.AfsServiceOrder != afterSales && // 不等于当前处理的售后单号
						d.AfsServiceState != 33 && d.AfsServiceState != 40 && d.AfsServiceState != 50 &&
						d.AfsServiceState != 60 && d.AfsServiceState != 113 {
						order.OrderBuyerPayableMoney -= int32(d.TotalMoney)
					}
				}
			}
			glog.Info("CreateAfterSalesOrder,", "dtoData:", kit.JsonEncode(dtoData), "order:", kit.JsonEncode(order))
			if int32(dtoData.Result.CashMoney) >= order.OrderBuyerPayableMoney {
				isFullRefund = 1 // 1整单退款
			} else {
				isFullRefund = 2 // 2部分退款
			}
		} else {
			return "根据订单号查询订单明细异常"
		}
		var sponsor string
		// 售后发起方（10:客服，20:用户APP，40:商家，50:用户H5，60:用户RN）

		if dtoData.Result.OrderSource == 10 {
			sponsor = "3" // 3-客服
			ocClient := oc.GetOrderServiceClient()
			out, e := ocClient.RPC.CancelOrder(kit.SetTimeoutCtx(context.Background()), &oc.CancelOrderRequest{
				OrderSn:      dtoData.Result.OrderID,
				CancelReason: "客服取消",
				IsRefund:     1,
				OldRefundSn:  dtoData.Result.AfsServiceOrder,
			})
			if e != nil {
				glog.Info("京东到家客户取消订单错误：", e, kit.JsonEncode(dtoData))
				strResult = e.Error()
			} else {
				strResult = out.Message
				glog.Info("京东到家客户取消订单结果：", kit.JsonEncode(out), "  售后单详情：", kit.JsonEncode(dtoData))
			}
			return strResult
		} else if dtoData.Result.OrderSource == 20 || dtoData.Result.OrderSource == 50 || dtoData.Result.OrderSource == 60 {
			sponsor = "1" // 1-用户
		} else if dtoData.Result.OrderSource == 40 {
			sponsor = "2" // 2-商家
		}
		var activityPtAmount int32
		goodsData := []*oc.RefundOrderGoodsData{}
		for _, d := range dtoData.Result.AfsDetailList {
			cashMoney, _ := strconv.ParseFloat(strconv.Itoa(d.CashMoney/d.SkuCount), 32)

			for _, ditem := range d.AfsSkuDiscountList {
				activityPtAmount += int32(ditem.PlatPayMoney)
			}

			dto := oc.RefundOrderGoodsData{
				SkuId:              d.SkuIDIsv,
				Quantity:           int32(d.SkuCount),
				RefundAmount:       cast.ToString(kit.FenToYuan(int64(d.CashMoney))),
				GoodsName:          d.WareName,
				PromotionType:      int32(d.PromotionType),
				Spec:               d.SkuSpecification,
				RefundPrice:        float32(kit.FenToYuan(int64(d.PayPrice))),
				RefundRealityPrice: float32(kit.FenToYuan(int64(cashMoney))), //实际支付单价
				AppFoodCode:        d.UpcCode,
			}
			goodsData = append(goodsData, &dto)
		}
		reqData := oc.RefundOrderApplyRequest{
			OrderId:              dtoData.Result.OrderID,                                                               // 订单号
			ExternalOrderId:      dtoData.Result.OrderID,                                                               // 外部订单号 Id
			Reason:               GetRefundReason(dtoData.Result.QuestionTypeCid),                                      // 原因
			RefundType:           refundType,                                                                           // 退款类型1为退款,2为退货
			RefundOrderGoodsData: goodsData,                                                                            // 部分退款商品sku数据集合的json格式数组
			ShopId:               dtoData.Result.StationNumOutSystem,                                                   // 门店id(财务编码)
			FullRefund:           isFullRefund,                                                                         // 1整单退款 2部分退款
			RefundRemark:         dtoData.Result.QuestionDesc,                                                          // 售后单备注
			RefundAmount:         float32(kit.FenToYuan(int64(dtoData.Result.CashMoney))),                              // 退款总金额
			OperationUser:        dtoData.Result.ApprovePin,                                                            // 操作用户
			ResType:              GetApproveType(dtoData.Result.AfsServiceState, dtoData.Result.ApproveType),           // 申退款状态类型
			ApplyOpUserType:      sponsor,                                                                              // 售后发起方
			OrderFrom:            4,                                                                                    // 渠道id(1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店)
			OperationType:        GetAfsServiceApproveType(dtoData.Result.ApproveType, dtoData.Result.AfsServiceState), // 操作描述
			//RefundOrderSn:        dtoData.Result.AfsServiceOrder,                                                       // 售后单号
			OldRefundSn:      dtoData.Result.AfsServiceOrder,                  // 售后单号
			ChannelId:        4,                                               // 来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
			ActivityPtAmount: float32(kit.FenToYuan(int64(activityPtAmount))), //平台承担的优惠金额
		}
		var imgUrl string
		images := strings.Split(dtoData.Result.QuestionPic, ",")
		for i, img := range images {
			if i > 0 {
				imgUrl += ","
			}
			imgUrl += "http://img10.360buyimg.com/o2o/" + img
		}
		reqData.Pictures = imgUrl

		ocClient := oc.GetOrderServiceClient()
		out, err := ocClient.ROC.RefundOrderApply(kit.SetTimeoutCtx(context.Background()), &reqData)
		if err != nil {
			glog.Error("京东到家订单中心生成售后单异常：", err.Error(), " 入参：", kit.JsonEncode(reqData))
			strResult = err.Error()
		} else {
			strResult = out.Message
		}
		glog.Info("京东到家订单中心生成售后单结果：", kit.JsonEncode(out), "  售后单详情：", kit.JsonEncode(dtoData), " 入参：", kit.JsonEncode(reqData))
	} else {
		glog.Info("创建售后单申请状态错误 JddjNewApplyAfterSaleBill：", dtoData.Result.AfsServiceState, " 售后单查询结果：", dtoData)
		strResult = dtoData.Msg
	}
	return strResult
}

// 根据售后编号查询售后单
//退款售后单状态变化：待审核（10）——退款处理中（30）——退款成功（32）
//退货售后单状态变化：待审核（10）——待退货（110）—— 取货失败即退货失败（113）/取货成功（111）——1111 退货成功-商品已送至门店——1112商家已确认收货—待退款（112）——退货退款成功（114）
func GetJddjAfsServiceByBillId(billId string, storeMasterId int32) (dto.AfsService, error) {
	dtoData := dto.AfsService{}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	// 请求查询售后单详情接口
	//todo tp jd
	out, err := etClient.JddjOrder.GetJddjAfsService(etClient.Ctx,
		&et.JddjAfsServiceRequest{AfsServiceOrder: billId, StoreMasterId: storeMasterId})
	if err != nil {
		glog.Error("京东到家 查询售后单详情接口 请求订单中心接口失败，售后单号：", billId, err.Error())
		return dtoData, err
	}
	if out.Message != "SUCCESS" {
		return dtoData, errors.New(out.Message)
	}
	json.Unmarshal([]byte(out.Data), &dtoData)
	return dtoData, nil
}

// 获取售后单明细
func GetOrderAfterSaleDetail(req *et.JddjOrderCalcMoneyRequest) (dto.OrderAfterSaleDetail, error) {
	dtoData := dto.OrderAfterSaleDetail{}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	// 请求查询售后单详情接口

	out, err := etClient.JddjOrder.GetJddjOrderCalcMoney(etClient.Ctx, req)
	if err != nil {
		glog.Error("京东到家 查询订单可售后商品金额接口 请求订单中心接口失败，请求入参：", req, err.Error())
		dtoData.Msg = err.Error()
		return dtoData, err
	}
	if out.Message != "SUCCESS" {
		return dtoData, errors.New(out.Message)
	}
	json.Unmarshal([]byte(out.Data), &dtoData)
	dtoData.Code = "200"
	return dtoData, nil
}

// 根据京东到家的消息单据编号进行售后处理
func AfterSalesResponse(billId, statusId string, storeMasterId int32) (*oc.BaseResponse, error) {
	dtoData, _ := GetJddjAfsServiceByBillId(billId, storeMasterId)
	resp := oc.BaseResponse{}
	if dtoData.Success {
		glog.Info("当前售后单的状态为：", dtoData.Result.AfsServiceState, GetAfsServiceState(dtoData.Result.AfsServiceState), " 入参状态：", statusId)
		// 审核结果状态 20031 商家同意取消申请,20032 商家驳回取消申请
		var isPassed int32 = 0
		if statusId == "20031" {
			isPassed = 1
		} else if statusId == "20032" {
			isPassed = 2
		}
		var activityPtAmount int32
		for _, d := range dtoData.Result.AfsDetailList {
			for _, ditem := range d.AfsSkuDiscountList {
				activityPtAmount += int32(ditem.PlatPayMoney)
			}
		}
		repData := oc.RefundOrderAnswerRequest{
			OrderId:          dtoData.Result.OrderID,                                                     // 订单号
			ExternalOrderId:  dtoData.Result.OrderID,                                                     // 外部订单号 Id
			RefundOrderSn:    dtoData.Result.AfsServiceOrder,                                             // 售后单号
			Reason:           GetRefundReason(dtoData.Result.QuestionTypeCid),                            // 原因
			ResultType:       isPassed,                                                                   // 申退款状态类型，参考值：1-同意；2-拒绝
			ResultTypeNote:   GetApproveType(dtoData.Result.AfsServiceState, dtoData.Result.ApproveType), // 申退款状态类型 备注 1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认
			OperationUser:    dtoData.Result.ApprovePin,                                                  // 操作用户
			OperationType:    "商家审核用户提交的取消订单申请",                                                          // 操作类型
			ActivityPtAmount: kit.FenToYuan(activityPtAmount),
		}

		ocClient := oc.GetOrderServiceClient()
		if out, e := ocClient.ROC.RefundOrderAnswer(kit.SetTimeoutCtx(context.Background()), &repData); e != nil {
			glog.Info("京东到家调用订单中心更改订单状态错误：", e, "，参数：", kit.JsonEncode(repData))
			resp.Code = http.StatusBadRequest
			resp.Message = e.Error()
		} else if out != nil {
			resp.Code = out.Code
			resp.Message = out.Message
		} else {
			glog.Info("京东到家调用订单中心更改订单状态错误：", "，参数：", kit.JsonEncode(repData))
			resp.Code = http.StatusBadRequest
			resp.Message = "京东到家调用订单中心更改订单状态错误"
		}
	} else {
		resp.Code = http.StatusBadRequest
		resp.Message = "获取京东到家接口数据错误：" + billId
	}
	return &resp, nil
}

func GetAfsServiceApproveType(approveType, afsServiceState int) string {
	var stringType string
	switch approveType {
	case 1:
		stringType = "到家客服" + "审核" + GetAfsServiceState(afsServiceState)
	case 2:
		// stringType = "商家" + "审核" + GetAfsServiceState(afsServiceState)
		stringType = "售后单发起"
	case 3:
		stringType = "商家超时自动审核" + GetAfsServiceState(afsServiceState)
	case 4:
		stringType = "自动取消"
	case 5:
		stringType = "平台自动审核" + GetAfsServiceState(afsServiceState)
	case 6:
		stringType = "客服超时自动审核" + GetAfsServiceState(afsServiceState)
	}
	return stringType
}

// 售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,50:客户取消,60:商家收货审核不通过,70:已解决, 91:直赔,92:直赔成功,93:直赔失败,90:待赔付, 110:待退货,111:取货成功,1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
func GetAfsServiceState(afsServiceState int) string {
	var stringType string
	switch afsServiceState {
	case 10:
		stringType = "待审核"
	case 20:
		stringType = "待取件"
	case 30:
		stringType = "退款处理中"
	case 31:
		stringType = "待商家收货审核"
	case 32:
		stringType = "退款成功"
	case 33:
		stringType = "退款失败"
	case 40:
		stringType = "审核不通过-驳回"
	case 50:
		stringType = "客户取消"
	case 60:
		stringType = "商家收货审核不通过"
	case 70:
		stringType = "已解决"
	case 91:
		stringType = "直赔"
	case 92:
		stringType = "直赔成功"
	case 93:
		stringType = "直赔失败"
	case 90:
		stringType = "待赔付"
	case 110:
		stringType = "待退货"
	case 111:
		stringType = "取货成功"
	case 1101:
		stringType = "取货中"
	case 1111:
		stringType = "退货成功-商品已送至门店"
	case 1112:
		stringType = "退货成功-商家已确认收货"
	case 112:
		stringType = "退货成功-待退款"
	case 113:
		stringType = "退货失败"
	case 114:
		stringType = "退货成功"
	}
	return stringType
}

// 根据 售后单查询接口的 QuestionTypeCid 字段获取退款原因
// 申请售后原因（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，402:不想要了，502:未在时效内送达）
func GetRefundReason(questionTypeCid int) string {
	var refundReason string
	switch questionTypeCid {
	case 201:
		refundReason = "商品质量问题"
	case 202:
		refundReason = "送错货"
	case 203:
		refundReason = "缺件少件"
	case 501:
		refundReason = "全部商品未收到"
	case 208:
		refundReason = "包装脏污有破损"
	case 207:
		refundReason = "缺斤少两"
	case 210:
		refundReason = "商家通知我缺货"
	case 303:
		refundReason = "实物与原图不符"
	case 402:
		refundReason = "不想要了"
	case 502:
		refundReason = "未在时效内送达"
	}
	return refundReason
}

// 获取批准类型
func GetApproveType(afsServiceState, approveType int) string {
	// 售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,50:客户取消,60:商家收货审核不通过,70:已解决, 91:直赔,92:直赔成功,
	// 93:直赔失败,90:待赔付,110:待退货,111:取货成功,1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
	if afsServiceState == 10 {
		return "0"
	} else if afsServiceState == 40 {
		if approveType == 1 {
			return "3"
		}
		return "1"
	} else if afsServiceState == 30 {
		if approveType == 1 {
			return "4"
		} else if approveType == 4 || approveType == 5 || approveType == 6 {
			return "5"
		}
		return "2"
	} else if afsServiceState == 50 {
		return "7"
	} else {
		return ""
	}
}
