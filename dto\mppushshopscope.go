package dto

type MpPushShopScope struct {

	//配送活动标识
	DeliveryServiceCode int32 `json:"delivery_service_code" form:"delivery_service_code" query:"delivery_service_code"`
	//美团配送内部订单id，最长不超过32个字符
	ShopId string `json:"shop_id" form:"shop_id" query:"shop_id"`
	//门店配送范围
	//例：[{"x":31.305655,"y":96.954307},
	//{"x":31.237576,"y":97.025718},
	//{"x":31.327946,"y":97.158928},
	//{"x":31.35375,"y":97.006492}]
	Scope string`json:"scope" form:"scope" query:"scope"`

	//开放平台分配的appkey，合作方唯一标识。
	AppKey string `json:"appkey" form:"appkey" query:"appkey"`
	//	时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
	Timestamp int64 `json:"timestamp" form:"timestamp" query:"timestamp"`
	//数据签名
	Sign string `json:"sign" form:"sign" query:"sign"`


}
