package utils

import (
	"net/url"
	"strings"
)

func ParseQuery(query string) (queryMap url.Values, err error) {
	queryMap = make(url.Values)
	querySlice := strings.Split(query, "&")
	for _, keyVal := range querySlice {
		if keyVal == "" {
			continue
		}
		kvSlice := strings.Split(keyVal, "=")
		if len(kvSlice) < 2 {
			continue
		}
		key, err := url.QueryUnescape(kvSlice[0])
		if err != nil {
			continue
		}
		val, err := url.QueryUnescape(kvSlice[1])
		if err != nil {
			continue
		}
		queryMap[key] = append(queryMap[kvSlice[1]], val)
	}
	return queryMap, err
}
