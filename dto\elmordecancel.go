package dto

type ELMOrderCancel struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	RefundOrderId interface{} `json:"refund_order_id" form:"refund_order_id" query:"refund_order_id"`
	//消息类型。10:发起申请,20:客服介入,
	//30:客服拒绝,40:客服同意,
	//50:商户拒绝,60:商户同意,70:申请失效
	Type string `json:"type" form:"type" query:"type"`
	//申请取消原因
	CancelReason string `json:"cancel_reason" form:"cancel_reason" query:"cancel_reason"`
	//申请取消附加原因
	AdditionReason string `json:"addition_reason" form:"addition_reason" query:"addition_reason"`
	//拒绝原因
	RefuseReason string `json:"refuse_reason" form:"refuse_reason" query:"refuse_reason"`
	//区分订单完成前用户全单取消或订单完成后全单退款流程。
	//1表示订单完成前用户全单取消申请流程，
	//2表示订单完成后用户全单退款申请流程
	CancelType string `json:"cancel_type" form:"cancel_type" query:"cancel_type"`
	//用户上传申请退款图片信息，最多3张；
	Pictures []string `json:"pictures" form:"pictures" query:"pictures"`
}
