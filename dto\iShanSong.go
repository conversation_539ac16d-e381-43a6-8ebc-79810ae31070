package dto

// IssOrderStatus
//闪送订单状态信息推送参数
type IssOrderStatus struct {
	OrderNo          string                `json:"orderNo"`          //我方订单号
	IssOrderNo       string                `json:"issOrderNo"`       //闪送订单
	Status           int32                 `json:"status"`           //配送状态 20：派单中 30：取货中 40：闪送中 50：已完成 60：已取消
	StatusDesc       string                `json:"statusDesc"`       //配送状态说明
	PickupPassword   string                `json:"pickupPassword"`   //
	DeliveryPassword string                `json:"deliveryPassword"` //
	AbortReason      string                `json:"abortReason"`      //取消原因
	AbortType        int32                 `json:"abortType"`        //取消类型 1：因客户取消 商家取消也会返回1 ；3：因闪送员取消 10：闪送系统自动取消
	DeductAmount     int32                 `json:"deductAmount"`     //取消订单情况下的扣款金额
	Courier          IssOrderStatusCourier `json:"courier"`          //送货员小哥哥的信息
}

// IssOrderStatusCourier
//闪送订单状态信息-送货员小哥哥的信息
type IssOrderStatusCourier struct {
	Latitude                string `json:"latitude"`
	Longitude               string `json:"longitude"`
	Name                    string `json:"name"`
	ServiceTimes            int32  `json:"serviceTimes"`
	Mobile                  string `json:"mobile"`
	EstimateDeliveryTimeTip string `json:"estimateDeliveryTimeTip"`
}

// IssOrderStatusResponse 闪送订单状态信息推送相应参数
type IssOrderStatusResponse struct {
	Status int32       `json:"status"` //状态码
	Msg    string      `json:"msg"`    //结果信息
	Data   interface{} `json:"data"`   //数据
}
