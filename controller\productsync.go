package controller

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/et"
	"external-ui/proto/pc"
	"external-ui/utils"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"strconv"
	"strings"
	"time"
)

// @Summary 子龙价格同步
// @Tags v4.2.1
// @Accept json
// @Produce json
// @Param product body []dto.SyncProductPrice true " "
// @Success 200 {object} dto.SyncProductPriceResponse
// @Failure 400 {object} dto.SyncProductPriceResponse
// @Router /external/product/price_sync [POST]
func ProductPriceSync(c echo.Context) error {
	model := new([]dto.SyncProductPrice)
	out := new(dto.SyncProductPriceResponse)
	out.Code = 200
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	requsestJson, _ := json.Marshal(model)
	logger.Info("价格同步开始,接收参数:", string(requsestJson))
	res, err := AddProductPrice(string(requsestJson))
	if err != nil {
		logger.Error("价格同步失败,接收参数:", requsestJson, err.Error())
		out.Message = "请求AddProductPrice失败"
		out.Error = err.Error()
		out.Code = 400
		return c.JSON(400, out)
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	out.Code = res.Code
	return c.JSON(200, out)
}

// @Summary 根据财务编码和产品id查询北京接口的价格数据
// @Tags v4.2.1
// @Accept json
// @Produce json
// @Param product body dto.ProductPrice true " "
// @Success 200 {object} dto.GetProductPriceInfo
// @Failure 400 {object} dto.GetProductPriceInfo
// @Router /external/product/GetProductPrice [POST]
func GetProductPrice(c echo.Context) error {
	model := new(dto.ProductPrice)
	out := new(dto.GetProductPriceInfo)
	out.Code = 200
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	RequestJson, _ := json.Marshal(model)
	if string(RequestJson) == "null" {
		out.Code = 400
		out.Msg = "参数不能为空"
		return c.JSON(200, out)
	}
	apiStr := utils.RandStringRunes(16)
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s",
		config.GetString("Product_price_sync_app_secret"),
		apiStr,
		config.GetString("Product_price_sync_app_id"),
		Timestamp,
		config.GetString("Product_price_sync_app_secret"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	model.ApiId = config.GetString("Product_price_sync_app_id")
	model.ApiSecret = config.GetString("Product_price_sync_app_secret")
	model.ApiStr = apiStr
	model.Timestamp = Timestamp
	model.Sign = md5sign
	RequestJson, _ = json.Marshal(model)
	url := config.GetString("Product_price_sync_url")
	//http://dmd.rprprprp.com/out/get-product-price  //开发环境的url
	result, err := utils.BJHttpPost(url, RequestJson, "")
	if err != nil {
		logger.Error("请求北京接口失败,err:", err.Error())
		return c.JSON(400, "请求北京接口失败,err:"+err.Error())
	}

	logger.Info("价格同步请求北京接口返回结果：", string(result))
	var ResultModel dto.GetProductPriceInfo
	err = json.Unmarshal(result, &ResultModel)
	if err != nil {
		logger.Error("请求北京接口反序列化失败,err:", err.Error())
		return c.JSON(400, "反序列化err:"+err.Error())
	}
	//写入深圳中心本地数据库
	if ResultModel.Status == 200 && len(ResultModel.Data) > 0 {

		list := ResultModel.Data[0].ProductInfo
		l := 200
		for {
			if len(list) < l {
				l = len(list)
			}
			_list := list[:l]
			var olist_list []dto.Data
			var olist dto.Data
			for _, s := range _list {
				olist.ProductInfo = append(olist.ProductInfo, s)
			}
			olist.StructCode = ResultModel.Data[0].StructCode
			olist_list = append(olist_list, olist)
			jsons, _ := json.Marshal(olist_list)
			_, err := AddProductPrice(string(jsons))
			if err != nil {
				logger.Error("价格同步失败接收参数:", string(jsons), err.Error())
			}
			list = list[l:]
			if len(list) <= 0 {
				break
			}
		}
	} //请求北京接口数据
	return c.JSON(200, ResultModel.Data)
}

func AddProductPrice(request string) (*pc.BaseResponse, error) {
	var params pc.AddProductRequest
	params.Response = request
	client := pc.GetDcProductClient()
	defer client.Close()
	res, err := client.RPC.AddProductPrice(client.Ctx, &params)
	if err != nil {
		logger.Error("价格同步失败,接收参数:", request, err.Error())
	}
	return res, err
}

// R1PriceSync @Summary R1采购价格同步
// @Tags 商品
// @Accept json
// @Produce json
// @Param model body pc.R1PriceSyncReq true " "
// @Success 200 {object} pc.ProductBaseResponse
// @Failure 400 {object} pc.ProductBaseResponse
// @Router /external/product/r1-price-sync [POST]
func R1PriceSync(c echo.Context) error {
	req := new(pc.R1PriceSyncReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	}

	client := pc.GetDcProductClient()
	defer client.Close()
	if out, err := client.RPC.R1PriceSync(client.Ctx, req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// R1PriceSyncSku @Summary R1采购价格同步至Sku
// @Tags 商品
// @Accept json
// @Produce json
// @Param model body pc.R1PriceSyncSkuReq true " "
// @Success 200 {object} pc.ProductBaseResponse
// @Failure 400 {object} pc.ProductBaseResponse
// @Router /external/product/r1-price-sync-sku [POST]
func R1PriceSyncSku(c echo.Context) error {
	req := new(pc.R1PriceSyncSkuReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	}

	client := pc.GetDcProductClient()
	defer client.Close()

	if out, err := client.RPC.R1PriceSyncSku(client.Ctx, req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// ZiLongDrugSync @Summary 子龙商品处方药属性同步
// @Tags 商品
// @Accept json
// @Produce json
// @Param model body pc.ZiLongDrugSyncReq true " "
// @Success 200 {object} pc.ProductBaseResponse
// @Failure 400 {object} pc.ProductBaseResponse
// @Router /external/product/zilong-drug-sync [POST]
func ZiLongDrugSync(c echo.Context) error {
	req := new(pc.ZiLongDrugSyncReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	}

	client := pc.GetDcProductClient()
	defer client.Close()

	if out, err := client.RPC.ZiLongDrugSync(client.Ctx, req); err != nil {
		return c.JSON(400, &pc.ProductBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// DealChannelProduct
// @Summary 处理渠道商品数据
// @Tags v2.1.1
// @Accept json
// @Produce json
// @Success 200 {object} dto.SyncProductPriceResponse
// @Failure 400 {object} dto.SyncProductPriceResponse
// @Router /external/tool/deal_channel_product [GET]
func DealChannelProduct(c echo.Context) error {
	out := new(dto.SyncProductPriceResponse)
	out.Code = 200
	client := et.GetExternalClient()
	defer client.Close()
	if _, err := client.Tool.DealErrorProductFromChannel(client.Ctx, &et.DealErrorProductFromChannelReq{}); err != nil {
		logger.Error("处理渠道商品数据失败:", err.Error())
	}
	return c.JSON(200, out)
}
