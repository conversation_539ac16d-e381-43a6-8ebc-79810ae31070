package dto

type ProductPrice struct {
	//产品编码
	ProductCode []string `json:"product_code"`
	//财务编码
	StructCode []string `json:"struct_code"`
	ApiId      string   `json:"apiId"`
	ApiSecret  string   `json:"apiSecret"`
	ApiStr     string   `json:"apiStr"`
	Timestamp  string   `json:"timestamp"`
	Sign       string   `json:"sign"`
}

type GetProductPriceInfo struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Data   []Data `json:"data"`
	Status int    `json:"status"`
}

type Data struct {
	StructCode  []string         `json:"struct_code"`
	ProductInfo []NewProductInfo `json:"product_info"`
}

type NewProductInfo struct {
	ProductCode string `json:"product_code"`
	SellPrice   string `json:"sell_price"`
}
