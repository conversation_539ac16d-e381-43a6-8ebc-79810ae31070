package controller

import (
	"testing"
)

/*func TestOrderRePush(t *testing.T) {
	OrderRePush("27010244133131775")
}*/

func TestOrderRePush2(t *testing.T) {
	OrderRefundPortionCallback(nil)
}

/*func TestPaidOrdersMy(t *testing.T) {
	type args struct {
		dataStr string
	}
	str := `{"app_id":"4889","incmp_code":0,"status":4,"wm_order_id_view":85107310849015528,"app_poi_code":"8510731","wm_poi_name":"%E7%91%9E%E9%B9%8F%E5%AE%A0%E7%89%A9%E5%8C%BB%E9%99%A2-%E5%AE%A0%E7%89%A9%E7%94%A8%E5%93%81%28%E6%96%B0%E5%85%89%E5%BA%97%29","recipient_address":"%E6%98%A5%E6%BD%AE%E8%8A%B1%E5%9B%AD%E4%BA%8C%E5%8C%BA%E5%8C%97%E5%8C%BA%E4%BA%8C%E5%8C%BA185-186%E5%8F%B7+%28%E6%98%A5%E6%BD%AE%E4%BA%8C%E5%8C%BA185%E5%8F%B7602%29%40%23%E6%B1%9F%E8%8B%8F%E7%9C%81%E6%97%A0%E9%94%A1%E5%B8%82%E6%96%B0%E5%90%B4%E5%8C%BA%E6%97%BA%E5%BA%84%E8%A1%97%E9%81%93%E6%98%A5%E6%BD%AE%E8%8A%B1%E5%9B%AD%E4%BA%8C%E5%8C%BA%E5%8C%97%E5%8C%BA","recipient_phone":"131****4313_9646","recipient_name":"%E8%8C%83%E8%8E%BA%28%E5%A5%B3%E5%A3%AB%29","shipping_fee":5,"total":61.1,"original_price":113.1,"invoice_title":"","taxpayer_id":"","ctime":1649482282,"delivery_time":1649490000,"estimate_arrival_time":1649490000,"latitude":31.544249,"longitude":120.37051,"day_seq":2,"logistics_code":"1003","total_weight":150,"caution":"%E6%94%B6%E9%A4%90%E4%BA%BA%E9%9A%90%E7%A7%81%E5%8F%B7+131****4313_9646%EF%BC%8C%E6%89%8B%E6%9C%BA%E5%8F%B7+139****4855","pay_type":2,"pick_type":0,"package_bag_money_yuan":"0.10","detail":"%5B%7B%22actual_price%22%3A59.0%2C%22app_food_code%22%3A%22100079%22%2C%22app_spu_code%22%3A%22100079%22%2C%22box_num%22%3A0.0%2C%22box_price%22%3A0.0%2C%22detail_extra%22%3A%7B%22cate%22%3A%22%E5%AE%A0%E7%89%A9%E7%94%9F%E6%B4%BB%22%7D%2C%22food_discount%22%3A1.0%2C%22food_name%22%3A%22%E9%BA%A6%E5%BE%B7%E6%B0%8FIN-VET%E9%B2%A8%E9%B1%BC%E8%BD%AF%E9%AA%A8%E7%B2%BE%E5%8D%8E%E7%B4%A0+150g%22%2C%22mt_sku_id%22%3A102****6898%2C%22mt_spu_id%22%3A9917325419%2C%22mt_tag_id%22%3A108****9143%2C%22original_price%22%3A108.0%2C%22price%22%3A59.0%2C%22quantity%22%3A1%2C%22sku_id%22%3A%22109375%22%2C%22spec%22%3A%22150+G%22%2C%22unit%22%3A%22%E4%BB%BD%22%2C%22upc%22%3A%22798304327342%22%2C%22weight%22%3A150%2C%22weight_for_unit%22%3A%22150.00%22%2C%22weight_unit%22%3A%22%E5%85%8B%28g%29%22%7D%5D","extras":"%5B%7B%22mt_charge%22%3A0.0%2C%22poi_charge%22%3A49.0%2C%22reduce_fee%22%3A49.0%2C%22remark%22%3A%22%E8%B4%AD%E4%B9%B0%E9%BA%A6%E5%BE%B7%E6%B0%8FIN-VET%E9%B2%A8%E9%B1%BC%E8%BD%AF%E9%AA%A8%E7%B2%BE%E5%8D%8E%E7%B4%A0+150g%E5%8E%9F%E4%BB%B7108.0%E5%85%83%E7%8E%B0%E4%BB%B759.0%E5%85%83%22%2C%22type%22%3A17%7D%2C%7B%22mt_charge%22%3A0.0%2C%22poi_charge%22%3A3.0%2C%22reduce_fee%22%3A3.0%2C%22remark%22%3A%22%E5%87%8F%E9%85%8D%E9%80%81%E8%B4%B93.0%E5%85%83%22%2C%22type%22%3A25%7D%2C%7B%22mt_charge%22%3A0.0%2C%22poi_charge%22%3A0.0%2C%22reduce_fee%22%3A0.0%2C%22remark%22%3A%22%E9%80%8110%E5%85%83%E5%95%86%E5%AE%B6%E4%BB%A3%E9%87%91%E5%88%B8%22%2C%22type%22%3A100%7D%5D","sku_benefit_detail":"%5B%7B%22activityPrice%22%3A59.0%2C%22app_food_code%22%3A%22100079%22%2C%22app_spu_code%22%3A%22100079%22%2C%22boxNumber%22%3A0.0%2C%22boxPrice%22%3A0.0%2C%22count%22%3A1%2C%22name%22%3A%22%E9%BA%A6%E5%BE%B7%E6%B0%8FIN-VET%E9%B2%A8%E9%B1%BC%E8%BD%AF%E9%AA%A8%E7%B2%BE%E5%8D%8E%E7%B4%A0+150g%22%2C%22originPrice%22%3A108.0%2C%22sku_id%22%3A%22109375%22%2C%22totalActivityPrice%22%3A59.0%2C%22totalBoxPrice%22%3A0.0%2C%22totalMtCharge%22%3A0.0%2C%22totalOriginPrice%22%3A108.0%2C%22totalPoiCharge%22%3A49.0%2C%22totalReducePrice%22%3A49.0%2C%22upc%22%3A%22798304327342%22%2C%22wmAppOrderActDetails%22%3A%5B%7B%22act_id%22%3A148****2191%2C%22batch_act_id%22%3A1509562929605885958%2C%22count%22%3A1%2C%22mtCharge%22%3A0.0%2C%22poiCharge%22%3A49.0%2C%22poi_act_id%22%3A5750472139%2C%22remark%22%3A%22%E8%B4%AD%E4%B9%B0%E9%BA%A6%E5%BE%B7%E6%B0%8FIN-VET%E9%B2%A8%E9%B1%BC%E8%BD%AF%E9%AA%A8%E7%B2%BE%E5%8D%8E%E7%B4%A0+150g%E5%8E%9F%E4%BB%B7108.0%E5%85%83%E7%8E%B0%E4%BB%B759.0%E5%85%83%22%2C%22sku_act_id%22%3A148****2191%2C%22type%22%3A17%7D%5D%7D%5D","poi_receive_detail_yuan":"%7B%22actOrderChargeByMt%22%3A%5B%7B%22comment%22%3A%22%E6%B4%BB%E5%8A%A8%E6%AC%BE%22%2C%22feeTypeDesc%22%3A%22%E6%B4%BB%E5%8A%A8%E6%AC%BE%22%2C%22feeTypeId%22%3A10019%2C%22money%22%3A%220.00%22%7D%5D%2C%22actOrderChargeByPoi%22%3A%5B%7B%22comment%22%3A%22%E8%B4%AD%E4%B9%B0%E9%BA%A6%E5%BE%B7%E6%B0%8FIN-VET%E9%B2%A8%E9%B1%BC%E8%BD%AF%E9%AA%A8%E7%B2%BE%E5%8D%8E%E7%B4%A0+150g%E5%8E%9F%E4%BB%B7108.0%E5%85%83%E7%8E%B0%E4%BB%B759.0%E5%85%83%22%2C%22feeTypeDesc%22%3A%22%E6%B4%BB%E5%8A%A8%E6%AC%BE%22%2C%22feeTypeId%22%3A10019%2C%22money%22%3A%2249.00%22%7D%2C%7B%22comment%22%3A%22%E5%87%8F%E9%85%8D%E9%80%81%E8%B4%B93.0%E5%85%83%22%2C%22feeTypeDesc%22%3A%22%E6%B4%BB%E5%8A%A8%E6%AC%BE%22%2C%22feeTypeId%22%3A10019%2C%22money%22%3A%223.00%22%7D%5D%2C%22foodShareFeeChargeByPoi%22%3A%223.05%22%2C%22logisticsFee%22%3A%225.00%22%2C%22onlinePayment%22%3A%2261.10%22%2C%22poiReceive%22%3A%2258.05%22%2C%22reconciliationExtras%22%3A%22%7B%5C%22performanceServiceFee%5C%22%3A%5C%220.00%5C%22%2C%5C%22chargeMode%5C%22%3A2%7D%22%7D","is_third_shipping":0}`
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				dataStr: str,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PaidOrdersMy(tt.args.dataStr)
		})
	}
}*/
