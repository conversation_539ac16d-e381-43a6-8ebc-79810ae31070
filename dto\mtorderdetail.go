package dto

type OrderDetail struct {
	AppId string `json:"app_id" form:"app_id" query:"app_id"`

	//数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
	//-1：有数据降级
	//0：无数据降级
	IncmpCode int32 `json:"incmp_code" form:"incmp_code" query:"incmp_code"`
	//订单状态，返回订单当前的状态。目前平台的订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。
	Status int32 `json:"status" form:"status" query:"status"`
	//订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。
	WmOrderIdView int64 `json:"wm_order_id_view" form:"wm_order_id_view" query:"wm_order_id_view"`
	//APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	//商家门店名称，即美团平台上当前订单所属门店的名称。
	WmPoiName string `json:"wm_poi_name" form:"wm_poi_name" query:"wm_poi_name"`
	//订单收货人地址，此字段为用户填写的收货地址。商家可在开发者中心->基础设置->订单订阅字段 页面订阅字段“13 收货地址”；若开启订阅，则订单数据会在此字段后追加根据经纬度反查地址的结果，并用“@#”符号分隔，如：用户填写地址@#反查结果。
	RecipientAddress string `json:"recipient_address" form:"recipient_address" query:"recipient_address"`
	//订单收货人联系电话，此字段信息可能推送真实手机号码或隐私号，即需兼容13812345678和13812345678_123456两种号码格式。
	RecipientPhone string `json:"recipient_phone" form:"recipient_phone" query:"recipient_phone"`
	//订单收货人姓名，同时包含用户在用户端选择的性别标识信息。(1)若用户没有填写姓名(老版本的用户端可能支持不填写)，此字段默认为空；商家可在开发者中心开启订阅“12 用户名称”字段，则平台会用“美团客人”自动填充此字段。(2)用户填写了收货人姓名时，此字段按用户所填全部内容正常展示，若姓名中包含特殊符号(如emoji表情)，需商家自行解析。
	RecipientName string `json:"recipient_name" form:"recipient_name" query:"recipient_name"`
	//门店配送费，单位是元。当前订单产生时该门店的配送费（商家自配送运费或美团配送运费），此字段数据为运费优惠前的原价。
	ShippingFee float64 `json:"shipping_fee" form:"shipping_fee" query:"shipping_fee"`
	//订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
	Total float64 `json:"total" form:"total" query:"total"`
	//订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
	OriginalPrice float64 `json:"original_price" form:"original_price" query:"original_price"`
	//发票抬头，为用户填写的开发票的抬头。
	InvoiceTitle string `json:"invoice_title" form:"invoice_title" query:"invoice_title"`
	//纳税人识别号，此字段信息默认不返回，如商家支持订单开发票，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“27 纳税人识别号”。
	TaxpayerId string `json:"taxpayer_id" form:"taxpayer_id" query:"taxpayer_id"`
	//订单创建时间，为10位秒级的时间戳，此字段为用户提交订单的时间。
	Ctime int64 `json:"ctime" form:"ctime" query:"ctime"`
	//预计送达时间。若为“立即送达”订单则推送0；若是“预订单”则推送用户选择的预计送达时间，为10位秒级的时间戳。
	DeliveryTime int64 `json:"delivery_time" form:"delivery_time" query:"delivery_time"`
	//页面开启订阅字段“35 订单预计送达时间”。关于订单预计送达时间字段的说明： (1)当用户选择订单“立即送达”(delivery_time=0)，estimate_arrival_time字段信息则为美团计算的该即时订单预计送达时间。 (2)当用户选择订单在某个特定时间送达，即为预订单。estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的订单预计送达时间。 (3)当订单为用户到店自取方式，estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的到店取货时间。
	EstimateArrivalTime int64 `json:"estimate_arrival_time" form:"estimate_arrival_time" query:"estimate_arrival_time"`
	//订单收货地址的纬度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
	Latitude float32 `json:"latitude" form:"latitude" query:"latitude"`
	//订单收货地址的经度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
	Longitude float32 `json:"longitude" form:"longitude" query:"longitude"`
	//当日订单流水号，门店每日已支付订单的流水号从1开始。
	//目前，自提订单的取货码与该订单流水号相同。
	//此字段信息默认不返回，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“15 订单流水号”。
	DaySeq int32 `json:"day_seq" form:"day_seq" query:"day_seq"`
	//订单配送方式，该字段信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“22 配送方式”。商家可在开放平台的【附录】文档中对照查看logistics_code的描述，如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等。 如商家想了解自己门店的配送方式以及如何区分等情况，请咨询美团品牌经理。
	LogisticsCode string `json:"logistics_code" form:"logistics_code" query:"logistics_code"`
	//订单商品总重量（该信息默认不返回，可在开发者中心订阅），单位为克/g。
	TotalWeight int64 `json:"total_weight" form:"total_weight" query:"total_weight"`
	//订单备注信息，是用户下单时填写和选择的备注信息；同时，当隐私号服务正常且用户开启号码保护时，此字段信息中会包含收货人脱敏真实号码和隐私号码，如“收餐人隐私号 18689114387_3473，手机号 185****2032”。
	//针对鲜花绿植品类的订单，本字段中支持展示预订人联系电话，请支持接收隐私号格式。
	Caution string `json:"caution" form:"caution" query:"caution"`
	//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
	PayType int32 `json:"pay_type" form:"pay_type" query:"pay_type"`
	//取货类型：0-普通(配送),1-用户到店自取。此字段的信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“28 取餐类型订阅字段”。
	PickType int32 `json:"pick_type" form:"pick_type" query:"pick_type"`
	//订单纬度的打包袋金额，单位是元。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“39 打包袋(元)”。
	//与package_bag_money字段相比，package_bag_money_yuan字段的单位为元，其他规则相同；订阅其中一个字段即可。
	PackageBagMoneyYuan string `json:"package_bag_money_yuan" form:"package_bag_money_yuan" query:"package_bag_money_yuan"`
	//订单商品详情，其值为由list序列化得到的json字符串
	Detail string `json:"detail" form:"detail" query:"detail"`
	//订单优惠信息，其值为由list序列化得到的json字符串。
	Extras string `json:"extras" form:"extras" query:"extras"`
	//商品优惠详情
	SkuBenefitDetail string `json:"sku_benefit_detail" form:"sku_benefit_detail" query:"sku_benefit_detail"`
	//订单纬度的商家对账信息，json格式数据。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“38 商家应收款详情(元)”。
	//与poi_receive_detail字段相比，poi_receive_detail_yuan字段里的金额类字段单位为元，其他规则相同；订阅其中一个字段即可。
	PoiReceiveDetailYuan string `json:"poi_receive_detail_yuan" form:"poi_receive_detail_yuan" query:"poi_receive_detail_yuan"`
	//是否是第三方配送平台配送,0表否，1表是）
	IsThirdShipping int32 `json:"is_third_shipping" form:"is_third_shipping" query:"is_third_shipping"`
	//美团配送骑手的姓名，取最新一次指派的骑手信息。
	LogisticsDispatcherName string `json:"logistics_dispatcher_name" form:"logistics_dispatcher_name" query:"logistics_dispatcher_name"`
	//美团配送骑手的联系电话，取最新一次指派的骑手信息。（请兼容13812345678和13812345678_123456两种号码格式，以便对接隐私号订单，最多不超过20位）
	LogisticsDispatcherMobile string `json:"logistics_dispatcher_mobile" form:"logistics_dispatcher_mobile" query:"logistics_dispatcher_mobile"`
}

//订单商品详情
type Detail struct {
	//APP方商品id，即商家中台系统里商品的编码(spu_code值)：(1)不同门店之间商品id可以重复，同一门店内商品id不允许重复。(2)字段信息限定长度不超过128个字符。(3)如此字段信息推送的是商品名称或信息为空，则表示商家没有维护商品编码，请商家自行维护。
	AppFoodCode string `json:"app_food_code"`
	//商品名称
	FoodName string `json:"food_name"`
	//SKU码(商家的规格编码)，是商品sku唯一标识码。
	SkuID string `json:"sku_id"`
	//商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
	Upc string `json:"upc"`
	//订单中此商品sku的购买数量
	Quantity int32 `json:"quantity"`
	//商品单价，单位是元。此字段信息默认返回活动折扣后价格，商家如有需求将价格替换为原价，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“18 替换菜品折扣价格为原价”。
	Price float64 `json:"price"`
	//订单中当前商品sku需使用包装盒的总数量（单件商品sku需使用包装盒数量*商品售卖数量）。单件商品sku包装盒数量是商家同步商品时维护的信息。
	BoxNum float64 `json:"box_num"`
	//包装盒单价，单位是元。为订单中当前商品sku单个包装盒的价格，是商家同步商品时维护的信息。
	BoxPrice float64 `json:"box_price"`
	//商品售卖单位
	Unit string `json:"unit"`
	//商品折扣系数，目前此字段默认为1，商家无需参考此字段的信息。
	FoodDiscount float64 `json:"food_discount"`
	//商品sku单件的重量，单位是克/g。
	Weight int32 `json:"weight"`
	//表示商品sku单件的重量数值信息，最多支持两位小数。
	WeightForUnit string `json:"weight_for_unit"`
	//表示sku的重量数值单位。
	WeightUnit string `json:"weight_unit"`
	//商品sku的规格名称
	Spec  string `json:"spec"`
	Count int
}

//平台服务费
type PoiReceiveDetailYuan struct {
	//商品分成，即平台服务费，单位为元
	FoodShareFeeChargeByPoi string `json:"foodShareFeeChargeByPoi"`
	ReconciliationExtras    string `json:"reconciliationExtras"`
}

type ReconciliationExtras struct {
	ChargeMode            int32  `json:"chargeMode"`
	PerformanceServiceFee string `json:"performanceServiceFee"`
	TechnicalServiceFee   string `json:"technicalServiceFee"`
}

//商品优惠详情
type SkuBenefitDetail struct {
	//SKU码(商家的规格编码)，是商品sku唯一标识码
	SkuId string `json:"sku_id"`

	//商品sku所参加的全部活动优惠的总金额，单位是元。
	TotalReducePrice float64 `json:"totalReducePrice"`

	//商品sku所参加的全部活动优惠，其中美团承担成本总金额，单位是元。
	TotalPoiCharge float64 `json:"totalPoiCharge"`

	//商品sku所参加的全部活动优惠的总金额，单位是元。
	TotalMtCharge float64 `json:"totalMtCharge"`

	//商品sku所参加的全部活动优惠后的实付价（单价），单位是元。
	ActivityPrice float64 `json:"activityPrice"`

	//商品sku所参加的全部活动优惠前的原价（单价），单位是元。
	OriginPrice float64 `json:"originPrice"`

	//商品sku实付金额。
	TotalActivityPrice float64 `json:"totalActivityPrice"`
	// 优惠商品数量
	Count int32 `json:"count"`
	//参与活动详情
	WmAppOrderActDetails []WmAppOrderActDetails `json:"wmAppOrderActDetails"`
}

//参与活动详情
type WmAppOrderActDetails struct {
	//优惠活动的活动id，适用于【ecommerce/order/getOrderActDetail】接口查询订单中此活动信息（注：目前已支持查询的活动类型，请参考该查询接口的说明）。
	ActId int64 `json:"act_id"`
	//优惠活动类型，参考值： 1-首单减；2-满减；3-抵价券；4-套餐赠；5-满赠；6-超时赔付；7-特价菜；8-首单返优惠劵；9-使用优惠劵；10-运营发优惠劵；11-提前下单减；12-满返优惠劵；13-当面付返优惠券；14-随机返优惠券；15-兑换红包；16-满减配送费；17-折扣菜；18-美团专送减；19-使用点评优惠券；20-第2份半价活动；21-会员免配送费；22-门店新用户立减；23-买赠活动；24-AB新客活动；25-减配送费；100-满减商家优惠券（下单返券）；101-使用商家优惠券；102-供应链自定义；103-进店领券（商家券）；26-满减AB；27-指定商品满减；50-微信钱包渠道首减；51-点评app渠道首减；52-美团app渠道首减；28-新客满减；29-新客满减AB；30-多阶梯满减配送费；31-满N件折；32-扫码购偶数件折扣；33-会员折扣/特价；34-买满件阶梯特价/折扣；35-组合特价/折扣；40-加价购；41-新客折扣菜；42-红包兑换, 不在B端配置，通过数据写入服务来创建；117-商品券；88-虚拟币；45-外卖拼团；43-X元M件 （可多种商品总计件数）；46-外卖加价购；118-商品折扣券；66-闪购会员折；48-拼团减配送费；53-新客专享减包装费；54-新客专享减配送费； 55-第N件优惠; 56-闪购爆品；57-新客专享减打包袋费（针对闪购新客爆品活动）；59-新客专享减配送费（针对闪购新客爆品活动）；201-会员首购优惠；202-会员限时特惠；304-减配送费劵；300-商家会员减配送费；305-联盟津贴；84-店外领券；123-好友助力。
	Type int32 `json:"type"`
	//优惠说明
	Remark string `json:"remark"`
	//美团承担成本，是商品sku参与本活动单次的成本，单位是元。
	MtCharge float64 `json:"mtCharge"`
	//商家承担成本，是商品sku参与本活动单次的成本，单位是元。
	PoiCharge float64 `json:"poiCharge"`
	//表示商品sku参与本活动的次数。
	//1.用折扣活动类型举例：如设置同一订单中此商品sku可享受折扣价格的限购数量是2件，那么用户购买2件及以上时，此处的count=2；如设置可享受折扣价的限购数量是1件，购买1件或多件时，此处count=1；如设置可享受折扣价的数量不限制，则此处count的值与商家购买此商品sku的数量相同。
	//2.用红包活动举例：如订单使用了一个美团红包，多种商品都享受了红包优惠，那么不同商品sku即使有多件数量，此处count的值均等于1，表示该商品仅参与了一次红包优惠活动。
	//3.用X元M件活动举例：设置活动为10元3件，如购买参加此活动的商品任意6件，则每种商品优惠均摊信息的wmAppOrderActDetails字段中count字段都为1。
	Count int32 `json:"count"`
}

//美团优惠信息
type OrderDiscountExtra struct {
	ActDetailId int64   `json:"act_detail_id"`
	MtCharge    float64 `json:"mt_charge"`
	PoiCharge   float64 `json:"poi_charge"`
	ReduceFee   float64 `json:"reduce_fee"`
	Remark      string  `json:"remark"`
	Type        int     `json:"type"`
}

type SelfDeliveryInfo struct {
	//订单号
	OrderViewId int64 `json:"order_view_id"`
	//APP方门店id
	AppPoiCode string `json:"app_poi_code"`
	//自配送配送单状态code，默认支持一种状态：20-骑手已取货。新增支持以下状态，如有则推送：0-配送单发往配送 1-已创建配送包 10-骑手已接单 15-骑手已到店 20-骑手已取货 40-骑手已送达 100-配送单已取消 新增状态目前仅针对白名单商家放开限制，如有对接需求，请在官网提工单申请开通权限。
	LogisticsStatus int `json:"logistics_status"`
	//操作时间（10位秒级时间戳），精确到秒
	OperateTime int `json:"operate_time"`
}
