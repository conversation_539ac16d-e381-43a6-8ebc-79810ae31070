package services

import (
	"context"
	"encoding/json"
	"errors"
	"external-ui/pkg/code"
	"external-ui/proto/dac"
	"external-ui/proto/oc"
	"external-ui/utils"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

const (
	OrderAppChannelRedisKey = "order-center:order:app-channel:"

	storeExternalRedisKey = "datacenter:store:external"
	orderSnRedisKey       = "datacenter:store:ordersn:"
)

//根据渠道订单号(ordersn) 获取门店的storeMasterId(appChannel)
func GetAppChannelByOrderSn(ordersn string) (int32, error) {
	glog.Info(ordersn, "根据ordersn查村appchannel")
	if len(ordersn) <= 0 {
		return 0, errors.New("ordersn 不合法")
	}
	redisConn := utils.GetRedisConn()
	key := OrderAppChannelRedisKey + ordersn
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去orderCenter查询db获取
	glog.Info(ordersn, "根据ordersn查redis值", appChannel)
	if len(appChannel) <= 0 {
		clientOrderCenter := oc.GetOrderServiceClient()
		defer clientOrderCenter.Close()
		request := &oc.QueryAppChannelByOrderSnReq{OrderSn: ordersn}
		glog.Info(ordersn, "根据ordersn查村appchannel请求参数", request)
		outQuery, err := clientOrderCenter.RPC.QueryAppChannelByOrderSn(clientOrderCenter.Ctx, request)
		if err != nil {
			glog.Error("QueryAppChannelByOrderSn rpc err", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}
		if outQuery.Code != 200 {
			glog.Error("QueryAppChannelByOrderSn rpc failed", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}

		return outQuery.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

//GetAppChannelByStoreId
//返回1.阿闻自有,2.TP代运营,0查不到门店
func GetAppChannelByStoreId(channelStoreId string) (storeMasterId int32, retCode int) {
	if len(channelStoreId) <= 0 {
		retCode = code.ErrorCommon
		return
	}
	redisConn := utils.GetRedisConn()
	data := redisConn.HGetAll(storeExternalRedisKey).Val()
	if len(data) <= 0 {
		setRedisData()
	}
	str := redisConn.HGet(storeExternalRedisKey, channelStoreId).Val()
	if len(str) <= 0 {
		storeMasterId, retCode = setRedisOne(channelStoreId)
		return
	}
	store := dac.StoreChannelExternalData{}
	err := json.Unmarshal([]byte(str), &store)
	if err != nil {
		glog.Error("解析data数据错误:StoreChannelExternalData", err)
		retCode = code.ErrorCommon
		return
	}
	return store.AppChannel, code.Success
}

func setRedisOne(channelStoreId string) (storeMasterId int32, retCode int) {
	data := getStoreChannelExternalData(channelStoreId)
	if len(data) > 0 {
		redisConn := utils.GetRedisConn()
		isOk := redisConn.HSet("datacenter:store:external", data[0].ChannelStoreId, kit.JsonEncode(data[0])).Val()
		if !isOk {
			glog.Error("set redis,datacenter:store:external:err", kit.JsonEncode(data))
			retCode = code.ErrorCommon
			return
		}
		retCode = code.Success
		storeMasterId = data[0].AppChannel
		return
	}
	retCode = code.ErrorCommon
	return
}

func setRedisData() {
	data := getStoreChannelExternalData("")
	if len(data) > 0 {
		redisValue := make(map[string]interface{}, 0)
		for _, v := range data {
			if len(v.ChannelStoreId) > 0 {
				redisValue[v.ChannelStoreId] = kit.JsonEncode(v)
			}
		}
		redisConn := utils.GetRedisConn()
		val := redisConn.HMSet("datacenter:store:external", redisValue)
		if val.Val() != "Ok" {
			glog.Error(val)
		}
	}
}

func getOrderAppChannel(orderSn string) int32 {
	dacClient := oc.GetOrderServiceClient()
	grpcRes, err := dacClient.RPC.GetOrderAppChannel(context.Background(), &oc.GetOrderAppChannelReq{OrderSn: orderSn})
	if err != nil {
		glog.Error("调用GetOrderAppChannel失败，", err, "，参数："+orderSn, err)
		return 0
	}
	if grpcRes.Code != 200 {
		glog.Error("调用GetOrderAppChannel失败，", err, "，参数：2"+orderSn, kit.JsonEncode(grpcRes))
		return 0
	}
	return grpcRes.Data.AppChannel
}

func getStoreChannelExternalData(channelStoreId string) []*dac.StoreChannelExternalData {
	data := make([]*dac.StoreChannelExternalData, 0)
	dacClient := dac.GetDataCenterClient()
	grpcRes, err := dacClient.RPC.GetStoreChannelExternal(context.Background(), &dac.GetStoreChannelExternalReq{ChannelId: 2, ChannelStoreId: channelStoreId})
	if err != nil {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2,"+channelStoreId, err)
		return data
	}
	if grpcRes.Code != 200 {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2,"+channelStoreId, kit.JsonEncode(grpcRes))
		return data
	}
	return grpcRes.Data
}
