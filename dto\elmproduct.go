package dto

// 商品信息结构体
type ElmSkuDelPush struct {
	ShopId         string   `json:"shop_id"`          // 合作方门店Id
	PlatformShopId string   `json:"platform_shop_id"` // 【内部专用】平台合作方门店Id
	upc            string   `json:"upc"`              // 条形码
	SkuId          string   `json:"sku_Id"`           // 商品Id
	Name           string   `json:"name"`             // 商品名称
	CategoryId     string   `json:"category_Id"`      // 自定义分类Id
	CategoryName   string   `json:"category_name"`    // 自定义分类名称
	CategoryList   []string `json:"category_list"`    // 自定义分类列表
	CustomSkuId    string   `json:"custom_sku_Id"`    // 商品自定义Id
	DeleteAppKey   string   `json:"delete_app_key"`   // 删除渠道商品的请求来源
	DeleteTime     string   `json:"delete_time"`      // 商品成功的时间（这里假设是时间类型）
	DeleteUserName string   `json:"delete_user_name"` // 操作账号
	DeleteReason   string   `json:"delete_reason"`    // 删除原因
}

type ElmSkuCreatePush struct {
	CreateAppKey   string `json:"createAppKey"`            // 商品创建端
	CreateTime     int64  `json:"createTime"`              // 商品成功创建的时间戳
	CreateUserName string `json:"createUserName"`          // 创建当前商品的操作账号
	SkuId          string `json:"sku_id"`                  // 饿了么商品ID
	ShopId         string `json:"shop_id,omitempty"`       // 合作方门店ID
	Upc            string `json:"upc"`                     // 条形码
	Name           string `json:"name"`                    // 商品名称
	BrandId        string `json:"brand_id,omitempty"`      // 品牌ID
	BrandName      string `json:"brand_name,omitempty"`    // 品牌名称
	CategoryId     string `json:"category_id,omitempty"`   // 店铺自定义分类ID
	CategoryName   string `json:"category_name,omitempty"` // 店铺自定义分类名称
	CustomSkuId    string `json:"custom_sku_id,omitempty"` // 商品自定义ID
}

type ElmSkuUpdatePush struct {
	ShopId         string `json:"shop_id,omitempty"`        // 合作方门店ID
	Upc            string `json:"upc"`                      // 条形码
	SkuId          string `json:"sku_id"`                   // 商品ID
	Name           string `json:"name"`                     // 商品名称
	CategoryId     int    `json:"category_id,omitempty"`    // 自定义分类ID
	CategoryName   string `json:"category_name,omitempty"`  // 自定义分类名称
	CustomSkuId    string `json:"custom_sku_id,omitempty"`  // 商品自定义ID
	UpdateAppKey   string `json:"updateAppKey,omitempty"`   // 编辑渠道商品的请求来源
	UpdateTime     int64  `json:"updateTime,omitempty"`     // 商品成功修改的时间
	UpdateUserName string `json:"updateUserName,omitempty"` // 修改当前商品的操作账号
	UpdateReason   string `json:"updateReason,omitempty"`   // 更新原因
	DiffContents   string `json:"diffContents,omitempty"`   // 修改与原值比较的JSON
}

type SkuUpdatePushDiff struct {
	Status struct {
		Origin string `json:"origin,omitempty"`
		Result string `json:"result,omitempty"`
	} `json:"status,omitempty"`
}
