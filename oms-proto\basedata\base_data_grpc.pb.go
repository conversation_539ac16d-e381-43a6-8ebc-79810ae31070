// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package basedata

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BaseDataServiceClient is the client API for BaseDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BaseDataServiceClient interface {
	// 单据类型
	OrderType(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 订单来源
	OrderSource(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 交易类型
	PayMode(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 销售渠道
	OrderChannel(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 交货方式
	DeliveryMode(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type baseDataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBaseDataServiceClient(cc grpc.ClientConnInterface) BaseDataServiceClient {
	return &baseDataServiceClient{cc}
}

func (c *baseDataServiceClient) OrderType(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/basedata.BaseDataService/OrderType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDataServiceClient) OrderSource(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/basedata.BaseDataService/OrderSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDataServiceClient) PayMode(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/basedata.BaseDataService/PayMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDataServiceClient) OrderChannel(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/basedata.BaseDataService/OrderChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDataServiceClient) DeliveryMode(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/basedata.BaseDataService/DeliveryMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseDataServiceServer is the server API for BaseDataService service.
// All implementations should embed UnimplementedBaseDataServiceServer
// for forward compatibility
type BaseDataServiceServer interface {
	// 单据类型
	OrderType(context.Context, *CommonRequest) (*CommonReply, error)
	// 订单来源
	OrderSource(context.Context, *CommonRequest) (*CommonReply, error)
	// 交易类型
	PayMode(context.Context, *CommonRequest) (*CommonReply, error)
	// 销售渠道
	OrderChannel(context.Context, *CommonRequest) (*CommonReply, error)
	// 交货方式
	DeliveryMode(context.Context, *CommonRequest) (*CommonReply, error)
}

// UnimplementedBaseDataServiceServer should be embedded to have forward compatible implementations.
type UnimplementedBaseDataServiceServer struct {
}

func (UnimplementedBaseDataServiceServer) OrderType(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderType not implemented")
}
func (UnimplementedBaseDataServiceServer) OrderSource(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderSource not implemented")
}
func (UnimplementedBaseDataServiceServer) PayMode(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayMode not implemented")
}
func (UnimplementedBaseDataServiceServer) OrderChannel(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderChannel not implemented")
}
func (UnimplementedBaseDataServiceServer) DeliveryMode(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliveryMode not implemented")
}

// UnsafeBaseDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BaseDataServiceServer will
// result in compilation errors.
type UnsafeBaseDataServiceServer interface {
	mustEmbedUnimplementedBaseDataServiceServer()
}

func RegisterBaseDataServiceServer(s grpc.ServiceRegistrar, srv BaseDataServiceServer) {
	s.RegisterService(&BaseDataService_ServiceDesc, srv)
}

func _BaseDataService_OrderType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDataServiceServer).OrderType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/basedata.BaseDataService/OrderType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDataServiceServer).OrderType(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDataService_OrderSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDataServiceServer).OrderSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/basedata.BaseDataService/OrderSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDataServiceServer).OrderSource(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDataService_PayMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDataServiceServer).PayMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/basedata.BaseDataService/PayMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDataServiceServer).PayMode(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDataService_OrderChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDataServiceServer).OrderChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/basedata.BaseDataService/OrderChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDataServiceServer).OrderChannel(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDataService_DeliveryMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDataServiceServer).DeliveryMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/basedata.BaseDataService/DeliveryMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDataServiceServer).DeliveryMode(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BaseDataService_ServiceDesc is the grpc.ServiceDesc for BaseDataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BaseDataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "basedata.BaseDataService",
	HandlerType: (*BaseDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OrderType",
			Handler:    _BaseDataService_OrderType_Handler,
		},
		{
			MethodName: "OrderSource",
			Handler:    _BaseDataService_OrderSource_Handler,
		},
		{
			MethodName: "PayMode",
			Handler:    _BaseDataService_PayMode_Handler,
		},
		{
			MethodName: "OrderChannel",
			Handler:    _BaseDataService_OrderChannel_Handler,
		},
		{
			MethodName: "DeliveryMode",
			Handler:    _BaseDataService_DeliveryMode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "basedata/base_data.proto",
}
