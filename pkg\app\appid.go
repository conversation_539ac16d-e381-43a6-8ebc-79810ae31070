package app

import (
	"context"
	"external-ui/pkg/code"
	"external-ui/pkg/util"
	"external-ui/pkg/util/cache"
	"external-ui/proto/dac"
	"strconv"
	"time"

	logger "github.com/maybgit/glog"
)

const (
	DelimiterForStoreMaster = "|||"
	// 饿了么店铺主体信息  key:storeMasterId  value:appid ||| appsecret
	StoreMasterElmRedisKey = "datacenter:store:master-info-elm:"
	// 美团店铺主体信息 key:storeMasterId value:appid ||| appsecret
	StoreMasterMtRedisKey = "datacenter:store:master-info-mt:"
	// 京东到家店铺主体信息 key:storeMasterId value:appid ||| appsecret
	StoreMasterJddjRedisKey = "datacenter:store:master-info-jddj:"
	// 京东到家店铺主体token key:storeMasterId value:apptoken
	StoreMasterJddjTokenRedisKey = "datacenter:store:master-info-jddj-token:"

	// 饿了么店铺主体Id  key:appId   value:storeMasterId
	StoreMasterIdElmRedisKey = "datacenter:store:master-id-elm:"
	// 美团店铺主体Id key:appId  value:storeMasterId
	StoreMasterIdMtRedisKey = "datacenter:store:master-id-mt:"
	// 京东到家店铺主体Id key:appId  value:storeMasterId
	StoreMasterIdJddjRedisKey = "datacenter:store:master-id-jddj:"
)

const (
	Channel_ELM  = 2
	Channel_MT   = 3
	Channel_JDDJ = 4
)

// 渠道的应用配置
type ChannelAppConfig struct {
	AppId     string
	AppSecret string
	// AppToken 只有京东到家渠道才有
	AppToken string
}

func GetStoreMasterId(appId string, channelId int32) (storeMasterId int32, retCode int) {
	retCode = code.ErrorCommon

	logHead := "GetStoreMasterId:"
	redisConn := cache.GetRedisConn()
	// channelId  2美团,3饿了么,4京东到家
	key := ""
	switch channelId {
	case Channel_ELM:
		key = StoreMasterIdElmRedisKey + appId
	case Channel_MT:
		key = StoreMasterIdMtRedisKey + appId
	case Channel_JDDJ:
		key = StoreMasterIdJddjRedisKey + appId
	default:
		retCode = code.ErrorCommon
		return
	}

	storeMasterIdTemp, _ := strconv.Atoi(redisConn.Get(key).Val())
	if storeMasterIdTemp != 0 {
		//logger.Error(logHead, "storeMasterIdStr is null,", appId, channelId)
		retCode = code.Success
		storeMasterId = int32(storeMasterIdTemp)
		return
	}

	dacClient := dac.GetDataCenterClient()
	requestParam := &dac.GetStoreMasterIDRequest{AppId: appId, ChannelId: channelId}
	resp, err := dacClient.RPC.GetStoreMasterIDByAppId(context.Background(), requestParam)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		logger.Error(logHead, "dacClient.RPC.GetStoreMasterIDRequest,", err)
		return
	}

	if resp.StoreMasterId != 0 {
		redisConn.SetNX(key, resp.StoreMasterId, 360*time.Hour)
	}

	retCode = code.Success
	storeMasterId = resp.StoreMasterId
	return
}

func GetStoreMasterChannelAppConfig(id int32, channelId int32) (appConfig *ChannelAppConfig, retCode int) {
	logHead := "GetStoreMasterChannelAppConfig:"
	redisConn := cache.GetRedisConn()
	retCode = code.Success

	appConfig = new(ChannelAppConfig)
	key := ""

	tokenKey := ""
	jddjToken := ""
	switch channelId {
	case Channel_ELM:
		key = StoreMasterElmRedisKey + strconv.Itoa(int(id))
	case Channel_MT:
		key = StoreMasterMtRedisKey + strconv.Itoa(int(id))
	case Channel_JDDJ:
		key = StoreMasterJddjRedisKey + strconv.Itoa(int(id))
		tokenKey = StoreMasterJddjTokenRedisKey + strconv.Itoa(int(id))
		jddjToken = redisConn.Get(tokenKey).Val()
		if jddjToken == "" {
			logger.Error(logHead, "京东到家token缺失,", id, channelId)
		}
		appConfig.AppToken = jddjToken
	default:
		retCode = code.ErrorCommon
		return
	}

	StoreMasterInfo := redisConn.Get(key).Val()
	if StoreMasterInfo != "" {
		slice := util.StringToSlice(StoreMasterInfo, DelimiterForStoreMaster)
		if len(slice) >= 2 {
			appConfig.AppId = slice[0]
			appConfig.AppSecret = slice[1]
			return
		}
		logger.Error(logHead, "util.StringToSlice failed,", StoreMasterInfo, id, channelId)
	}
	logger.Info(logHead, "get redis failed,", StoreMasterInfo, id, channelId)

	dacClient := dac.GetDataCenterClient()
	requestParam := &dac.GetStoreMasterInfoRequest{Id: id}
	resp, err := dacClient.RPC.GetStoreMasterInfo(context.Background(), requestParam)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		logger.Error(logHead, "dacClient.RPC.GetStoreMasterInfo,", err)
		return
	}
	if resp.Data.ElmAppId != "" && resp.Data.ElmAppSecret != "" {
		key := StoreMasterElmRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.ElmAppId+DelimiterForStoreMaster+resp.Data.ElmAppSecret, 360*time.Hour) // 1 年
	}
	if resp.Data.MtAppId != "" && resp.Data.MtAppSecret != "" {
		key := StoreMasterMtRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.MtAppId+DelimiterForStoreMaster+resp.Data.MtAppSecret, 360*time.Hour) // 1 年
	}
	if resp.Data.JddjAppId != "" && resp.Data.JddjAppSecret != "" {
		key := StoreMasterJddjRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.JddjAppId+DelimiterForStoreMaster+resp.Data.JddjAppSecret, 360*time.Hour) // 1 年
	}
	switch channelId {
	case Channel_ELM:
		appConfig.AppId = resp.Data.ElmAppId
		appConfig.AppSecret = resp.Data.ElmAppSecret
	case Channel_MT:
		appConfig.AppId = resp.Data.MtAppId
		appConfig.AppSecret = resp.Data.MtAppSecret
	case Channel_JDDJ:
		appConfig.AppId = resp.Data.JddjAppId
		appConfig.AppSecret = resp.Data.JddjAppSecret
	}
	return
}

// 根据京东到家AppKey,获取AppToken
func GetStoreMasterAppToken(appKey string) (AppToken string, retCode int) {
	retCode = code.ErrorCommon
	if appKey == "" {
		return
	}
	var storeMasterId int32
	storeMasterId, retCode = GetStoreMasterId(appKey, Channel_JDDJ)
	if retCode != code.Success {
		logger.Error("GetStoreMasterAppTokenByStoreMasterId", "GetStoreMasterId,", appKey, Channel_JDDJ)
		return
	}

	redisConn := cache.GetRedisConn()
	tokenKey := StoreMasterJddjTokenRedisKey + strconv.Itoa(int(storeMasterId))
	jddjToken := redisConn.Get(tokenKey).Val()
	if jddjToken == "" {
		logger.Error("GetStoreMasterAppTokenByStoreMasterId", "京东到家token缺失,", storeMasterId)
	}
	AppToken = jddjToken
	retCode = code.Success
	return
}
