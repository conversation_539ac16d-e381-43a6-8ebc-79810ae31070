package controller

import "testing"

func Test_oldOrderSnIsExist(t *testing.T) {
	type args struct {
		oldOrderSn string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"test1",
			args{
				"16028029354407580",
			},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := oldOrderSnIsExist(tt.args.oldOrderSn); got != tt.want {
				t.Errorf("oldOrderSnIsExist() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestJddjOrderPush(t *testing.T) {
	type args struct {
		billId   string
		isAdjust int32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "利用查询订单详情接口推送订单到订单中心",
			args: args{
				billId:   "2107550629000261",
				isAdjust: 0,
			},
			want: "ok",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := JddjOrderPush(tt.args.billId, 1, tt.args.isAdjust); got != tt.want {
				t.Errorf("JddjOrderPush() = %v, want %v", got, tt.want)
			}
		})
	}
}
