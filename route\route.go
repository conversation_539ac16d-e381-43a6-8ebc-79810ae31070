package route

import (
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"
	rpMiddleware "github.com/tricobbler/echo-tool/middleware"
	"go.elastic.co/apm/module/apmechov4"
	"os"
	"strings"

	"external-ui/controller"

	_ "external-ui/docs"
	"github.com/labstack/echo/v4"
)

func InitRoute() *echo.Echo {
	e := echo.New()

	//swagger文档
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	//开启debug模式
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "staging" || env == "uat" || env == "" {
		e.Debug = true
	}

	if e.Debug {
		e.Use(apmechov4.Middleware())
	} else {
		//错误中间件
		e.Use(rpMiddleware.MyRecover(middleware.RecoverConfig{
			Skipper:           middleware.DefaultSkipper,
			StackSize:         4 << 10, // 4 KB
			DisableStackAll:   false,
			DisablePrintStack: false,
		}))
	}

	return Route(e)
}

// Route 路由
func Route(e *echo.Echo) *echo.Echo {
	external := e.Group("/external")

	external.GET("/tool/deal_channel_product", controller.DealChannelProduct)
	external.GET("/test/get", controller.TestMp)

	// 发票回调地址
	external.POST("/invoice/callback", controller.InvoiceCallback)

	//美团回调
	mtCallbackGroup(external)

	//美配回调
	mpCallbackGroup(external)

	//美配模拟
	mpTestGroup(external)

	//美团模拟
	mtTestGroup(external)

	//饿了么回调
	elmCallbackGroup(external)

	//蜂鸟
	FnCallbackGroup(external)
	//达达
	DaDaCallbackGroup(external)
	//京东到家回调
	jddjCallbackGroup(external)

	refundOrderGroup(external)

	//价格同步
	priceSyncGroup(external)

	//保险的回调
	insuranceCallbackGroup(external)

	//闪送回调
	issCallbackGroup(external)

	// 小程序回调
	miniProgramCallbackGroup(external)
	weiMengCallbackGroup(external)

	cardGroup(external)
	MytCallbackGroup(external)
	return e
}
