package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/pkg/app"
	"external-ui/pkg/code"
	"external-ui/proto/dac"
	"external-ui/proto/et"
	"external-ui/proto/oc"
	"external-ui/services"
	"external-ui/utils"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	logger "github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 京东到家消息通知回调响应
type CallbackResponse struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

// 根据京东到家 应用商家Id,获取StoreMasterId
func GetStoreMasterIdByMerchantId(ctx echo.Context) (storeMasterId int32, retCode int) {
	retCode = code.ErrorCommon
	params, err := ctx.FormParams()
	if err != nil {
		glog.Error("GetStoreMasterIdByMerchantId FormParams，err:", err)
		return
	}
	if len(params.Get("token")) == 0 {
		return
	}

	tokenParam := &struct {
		Token     string `json:"token"`
		ExpiresIn string `json:"expires_in"`
		VenderId  string `json:"venderId"`
	}{}
	if err = json.Unmarshal([]byte(params.Get("token")), tokenParam); err != nil {
		glog.Error("京东到家回调token接口解析token参数失败，err:", err)
	}

	dacClient := dac.GetDataCenterClient()
	requestParam := &dac.GetStoreMasterIdByMerchantIdRequest{JddjAppMerchantId: tokenParam.VenderId}
	resp, err := dacClient.RPC.GetStoreMasterIdByMerchantId(context.Background(), requestParam)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		logger.Error("GetStoreMasterIdByMerchantId", "dacClient.RPC.GetStoreMasterIdByMerchantId,", err)
		return
	}
	storeMasterId = resp.Id
	retCode = code.Success
	return
}

// 根据京东到家AppKey,获取StoreMasterId
func GetStoreMasterIdFromAppKey(appKey string) (storeMasterId int32, retCode int) {
	retCode = code.ErrorCommon

	if len(appKey) == 0 {
		glog.Error("GetStoreMasterIdFromAppKey params.Get，err:", appKey)
		return
	}
	storeMasterId, retCode = app.GetStoreMasterId(appKey, app.Channel_JDDJ)
	return
}

// 根据京东到家StoreMasterId,获取AppToken
func GetStoreMasterAppTokenByStoreMasterId(storeMasterId int32) (AppToken string, retCode int) {
	retCode = code.ErrorCommon

	redisConn := utils.GetRedisConn()
	tokenKey := app.StoreMasterJddjTokenRedisKey + strconv.Itoa(int(storeMasterId))
	jddjToken := redisConn.Get(tokenKey).Val()
	if jddjToken == "" {
		logger.Info("GetStoreMasterAppTokenByStoreMasterId", "未找到旧京东到家token,", storeMasterId)
	}
	AppToken = jddjToken
	retCode = code.Success
	return
}

// 新建售后单申请消息回调（客户创建新售后单提交申请，开放平台根据商家编号将售后单号推送给相应的商家，商家对新建售后单进行审核。）
func JddjNewApplyAfterSaleBill(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(params["app_key"].(string))
	if retCode != code.Success {
		glog.Error("JddjNewApplyAfterSaleBill", "GetStoreMasterIdFromAppKey", retCode)
	}

	glog.Info("创建售后单申请消息回调入参 JddjNewApplyAfterSaleBill：", params)
	resp.Data = services.CreateAfterSalesOrder(params["billId"].(string), storeMasterId)
	return ctx.JSON(http.StatusOK, resp)
}

// 用户取消申请消息回调（场景描述：从商家接单到众包配送交接或商家自送拣货完成期间，用户可以申请取消订单，到家推送订单取消申请消息，商家对用户取消申请进行取消确认或驳回。）
func JddjApplyCancelOrder(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	// 消息状态ID(20030:用户取消申请)
	if params["statusId"] == "20030" {
		ocClient := oc.GetOrderServiceClient()
		out, err := ocClient.ROC.CancelOrderByJd(kit.SetTimeoutCtx(context.Background()), &oc.CancelOrderByJdRequest{
			ExternalOrderSn: params["billId"].(string),
		})
		if err != nil {
			glog.Error("京东到家用户取消订单申请异常 JddjApplyCancelOrder：", params, err)
			resp.Code = -1
			resp.Msg = "取消订单申请异常"
			return ctx.JSON(http.StatusOK, resp)
		}
		if out.Code != 200 {
			glog.Warning("京东到家用户取消订单申请异常 JddjApplyCancelOrder：", kit.JsonEncode(out))
			resp.Code = -1
			resp.Msg = "取消订单申请异常，" + out.Message
			return ctx.JSON(http.StatusOK, resp)
		}
		glog.Info("京东到家用户取消订单申请结果 JddjApplyCancelOrder：", params, kit.JsonEncode(out))
	} else {
		glog.Info("用户取消申请消息回调入参 JddjApplyCancelOrder：", kit.JsonEncode(params))
		resp.Data = "京东到家用户取消订单申请消息状态ID不正确：" + params["statusId"].(string)
	}
	return ctx.JSON(http.StatusOK, resp)
}

// 用户取消/审核消息回调（商家确认接单之前，用户取消订单，推送订单取消消息；订单开始配送之前，到家客服可以取消订单，推送订单取消消息；商家审核用户取消申请，如审核通过后，推送订单取消消息；客户拒收订单时，订单处于锁定状态，商家确认收到拒收或商品后，推送订单取消消息。）
func JddjUserCancelOrder(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	glog.Info("京东到家 用户取消消息回调入参 JddjUserCancelOrder：", params)
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(params["app_key"].(string))
	if retCode != code.Success {
		glog.Error("JddjUserCancelOrder,", retCode)
	}
	// 消息状态ID(20020:订单用户取消)
	if params["statusId"] == "20020" {
		ocClient := oc.GetOrderServiceClient()
		out, e := ocClient.RPC.CancelOrder(kit.SetTimeoutCtx(context.Background()), &oc.CancelOrderRequest{
			OrderSn:      params["billId"].(string),
			CancelReason: "用户取消订单",
			IsRefund:     1,
		})
		glog.Info("用户取消订单回调 JddjUserCancelOrder 结果：", out, " 异常：", e)
	} else if params["statusId"] == "20031" || params["statusId"] == "20032" {
		// 审核结果状态 20031 商家同意取消申请,20032 商家驳回取消申请
		out, _ := services.AfterSalesResponse(params["billId"].(string), params["statusId"].(string), storeMasterId)
		resp.Data = out.Message
	} else {
		resp.Msg = "当前订单状态码异常：" + params["statusId"].(string)
	}
	return ctx.JSON(http.StatusOK, resp)
}

// 商家审核用户取消申请消息回调（用户申请取消订单，商家同意或驳回用户取消申请审核操作后，推送商家审核用户取消申请消息。）
func JddjVenderAuditApplyCancelOrder(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(params["app_key"].(string))
	if retCode != code.Success {
		glog.Error("JddjVenderAuditApplyCancelOrder,", retCode)
	}
	glog.Info("商家审核用户取消申请消息回调入参 JddjVenderAuditApplyCancelOrder：", params)
	// 审核结果状态 20031 商家同意取消申请,20032 商家驳回取消申请
	out, _ := services.AfterSalesResponse(params["billId"].(string), params["statusId"].(string), storeMasterId)
	resp.Data = out.Message
	return ctx.JSON(http.StatusOK, resp)
}

// 新建售后单审核通过消息回调（售后单审核通过后，开放平台根据商家编号将售后单号推送给相应的商家， 针对用户申请售后单是物流责任的售后单，平台不再推送售后单消息给商家。）
func JddjNewAfterSaleBill(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}

	glog.Info("新建售后单审核通过消息回调入参 JddjNewAfterSaleBill：", params)
	// 30 退款售后单
	// 90 直陪售后单
	// 110 退货售后单
	return ctx.JSON(http.StatusOK, resp)
}

// 售后单状态消息回调（售后单状态变更后，开放平台根据商家编号将售后单号和状态推送给相应的商家。）
func JddjAfterSaleBillStatus(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(params["app_key"].(string))
	if retCode != code.Success {
		glog.Error("JddjAfterSaleBillStatus,", retCode)
	}
	var stringJson string
	dtoData, err := services.GetJddjAfsServiceByBillId(params["billId"].(string), storeMasterId)
	if err == nil && dtoData.Success {
		// 售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,50:客户取消,60:商家收货审核不通过,70:已解决, 91:直赔,92:直赔成功,
		// 93:直赔失败,90:待赔付, 110:待退货,111:取货成功,1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
		// 同意
		if dtoData.Result.AfsServiceState == 30 || dtoData.Result.AfsServiceState == 110 || dtoData.Result.AfsServiceState == 32 || dtoData.Result.AfsServiceState == 114 {
			out, _ := services.AfterSalesResponse(params["billId"].(string), "20031", storeMasterId)
			resp.Data = out.Message
			stringJson = kit.JsonEncode(out)
			// 驳回
		} else if dtoData.Result.AfsServiceState == 40 || dtoData.Result.AfsServiceState == 60 {
			out, _ := services.AfterSalesResponse(params["billId"].(string), "20032", storeMasterId)
			resp.Data = out.Message
			stringJson = kit.JsonEncode(out)
			// 撤销
		} else if dtoData.Result.AfsServiceState == 50 {
			ocClient := oc.GetOrderServiceClient()
			cancelData := oc.RefundOrderCancelRequest{
				RefundOrderSn: dtoData.Result.AfsServiceOrder,
				ResType:       services.GetApproveType(dtoData.Result.AfsServiceState, dtoData.Result.ApproveType),
				OperationType: "撤销售后申请",
				OperationUser: "RP",
				Reason:        services.GetRefundReason(dtoData.Result.QuestionTypeCid),
			}
			if out, e := ocClient.ROC.RefundOrderCancel(kit.SetTimeoutCtx(context.Background()), &cancelData); e != nil {
				glog.Error("京东到家调用订单中心撤销售后单错误，入参", kit.JsonEncode(cancelData), " 异常信息：", e)
				resp.Code = http.StatusBadRequest
				resp.Data = e.Error()
			} else if out != nil {
				resp.Code = out.Code
				resp.Data = out.Message
				stringJson = kit.JsonEncode(out)
			}
		}
	}

	// 110:待退货
	glog.Info("售后单状态消息回调入参 JddjAfterSaleBillStatus：", params, " 结果：", stringJson, " 售后单详情：", kit.JsonEncode(dtoData))
	return ctx.JSON(http.StatusOK, resp)
}

// 查询订单可售后商品金额接口
func GetJddjOrderCalcMoney(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "SUCCESS", Code: http.StatusOK}
	req := new(et.JddjOrderCalcMoneyRequest)
	req.OrderId = ctx.FormValue("orderId")
	appKey := ctx.FormValue("app_key")
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(appKey)
	if retCode != code.Success {
		glog.Error("GetJddjOrderCalcMoney,", retCode)
		resp.Code = http.StatusBadRequest
		resp.Msg = "获取店铺主体信息失败"
		return ctx.JSON(http.StatusOK, resp)
	}
	req.StoreMasterId = storeMasterId
	json.Unmarshal([]byte(ctx.FormValue("skuList")), &req.SkuList)
	out, err := services.GetOrderAfterSaleDetail(req)
	if err != nil {
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		return ctx.JSON(http.StatusOK, resp)
	}
	return ctx.JSON(http.StatusOK, out)
}

// 商家自主发起售后接口
func JddjMerchantInitiateAfterSale(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "SUCCESS", Code: http.StatusOK}
	params, _ := ctx.FormParams()
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	appKey := ctx.FormValue("app_key")
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(appKey)
	if retCode != code.Success {
		glog.Error("JddjMerchantInitiateAfterSale", retCode)
	}
	applyData := new(et.JddjMerchantInitiateAfterSaleRequest)
	applyData.OrderId = ctx.FormValue("orderId")
	applyData.Pin = ctx.FormValue("pin")
	applyData.QuestionTypeCode = ctx.FormValue("questionTypeCode")
	applyData.QuestionDesc = ctx.FormValue("questionDesc")
	applyData.QuestionPic = ctx.FormValue("questionPic")
	applyData.CustomerName = ctx.FormValue("customerName")
	applyData.CustomerMobilePhone = ctx.FormValue("customerMobilePhone")
	applyData.Address = ctx.FormValue("address")
	applyData.StoreMasterId = storeMasterId
	json.Unmarshal([]byte(ctx.FormValue("skuList")), &applyData.SkuList)

	etClient := et.GetExternalClient()
	defer etClient.Close()

	// 请求查询售后单详情接口
	//todo tp jd
	out, err := etClient.JddjOrder.JddjMerchantInitiateAfterSale(etClient.Ctx, applyData)
	if err != nil {
		glog.Error("京东到家 商家自主发起售后接口 失败", params, err.Error())
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		return ctx.JSON(http.StatusOK, resp)
	}
	if out.Message != "SUCCESS" {
		resp.Code = http.StatusBadRequest
		resp.Msg = out.Message
		return ctx.JSON(http.StatusOK, resp)
	}
	resultData := dto.ProcessResult{}
	json.Unmarshal([]byte(out.Data), &resultData)
	glog.Error("京东到家商家自主发起售后接口处理结果：", resultData, " 订单号：", applyData.OrderId, " 入参：", params)
	return ctx.JSON(http.StatusOK, out)
}

// 商家审核用户取消申请接口
func JddjOrderCancelOperate(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "SUCCESS", Code: http.StatusOK}
	etClient := et.GetExternalClient()
	defer etClient.Close()

	params, _ := ctx.FormParams()
	var isAgreed bool
	if ctx.FormValue("isAgreed") == "" {
		resp.Code = http.StatusBadRequest
		resp.Msg = "审核状态只能为（ true 同意 false驳回）"
		return ctx.JSON(http.StatusOK, resp)
	} else if ctx.FormValue("isAgreed") == "true" {
		isAgreed = true
	} else if ctx.FormValue("isAgreed") == "false" {
		isAgreed = false
	}

	storeMasterId, err := GetAppChannelByOrderSn(ctx.FormValue("orderId"))
	if err != nil {
		glog.Error("JddjOrderCancelOperate,", "GetAppChannelByOrderSn,", ctx.FormValue("orderId"))
		return ctx.JSON(http.StatusOK, resp)
	}

	// 请求商家审核用户取消申请接口
	out, err := etClient.JddjOrder.JddjOrderCancelOperate(etClient.Ctx, &et.JddjOrderCancelOperateRequest{
		OrderId:       ctx.FormValue("orderId"),
		IsAgreed:      isAgreed,
		Operator:      ctx.FormValue("operator"),
		Remark:        ctx.FormValue("remark"),
		StoreMasterId: storeMasterId,
	})
	if err != nil {
		glog.Error("京东到家 请求商家审核用户取消申请接口 失败", params, err.Error())
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		return ctx.JSON(http.StatusOK, resp)
	}
	if out.Message != "SUCCESS" {
		resp.Code = http.StatusBadRequest
		resp.Msg = out.Message
		return ctx.JSON(http.StatusOK, resp)
	}
	resultData := dto.ProcessResult{}
	json.Unmarshal([]byte(out.Data), &resultData)
	glog.Error("京东到家请求商家审核用户取消申请接口处理结果：", resultData, " 入参：", params)
	return ctx.JSON(http.StatusOK, out)
}

// 修改售后单申请消息
func JddjUpdateApplyAfterSaleBill(ctx echo.Context) error {
	resp := CallbackResponse{Msg: "success"}
	params := GetCallbackParam(ctx)
	if len(params) <= 0 {
		return ctx.JSON(http.StatusOK, resp)
	}
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(params["app_key"].(string))
	if retCode != code.Success {
		glog.Error("JddjUpdateApplyAfterSaleBill", retCode)
	}
	dtoData, err := services.GetJddjAfsServiceByBillId(params["billId"].(string), storeMasterId)
	if err == nil && dtoData.Success && dtoData.Result.AfsServiceState == 10 {
		ocClient := oc.GetOrderServiceClient()
		// 先删除售后单
		out, _ := ocClient.ROC.DeleteRefundOrder(kit.SetTimeoutCtx(context.Background()), &oc.DeleteRefundOrderRequest{RefundSn: params["billId"].(string)})
		// 重新生成新的售后单
		resp.Data = services.CreateAfterSalesOrder(params["billId"].(string), storeMasterId)
		glog.Info("京东到家修改售后单申请消息 UpdateApplyAfterSaleBill：", params, " 删除售后单结果：", kit.JsonEncode(out), "重新生成售后单结果：", resp.Data, " 售后单详情：", kit.JsonEncode(dtoData))
	} else {
		glog.Info("京东到家修改售后单申请消息 UpdateApplyAfterSaleBill：", params, " 售后单详情：", kit.JsonEncode(dtoData))
	}
	return ctx.JSON(http.StatusOK, resp)
}

func JddjCallback(c echo.Context) error {
	resp := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}{
		Code: -1,
	}

	params, err := c.FormParams()
	if err != nil {
		glog.Error("京东到家回调token接口入参获取失败，err:", err)
		resp.Msg = err.Error()
		return c.JSON(200, resp)
	}
	storeMasterId, retCode := GetStoreMasterIdByMerchantId(c)
	if retCode != code.Success {
		glog.Error("JddjCallback,", "GetStoreMasterIdByMerchantId,", retCode)
		return c.JSON(200, resp)
	}
	glog.Info("JddjCallback入参，", params, ",", storeMasterId)

	if len(params.Get("token")) > 0 {
		tokenParam := &struct {
			Token     string `json:"token"`
			ExpiresIn string `json:"expires_in"`
		}{}

		if err = json.Unmarshal([]byte(params.Get("token")), tokenParam); err != nil {
			glog.Error("京东到家回调token接口解析token参数失败，err:", err)
			resp.Msg = err.Error()
			return c.JSON(200, resp)
		}

		redis := utils.GetRedisConn()

		//oldToken := redis.Get("channelJddjToken").Val()
		oldToken, retCode := GetStoreMasterAppTokenByStoreMasterId(storeMasterId)
		if retCode != code.Success {
			glog.Error("JddjCallback,", "GetStoreMasterAppTokenByStoreMasterId,", retCode)
		}

		//有旧token代表是续期token
		if len(oldToken) > 0 {
			client := et.GetExternalClient()
			defer client.Close()

			if _, err = client.JddjPlatform.VerificationUpdateToken(context.Background(), &et.VerificationUpdateTokenRequest{
				OldToken:      oldToken,
				NewToken:      tokenParam.Token,
				StoreMasterId: storeMasterId,
			}); err != nil {
				glog.Error(err)
				resp.Msg = err.Error()
				return c.JSON(200, resp)
			}
		}
		tokenKey := app.StoreMasterJddjTokenRedisKey + strconv.Itoa(int(storeMasterId))
		//token写入redis  "channelJddjToken"
		glog.Info("京东到家回调token接口保存,", ",", tokenKey, ",", tokenParam.Token)
		if err = redis.Set(tokenKey, tokenParam.Token, time.Duration(cast.ToInt64(tokenParam.ExpiresIn))*time.Second).Err(); err != nil {
			glog.Error("京东到家回调token接口保存token失败，err:", err, storeMasterId)
			resp.Msg = err.Error()
			return c.JSON(200, resp)
		}
	}

	return nil
}

//创建新订单
func JddjNewOrder(c echo.Context) error {
	resp := dto.JddjReturn{
		Msg: "success",
	}
	params := GetJdParams(c)
	appKey := params["app_key"].(string)
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(appKey)
	if retCode != code.Success {
		glog.Error("JddjNewOrder", "GetStoreMasterIdFromAppKey", retCode)
	}

	paramJson := GetJdParamJson(params, storeMasterId)

	modedata := dto.JddjOrderCreate{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		resp.Code = -1
		resp.Msg = err.Error()
		glog.Error("创建订单OrderCreate：", modedata.BillId, ""+err.Error())
		return c.JSON(200, resp)
	}

	redisConn := utils.GetRedisConn()
	lockCard := "lock:jddjOrderCreate" + modedata.BillId
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Minute).Val()
	if !lockRes {
		glog.Info("创建订单OrderCreate1 lock:jddjOrderCreate" + modedata.BillId + "存在：")
		resp.Code = -1
		resp.Msg = "fail"
		return c.JSON(200, resp)
	}
	defer redisConn.Del(lockCard)

	//判断第三方订单号是否存在
	ok := oldOrderSnIsExist(modedata.BillId)
	if ok {
		return c.JSON(200, resp)
	}

	//推送订单中心，推送失败后继续推送，次数10次
	result := "fail"
	for i := 0; i < 10; i++ {
		result = JddjOrderPush(modedata.BillId, storeMasterId, 0)
		if result == "ok" {
			break
		}
	}

	if result != "ok" {
		resp.Code = -1
		resp.Msg = "fail"
	}
	return c.JSON(200, resp)
}

//推送京东到家已完成订单
func JddjOrderCompletedCallback(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = -1
	resp.Msg = "fail"
	params := GetJdParams(c)
	paramJson := GetJdParamJson(params, 0)

	glog.Info("推送京东到家已完成订单接收参数：" + paramJson + "")
	modedata := dto.JddjOrderCompleted{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error("完成订单 ：", modedata.BillId, ""+err.Error())
		return c.JSON(200, resp)
	}

	if len(modedata.BillId) == 0 {
		return c.JSON(200, resp)
	}

	model := new(oc.AccomplishOrderRequest)
	model.OrderSn = modedata.BillId
	model.ConfirmTime = kit.GetTimeNow()

	var logbuild strings.Builder
	logbuild.WriteString("推送京东到家已完成订单接收参数：" + kit.JsonEncode(model)) // 请求参数

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.RPC.AccomplishOrder(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		logbuild.WriteString("推送京东到家已完成订单处理结果：" + err.Error())
		glog.Error(logbuild.String()) // 记录日志
		return c.JSON(200, resp)
	}
	if grpcRes.Code != 200 {
		logbuild.WriteString("推送京东到家已完成订单处理结果：" + grpcRes.Error)
		glog.Error(logbuild.String()) // 记录日志
		return c.JSON(200, resp)
	}
	logbuild.WriteString("推送京东到家已完成订单订单处理结果：成功") // 处理结果
	glog.Info(logbuild.String())
	resp.Code = 0
	resp.Msg = "success"
	return c.JSON(200, resp)
}

//订单运单状态消息
func JddjOrderDeliveryStatusPush(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = 0
	resp.Msg = "success"
	params := GetJdParams(c)
	paramJson := GetJdParamJson(params, 0)

	modedata := dto.JddjDeliveryNode{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error("订单运单状态消息 ：", modedata.OrderSn, ""+err.Error())
		return c.JSON(200, resp)
	}

	model := new(oc.ElmDeliveryNodeRequest)
	random := rand.Intn(899) + 100
	deliveryId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
	intDeliveryId, err := strconv.ParseInt(deliveryId, 10, 64)
	model.DeliveryId = intDeliveryId
	model.OrderSn = modedata.OrderSn
	intStatus, _ := strconv.ParseInt(modedata.Status, 10, 32)
	model.Status = int32(intStatus)
	model.CourierName = modedata.CourierName
	model.CourierPhone = modedata.CourierPhone
	model.CreateTime = kit.GetTimeNow()

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.RPC.JddjDeliveryNode(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		glog.Error("京东到家订单物流状态推送："+modedata.OrderSn+"", err.Error())
		resp.Code = -1
		resp.Msg = "fail"
		return c.JSON(200, resp)
	}
	if grpcRes.Code != 200 {
		glog.Error("京东到家订单物流状态推送：" + modedata.OrderSn + "+" + grpcRes.Error)
		resp.Code = -1
		resp.Msg = "fail"
		return c.JSON(200, resp)
	}
	glog.Info("京东到家订单物流状态推送成功："+modedata.OrderSn+"", modedata.Status)
	return c.JSON(200, resp)
}

//门店商品库存上下架状态消息
//接口地址:    http://xxx.xxx.xxx/xxx/djsw/stockIsHave，其中红色部分自定义。
func JdStockIsHaveCallback(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = 0
	resp.Msg = "success"
	params := GetJdParams(c)

	appKey := params["app_key"].(string)
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(appKey)
	if retCode != code.Success {
		glog.Error("JddkOrderAdjustCallback", "GetStoreMasterIdFromAppKey", retCode)
	}

	paramJson := GetJdParamJson(params, storeMasterId)
	//err := json.Unmarshal([]byte(paramJson), &modedata)
	modedata := dto.ProductUpdateCall{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error(kit.RunFuncName(), "，json解析失败，", err, "，json：", paramJson)
		resp.Code = 400
		resp.Msg = err.Error()
		return c.JSON(200, resp)
	}
	//如果是商家操作，需要判断我们这边是否是商家的，否则下架
	if modedata.Vendibility == "0" {

	}
	glog.Infof("非接口操作商品状态变更：%+v", modedata)
	fmt.Println(modedata)
	return c.JSON(200, resp)
}

//推送修改  订单调整消息
func JddkOrderAdjustCallback(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = 0
	resp.Msg = "success"

	params := GetJdParams(c)

	appKey := params["app_key"].(string)
	storeMasterId, retCode := GetStoreMasterIdFromAppKey(appKey)
	if retCode != code.Success {
		glog.Error("JddkOrderAdjustCallback", "GetStoreMasterIdFromAppKey", retCode)
	}

	paramJson := GetJdParamJson(params, storeMasterId)

	modedata := dto.CallBackBase{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error(kit.RunFuncName(), "，json解析失败，", err, "，json：", paramJson)
		resp.Code = 400
		resp.Msg = err.Error()
		return c.JSON(200, resp)
	}
	glog.Infof("推送订单调整消息接口：%+v", modedata)

	ocClient := oc.GetOrderServiceClient()
	reqOrder := oc.JddjAdjustOrderRequest{
		OldOrderSn: modedata.BillId,
		UserName:   "京东后台操作",
	}
	var res *oc.BaseResponse
	//调整订单（取消原订单，重新生成订单）
	if res, err = ocClient.RPC.JddjAdjustCancelOrder(kit.SetTimeoutCtx(context.Background()), &reqOrder); err != nil {
		glog.Error(kit.RunFuncName(), "，调用JddjAdjustCancelOrder失败，", err)
		res.Message = err.Error()
		return c.JSON(200, resp)
	}
	if res.Code == 400 {
		resp.Code = 400
		resp.Msg = "订单调整取消原订单失败"
		return c.JSON(200, resp)
	} else {
		JddjOrderPush(modedata.BillId, storeMasterId, 1)
	}

	resp.Code = 0
	resp.Msg = "success"
	return c.JSON(200, resp)
}

//京东到家订单妥投消息
func JddjFinishOrderCallback(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = -1
	resp.Msg = "fail"
	params := GetJdParams(c)
	paramJson := GetJdParamJson(params, 0)
	glog.Info("推送京东到家已完成订单接收参数：" + paramJson + "")
	modedata := dto.JddjOrderFinish{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error("推送京东到家已完成订单完成订单出错 ：", modedata.OrderId, " "+err.Error())
		return c.JSON(200, resp)
	}

	if len(modedata.OrderId) == 0 {
		return c.JSON(200, resp)
	}
	model := new(oc.AccomplishOrderRequest)
	model.OrderSn = modedata.OrderId
	model.ConfirmTime = kit.GetTimeNow()

	glog.Info("推送京东到家已完成订单推送订单中心参数：" + kit.JsonEncode(model)) // 请求参数

	ocClient := oc.GetOrderServiceClient()
	var grpcRes *oc.BaseResponse
	grpcRes, err = ocClient.RPC.AccomplishOrder(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		glog.Error("推送京东到家已完成订单处理结果："+err.Error(), modedata.OrderId)
		return c.JSON(200, resp)
	}
	if grpcRes.Code != 200 {
		glog.Error("推送京东到家已完成订单处理结果："+grpcRes.Error, modedata.OrderId)
		return c.JSON(200, resp)
	}
	glog.Info("推送京东到家已完成订单订单处理结果：成功") // 处理结果
	resp.Code = 0
	resp.Msg = "success"
	return c.JSON(200, resp)
}

//订单拣货完成消息
func JddjPickFinishOrderCallback(c echo.Context) error {
	resp := dto.JddjReturn{}
	resp.Code = -1
	resp.Msg = "fail"
	params := GetJdParams(c)
	paramJson := GetJdParamJson(params, 0)
	glog.Info("推送京东到家订单拣货完成消息接收参数：" + paramJson + "")
	modedata := dto.JddjOrderCompleted{}
	err := json.Unmarshal([]byte(paramJson), &modedata)
	if err != nil {
		glog.Error("订单拣货完成消息 ：", modedata.BillId, ""+err.Error())
		return c.JSON(200, resp)
	}

	if len(modedata.BillId) == 0 {
		return c.JSON(200, resp)
	}
	model := new(oc.PickingOrderRequest)
	model.OrderSn = modedata.BillId
	storeMasterId, err := services.GetAppChannelByOrderSn(modedata.BillId)
	if err != nil {
		glog.Error("JddjPickFinishOrderCallback,", "GetAppChannelByOrderSn,", modedata.BillId)
		return c.JSON(200, resp)
	}
	model.StoreMasterId = storeMasterId

	glog.Info("推送京东到家订单拣货完成消息订单接收参数：" + kit.JsonEncode(model)) // 请求参数

	ocClient := oc.GetOrderServiceClient()
	grpcRes, err := ocClient.RPC.JddjPickingOrder(kit.SetTimeoutCtx(context.Background()), model)
	if err != nil {
		glog.Error("推送京东到家订单拣货完成消息订单处理结果：" + err.Error())
		return c.JSON(200, resp)
	}
	if grpcRes.Code != 200 {
		glog.Error("推送京东到家订单拣货完成消息订单处理结果：" + grpcRes.Error)
		return c.JSON(200, resp)
	}
	glog.Info("推送京东到家订单拣货完成消息订单订单处理结果：成功") // 处理结果
	resp.Code = 0
	resp.Msg = "success"
	resp.Data = ""
	return c.JSON(200, resp)
}

// 解析京东到家返回参数
func GetJdParams(c echo.Context) map[string]interface{} {
	body, _ := ioutil.ReadAll(c.Request().Body)
	strParams, _ := url.PathUnescape(string(body))
	params := make(map[string]interface{})
	if len(strParams) <= 0 {
		return params
	}
	decodeUrl, _ := url.QueryUnescape(strParams)
	entries := strings.Split(decodeUrl, "&")
	for _, item := range entries {
		parts := strings.Split(item, "=")
		params[parts[0]] = parts[1]
	}
	return params
}

// 通过jd_param_json参数获取京东到家请求入参
func GetCallbackParam(ctx echo.Context) map[string]interface{} {
	param := GetJdParams(ctx)
	if len(param) <= 0 {
		return param
	}
	callbackParam := make(map[string]interface{})
	appKey := param["app_key"].(string)
	jdParamJson := param["jd_param_json"].(string)

	encryptJdParamJson, _ := param["encrypt_jd_param_json"].(string)
	if encryptJdParamJson != "" {
		storeMasterId, retCode := app.GetStoreMasterId(appKey, app.Channel_JDDJ)
		if retCode != code.Success {
			glog.Error("京东参数解密获取StoreMasterId失败-", retCode, "-", encryptJdParamJson)
			return callbackParam
		}
		DecryptParamJson, err := utils.NewJddjHandle(storeMasterId).Decrypt(encryptJdParamJson)
		if err != nil {
			glog.Error("京东参数解密失败-", encryptJdParamJson, "-err:", err)
			return callbackParam
		}
		jdParamJson = DecryptParamJson
	}

	err := json.Unmarshal([]byte(jdParamJson), &callbackParam)
	if err != nil {
		glog.Error("京东参数解码出错-", err, "-", jdParamJson)
	}
	callbackParam["app_key"] = appKey

	return callbackParam
}

//获取京东回调的参数
//之前的版本读取 jd_param_json 20221015之后读取encrypt_jd_param_json
func GetJdParamJson(param map[string]interface{}, storeMasterId int32) string {
	jdParamJson := param["jd_param_json"].(string)

	encryptJdParamJson, _ := param["encrypt_jd_param_json"].(string)
	appKey := param["app_key"].(string)
	if encryptJdParamJson != "" {
		//如果没有传storeMasterId 再查一次
		if storeMasterId == 0 {
			getMasterId, retCode := app.GetStoreMasterId(appKey, app.Channel_JDDJ)
			if retCode != code.Success {
				glog.Error("京东参数解密获取StoreMasterId失败-", retCode, "-", encryptJdParamJson)
				return jdParamJson
			}
			storeMasterId = getMasterId
		}
		DecryptParamJson, err := utils.NewJddjHandle(storeMasterId).Decrypt(encryptJdParamJson)
		if err != nil {
			glog.Error("京东参数解密失败-", encryptJdParamJson, "-err:", err)
			return jdParamJson
		}
		jdParamJson = DecryptParamJson
	}
	return jdParamJson
}
