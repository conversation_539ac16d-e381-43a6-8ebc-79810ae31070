package middleware

import (
	"bytes"
	"external-ui/pkg/app"
	"external-ui/pkg/code"
	"external-ui/utils"
	"io/ioutil"
	"net/url"
	"sort"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

//校验渠道id和来源，并写入context
func JddjAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			body, _ := ioutil.ReadAll(c.Request().Body)
			bodyDecode, _ := url.PathUnescape(string(body))

			bodyMap, _ := utils.ParseQuery(bodyDecode)

			appKId := bodyMap.Get("app_key")
			glog.Info(c.Request().URL.Path, "，京东到家回调参数：", string(body), "appId:", appKId)
			//验签
			if c.Request().URL.String() != "/external/jddj-callback" {
				storeMasterId, retCode := app.GetStoreMasterId(appKId, app.Channel_JDDJ)
				if retCode != code.Success {
					glog.Error("JddjAuth-GetStoreMasterId", retCode)
					return c.JSON(200, utils.JdResponse{Code: "10013"})
				}
				if errorResponse := utils.NewJddjHandle(storeMasterId).AuthSign(bodyMap); errorResponse != nil {
					glog.Error("京东到家回调验签失败：", errorResponse.Msg, "，body：", bodyDecode)
					return c.JSON(200, errorResponse)
				}
			}

			//将body重新写入
			c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(body))

			return next(c)
		}
	}
}

//校验渠道id和来源，并写入context
func PriceSyncAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			body, _ := ioutil.ReadAll(c.Request().Body)
			bodyDecode, _ := url.PathUnescape(string(body))
			bodyMap, _ := url.ParseQuery(bodyDecode)
			glog.Info(c.Request().URL.Path, "请求参数：", bodyMap)
			//验签
			timestamp := c.Request().Header.Get("timestamp")
			signStr := c.Request().Header.Get("signStr")

			//排除GetProductPrice
			if c.Request().URL.Path == "/external/product/GetProductPrice" {
				//将body重新写入
				c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(body))
				return next(c)
			}
			//本地调试的话不验证签名
			if c.Request().Host != "localhost:7033" {
				if errorResponse := AuthSign(bodyDecode, timestamp, signStr); errorResponse != "" {
					glog.Error("验签失败：", errorResponse, "，body：", bodyDecode)
					return c.JSON(200, errorResponse)
				}
			}
			//将body重新写入
			c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(body))
			return next(c)
		}
	}
}

func AuthSign(body string, timestamp string, signStr string) string {
	retmes := ""
	OMSSecret := config.GetString("PriceSyncSecret")
	arrReceive := make(map[string]string)
	arrReceive["timestamp"] = timestamp
	arrReceive["secret"] = OMSSecret
	arrReceive["body"] = body
	signReceive, errmes := ElmSign(arrReceive)
	if signStr != signReceive {
		retmes = "验签不通过！！" + errmes
	}
	return retmes
}

func ElmSign(arr map[string]string) (string, string) {
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + arr[v]
	}
	return strings.ToUpper(kit.GetMd5(str)), str
}

//校验渠道id和来源，并写入context
func MTLog(name string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			body, _ := ioutil.ReadAll(c.Request().Body)

			glog.Info(c.Request().URL.Path, " "+name+"接口回调参数：", string(body))

			//将body重新写入
			c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(body))

			return next(c)
		}
	}
}
