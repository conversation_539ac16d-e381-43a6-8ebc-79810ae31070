#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3815), pid=11808, tid=1972
#
# JRE version:  (21.0.3+13) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.3+13-b509.11, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.rvet.cn': 

Host: Intel(R) Core(TM) i7-10870H CPU @ 2.20GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
Time: Sun Sep  8 17:05:47 2024  Windows 10 , 64 bit Build 19041 (10.0.19041.4717) elapsed time: 0.026381 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000017d99d9e7b0):  JavaThread "Unknown thread" [_thread_in_vm, id=1972, stack(0x0000000b70c00000,0x0000000b70d00000) (1024K)]

Stack: [0x0000000b70c00000,0x0000000b70d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e02f9]
V  [jvm.dll+0x8bd383]
V  [jvm.dll+0x8bf8de]
V  [jvm.dll+0x8bffc3]
V  [jvm.dll+0x2879c6]
V  [jvm.dll+0x6dcac5]
V  [jvm.dll+0x6d0f9a]
V  [jvm.dll+0x35f9bb]
V  [jvm.dll+0x367566]
V  [jvm.dll+0x3b8b56]
V  [jvm.dll+0x3b8e28]
V  [jvm.dll+0x33217c]
V  [jvm.dll+0x332e6b]
V  [jvm.dll+0x8845e9]
V  [jvm.dll+0x3c5f38]
V  [jvm.dll+0x86d80d]
V  [jvm.dll+0x45ab9e]
V  [jvm.dll+0x45c771]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd9905d108, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x0000017d9bf06f40 WorkerThread "GC Thread#0"                     [id=3336, stack(0x0000000b70d00000,0x0000000b70e00000) (1024K)]
  0x0000017d9bf18ef0 ConcurrentGCThread "G1 Main Marker"            [id=9916, stack(0x0000000b70e00000,0x0000000b70f00000) (1024K)]
  0x0000017d9bf1b0b0 WorkerThread "G1 Conc#0"                       [id=31848, stack(0x0000000b70f00000,0x0000000b71000000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd987552d7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd990d1858] Heap_lock - owner thread: 0x0000017d99d9e7b0

Heap address: 0x0000000602000000, size: 8160 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000602000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x0000017db05f0000,0x0000017db15e0000] _byte_map_base: 0x0000017dad5e0000

Marking Bits: (CMBitMap*) 0x0000017d9bf08560
 Bits: [0x0000017db15e0000, 0x0000017db9560000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.016 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff63a600000 - 0x00007ff63a60a000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\java.exe
0x00007ffe18f30000 - 0x00007ffe19128000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe18e20000 - 0x00007ffe18ee1000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe16ac0000 - 0x00007ffe16dbd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe16610000 - 0x00007ffe16710000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffdf5910000 - 0x00007ffdf5928000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\jli.dll
0x00007ffe17fc0000 - 0x00007ffe1815d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe16f30000 - 0x00007ffe16f52000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe18420000 - 0x00007ffe1844b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe01140000 - 0x00007ffe0115b000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\VCRUNTIME140.dll
0x00007ffe16dc0000 - 0x00007ffe16ed7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe167a0000 - 0x00007ffe1683d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe02f50000 - 0x00007ffe031ea000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffe172e0000 - 0x00007ffe1737e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe183f0000 - 0x00007ffe1841f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe0eb50000 - 0x00007ffe0eb5c000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\vcruntime140_1.dll
0x00007ffdd6400000 - 0x00007ffdd648d000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\msvcp140.dll
0x00007ffd98410000 - 0x00007ffd991c2000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\server\jvm.dll
0x00007ffe18a90000 - 0x00007ffe18b40000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe18350000 - 0x00007ffe183f0000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe18960000 - 0x00007ffe18a83000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe165e0000 - 0x00007ffe16607000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe18b40000 - 0x00007ffe18bab000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe07d70000 - 0x00007ffe07d97000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe0c460000 - 0x00007ffe0c46a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe15b20000 - 0x00007ffe15b6b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe15990000 - 0x00007ffe159a2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe14de0000 - 0x00007ffe14df2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe01080000 - 0x00007ffe0108a000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\jimage.dll
0x00007ffe141d0000 - 0x00007ffe143b4000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffdfd170000 - 0x00007ffdfd1a4000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe16710000 - 0x00007ffe16792000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffdf9d70000 - 0x00007ffdf9d8f000 	C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Users\<USER>\AppData\Local\Programs\GoLand\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.rvet.cn': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/GoLand/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/GoLand/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8556380160                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8556380160                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python312\Scripts\;C:\Program Files\Python312\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Program Files (x86)\nodejs\;C:\Program Files\Go\bin;D:\Program Files (x86)\Xshell 7\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;D:\www\php-8.3.7;D:\www\nginx-1.27.0;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 11208K (0% of 33418028K total physical memory with 5268496K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
OS uptime: 25 days 15:00 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xc8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 16 processors :
  Max Mhz: 2208, Current Mhz: 2208, Mhz Limit: 2208

Memory: 4k page, system-wide physical 32634M (5145M free)
TotalPageFile size 51762M (AvailPageFile size 76M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 71M, peak: 582M

vm_info: OpenJDK 64-Bit Server VM (21.0.3+13-b509.11) for windows-amd64 JRE (21.0.3+13-b509.11), built on 2024-08-12 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
