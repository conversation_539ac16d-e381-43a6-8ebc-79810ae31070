// Code generated by protoc-gen-go. DO NOT EDIT.
// source: stock/stock.proto

package stock

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type LockTakeStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 订单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 订单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime string `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	// 需要冻结货号及库存大小
	List []*SkuStock `protobuf:"bytes,4,rep,name=List,proto3" json:"List,omitempty"`
	// 是否为手动订单
	IsManual bool `protobuf:"varint,5,opt,name=IsManual,proto3" json:"IsManual,omitempty"`
	// 检查库存是否充足
	IsEnough             bool     `protobuf:"varint,6,opt,name=IsEnough,proto3" json:"IsEnough,omitempty"`
	BillType             int32    `protobuf:"varint,7,opt,name=BillType,proto3" json:"BillType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockTakeStockRequest) Reset()         { *m = LockTakeStockRequest{} }
func (m *LockTakeStockRequest) String() string { return proto.CompactTextString(m) }
func (*LockTakeStockRequest) ProtoMessage()    {}
func (*LockTakeStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{0}
}

func (m *LockTakeStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockTakeStockRequest.Unmarshal(m, b)
}
func (m *LockTakeStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockTakeStockRequest.Marshal(b, m, deterministic)
}
func (m *LockTakeStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockTakeStockRequest.Merge(m, src)
}
func (m *LockTakeStockRequest) XXX_Size() int {
	return xxx_messageInfo_LockTakeStockRequest.Size(m)
}
func (m *LockTakeStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LockTakeStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LockTakeStockRequest proto.InternalMessageInfo

func (m *LockTakeStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *LockTakeStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *LockTakeStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

func (m *LockTakeStockRequest) GetList() []*SkuStock {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *LockTakeStockRequest) GetIsManual() bool {
	if m != nil {
		return m.IsManual
	}
	return false
}

func (m *LockTakeStockRequest) GetIsEnough() bool {
	if m != nil {
		return m.IsEnough
	}
	return false
}

func (m *LockTakeStockRequest) GetBillType() int32 {
	if m != nil {
		return m.BillType
	}
	return 0
}

type LockTakeStockResponse struct {
	// 状态码，200：库存充足并冻结成功，201：库存不足并冻结成功，400：冻结失败，业务处理异常
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	// 业务错误信息
	Message              string   `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockTakeStockResponse) Reset()         { *m = LockTakeStockResponse{} }
func (m *LockTakeStockResponse) String() string { return proto.CompactTextString(m) }
func (*LockTakeStockResponse) ProtoMessage()    {}
func (*LockTakeStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{1}
}

func (m *LockTakeStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockTakeStockResponse.Unmarshal(m, b)
}
func (m *LockTakeStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockTakeStockResponse.Marshal(b, m, deterministic)
}
func (m *LockTakeStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockTakeStockResponse.Merge(m, src)
}
func (m *LockTakeStockResponse) XXX_Size() int {
	return xxx_messageInfo_LockTakeStockResponse.Size(m)
}
func (m *LockTakeStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LockTakeStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LockTakeStockResponse proto.InternalMessageInfo

func (m *LockTakeStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LockTakeStockResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type UnlockTakeStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 销售订单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 销售订单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime      string   `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	BillType             int32    `protobuf:"varint,4,opt,name=BillType,proto3" json:"BillType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockTakeStockRequest) Reset()         { *m = UnlockTakeStockRequest{} }
func (m *UnlockTakeStockRequest) String() string { return proto.CompactTextString(m) }
func (*UnlockTakeStockRequest) ProtoMessage()    {}
func (*UnlockTakeStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{2}
}

func (m *UnlockTakeStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockTakeStockRequest.Unmarshal(m, b)
}
func (m *UnlockTakeStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockTakeStockRequest.Marshal(b, m, deterministic)
}
func (m *UnlockTakeStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockTakeStockRequest.Merge(m, src)
}
func (m *UnlockTakeStockRequest) XXX_Size() int {
	return xxx_messageInfo_UnlockTakeStockRequest.Size(m)
}
func (m *UnlockTakeStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockTakeStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockTakeStockRequest proto.InternalMessageInfo

func (m *UnlockTakeStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *UnlockTakeStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UnlockTakeStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

func (m *UnlockTakeStockRequest) GetBillType() int32 {
	if m != nil {
		return m.BillType
	}
	return 0
}

type CommonResponse struct {
	// 状态码，200：业务处理成功，400：业务处理失败
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	// 业务错误信息
	Message              string   `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonResponse) Reset()         { *m = CommonResponse{} }
func (m *CommonResponse) String() string { return proto.CompactTextString(m) }
func (*CommonResponse) ProtoMessage()    {}
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{3}
}

func (m *CommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResponse.Unmarshal(m, b)
}
func (m *CommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResponse.Marshal(b, m, deterministic)
}
func (m *CommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResponse.Merge(m, src)
}
func (m *CommonResponse) XXX_Size() int {
	return xxx_messageInfo_CommonResponse.Size(m)
}
func (m *CommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResponse proto.InternalMessageInfo

func (m *CommonResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommonResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type SkuStock struct {
	// oms系统SkuId
	SkuId int64 `protobuf:"varint,1,opt,name=SkuId,proto3" json:"SkuId,omitempty"`
	// 期望库存大小
	Stock                int32    `protobuf:"varint,2,opt,name=Stock,proto3" json:"Stock,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuStock) Reset()         { *m = SkuStock{} }
func (m *SkuStock) String() string { return proto.CompactTextString(m) }
func (*SkuStock) ProtoMessage()    {}
func (*SkuStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{4}
}

func (m *SkuStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuStock.Unmarshal(m, b)
}
func (m *SkuStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuStock.Marshal(b, m, deterministic)
}
func (m *SkuStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuStock.Merge(m, src)
}
func (m *SkuStock) XXX_Size() int {
	return xxx_messageInfo_SkuStock.Size(m)
}
func (m *SkuStock) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuStock.DiscardUnknown(m)
}

var xxx_messageInfo_SkuStock proto.InternalMessageInfo

func (m *SkuStock) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SkuStock) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

type LockCommingStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 退款单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 退款单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime string `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	// 在途仓库货号及库存大小
	List []*SkuStock `protobuf:"bytes,4,rep,name=List,proto3" json:"List,omitempty"`
	// 是否为手动订单
	IsManual bool `protobuf:"varint,5,opt,name=IsManual,proto3" json:"IsManual,omitempty"`
	// 单据类型
	BillType             int32    `protobuf:"varint,6,opt,name=BillType,proto3" json:"BillType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockCommingStockRequest) Reset()         { *m = LockCommingStockRequest{} }
func (m *LockCommingStockRequest) String() string { return proto.CompactTextString(m) }
func (*LockCommingStockRequest) ProtoMessage()    {}
func (*LockCommingStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{5}
}

func (m *LockCommingStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockCommingStockRequest.Unmarshal(m, b)
}
func (m *LockCommingStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockCommingStockRequest.Marshal(b, m, deterministic)
}
func (m *LockCommingStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockCommingStockRequest.Merge(m, src)
}
func (m *LockCommingStockRequest) XXX_Size() int {
	return xxx_messageInfo_LockCommingStockRequest.Size(m)
}
func (m *LockCommingStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LockCommingStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LockCommingStockRequest proto.InternalMessageInfo

func (m *LockCommingStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *LockCommingStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *LockCommingStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

func (m *LockCommingStockRequest) GetList() []*SkuStock {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *LockCommingStockRequest) GetIsManual() bool {
	if m != nil {
		return m.IsManual
	}
	return false
}

func (m *LockCommingStockRequest) GetBillType() int32 {
	if m != nil {
		return m.BillType
	}
	return 0
}

type UnlockCommingStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 退款单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 退款单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime string `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	// 是否推送库存给阿闻
	IsPushStockToAwen bool `protobuf:"varint,4,opt,name=IsPushStockToAwen,proto3" json:"IsPushStockToAwen,omitempty"`
	// 单据类型
	BillType             int32    `protobuf:"varint,5,opt,name=BillType,proto3" json:"BillType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockCommingStockRequest) Reset()         { *m = UnlockCommingStockRequest{} }
func (m *UnlockCommingStockRequest) String() string { return proto.CompactTextString(m) }
func (*UnlockCommingStockRequest) ProtoMessage()    {}
func (*UnlockCommingStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{6}
}

func (m *UnlockCommingStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockCommingStockRequest.Unmarshal(m, b)
}
func (m *UnlockCommingStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockCommingStockRequest.Marshal(b, m, deterministic)
}
func (m *UnlockCommingStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockCommingStockRequest.Merge(m, src)
}
func (m *UnlockCommingStockRequest) XXX_Size() int {
	return xxx_messageInfo_UnlockCommingStockRequest.Size(m)
}
func (m *UnlockCommingStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockCommingStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockCommingStockRequest proto.InternalMessageInfo

func (m *UnlockCommingStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *UnlockCommingStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UnlockCommingStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

func (m *UnlockCommingStockRequest) GetIsPushStockToAwen() bool {
	if m != nil {
		return m.IsPushStockToAwen
	}
	return false
}

func (m *UnlockCommingStockRequest) GetBillType() int32 {
	if m != nil {
		return m.BillType
	}
	return 0
}

type CancelTakeStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 销售订单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 销售订单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime      string   `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelTakeStockRequest) Reset()         { *m = CancelTakeStockRequest{} }
func (m *CancelTakeStockRequest) String() string { return proto.CompactTextString(m) }
func (*CancelTakeStockRequest) ProtoMessage()    {}
func (*CancelTakeStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{7}
}

func (m *CancelTakeStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelTakeStockRequest.Unmarshal(m, b)
}
func (m *CancelTakeStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelTakeStockRequest.Marshal(b, m, deterministic)
}
func (m *CancelTakeStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelTakeStockRequest.Merge(m, src)
}
func (m *CancelTakeStockRequest) XXX_Size() int {
	return xxx_messageInfo_CancelTakeStockRequest.Size(m)
}
func (m *CancelTakeStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelTakeStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelTakeStockRequest proto.InternalMessageInfo

func (m *CancelTakeStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *CancelTakeStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CancelTakeStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

type CancelCommingStockRequest struct {
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode,omitempty"`
	// 退款单号
	OrderSn string `protobuf:"bytes,2,opt,name=OrderSn,proto3" json:"OrderSn,omitempty"`
	// 退款单创建时间，格式：2006-01-02 15:04:05
	OrderCreateTime string `protobuf:"bytes,3,opt,name=OrderCreateTime,proto3" json:"OrderCreateTime,omitempty"`
	// 单据类型
	BillType             int32    `protobuf:"varint,4,opt,name=BillType,proto3" json:"BillType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelCommingStockRequest) Reset()         { *m = CancelCommingStockRequest{} }
func (m *CancelCommingStockRequest) String() string { return proto.CompactTextString(m) }
func (*CancelCommingStockRequest) ProtoMessage()    {}
func (*CancelCommingStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6b29708bd139bde, []int{8}
}

func (m *CancelCommingStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelCommingStockRequest.Unmarshal(m, b)
}
func (m *CancelCommingStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelCommingStockRequest.Marshal(b, m, deterministic)
}
func (m *CancelCommingStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelCommingStockRequest.Merge(m, src)
}
func (m *CancelCommingStockRequest) XXX_Size() int {
	return xxx_messageInfo_CancelCommingStockRequest.Size(m)
}
func (m *CancelCommingStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelCommingStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelCommingStockRequest proto.InternalMessageInfo

func (m *CancelCommingStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *CancelCommingStockRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CancelCommingStockRequest) GetOrderCreateTime() string {
	if m != nil {
		return m.OrderCreateTime
	}
	return ""
}

func (m *CancelCommingStockRequest) GetBillType() int32 {
	if m != nil {
		return m.BillType
	}
	return 0
}

func init() {
	proto.RegisterType((*LockTakeStockRequest)(nil), "stock.LockTakeStockRequest")
	proto.RegisterType((*LockTakeStockResponse)(nil), "stock.LockTakeStockResponse")
	proto.RegisterType((*UnlockTakeStockRequest)(nil), "stock.UnlockTakeStockRequest")
	proto.RegisterType((*CommonResponse)(nil), "stock.CommonResponse")
	proto.RegisterType((*SkuStock)(nil), "stock.SkuStock")
	proto.RegisterType((*LockCommingStockRequest)(nil), "stock.LockCommingStockRequest")
	proto.RegisterType((*UnlockCommingStockRequest)(nil), "stock.UnlockCommingStockRequest")
	proto.RegisterType((*CancelTakeStockRequest)(nil), "stock.CancelTakeStockRequest")
	proto.RegisterType((*CancelCommingStockRequest)(nil), "stock.CancelCommingStockRequest")
}

func init() { proto.RegisterFile("stock/stock.proto", fileDescriptor_e6b29708bd139bde) }

var fileDescriptor_e6b29708bd139bde = []byte{
	// 500 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x55, 0xcd, 0x6e, 0xd3, 0x40,
	0x10, 0x66, 0x89, 0x9d, 0x86, 0x81, 0x12, 0xb2, 0xb4, 0xc5, 0x0d, 0x3f, 0xb2, 0x0c, 0x07, 0x1f,
	0x20, 0x95, 0x8a, 0xc4, 0x11, 0x89, 0x46, 0x3d, 0x44, 0xa4, 0x2a, 0xb2, 0x83, 0x90, 0xb8, 0x99,
	0x64, 0x94, 0x58, 0x76, 0x76, 0x83, 0xd7, 0x06, 0x71, 0xe5, 0x41, 0x90, 0x78, 0x22, 0x5e, 0x01,
	0x1e, 0x83, 0x1b, 0xf2, 0x6e, 0x6c, 0xc5, 0x1b, 0x07, 0x09, 0x2e, 0x56, 0x2f, 0x51, 0xbe, 0xf9,
	0xbc, 0xb3, 0xf3, 0xcd, 0xdf, 0x42, 0x4f, 0xa4, 0x7c, 0x1a, 0x9d, 0xc8, 0xdf, 0xc1, 0x2a, 0xe1,
	0x29, 0xa7, 0xa6, 0x04, 0xce, 0x6f, 0x02, 0x07, 0x63, 0x3e, 0x8d, 0x26, 0x41, 0x84, 0x7e, 0x6e,
	0xf1, 0xf0, 0x63, 0x86, 0x22, 0xa5, 0x4f, 0x60, 0xff, 0x5d, 0x90, 0xe0, 0x82, 0x67, 0x02, 0x87,
	0x7c, 0x86, 0x16, 0xb1, 0x89, 0x7b, 0xc3, 0xab, 0x1a, 0xa9, 0x05, 0x7b, 0x97, 0xc9, 0x0c, 0x13,
	0x9f, 0x59, 0xd7, 0x25, 0x5f, 0x40, 0xea, 0x42, 0x57, 0xfe, 0x1d, 0x26, 0x18, 0xa4, 0x38, 0x09,
	0x97, 0x68, 0xb5, 0xe4, 0x17, 0xba, 0x99, 0x3e, 0x06, 0x63, 0x1c, 0x8a, 0xd4, 0x32, 0xec, 0x96,
	0x7b, 0xf3, 0xb4, 0x3b, 0x50, 0x51, 0xfa, 0x51, 0xa6, 0xe2, 0x91, 0x24, 0xed, 0x43, 0x67, 0x24,
	0x2e, 0x02, 0x96, 0x05, 0xb1, 0x65, 0xda, 0xc4, 0xed, 0x78, 0x25, 0x56, 0xdc, 0x39, 0xe3, 0xd9,
	0x7c, 0x61, 0xb5, 0x0b, 0x4e, 0xe1, 0x9c, 0x3b, 0x0b, 0xe3, 0x78, 0xf2, 0x65, 0x85, 0xd6, 0x9e,
	0x4d, 0x5c, 0xd3, 0x2b, 0xb1, 0x73, 0x0e, 0x87, 0x9a, 0x74, 0xb1, 0xe2, 0x4c, 0x20, 0xa5, 0x60,
	0x94, 0x92, 0x4d, 0xcf, 0x28, 0x94, 0x5e, 0xa0, 0x10, 0xc1, 0x1c, 0x0b, 0xa5, 0x6b, 0xe8, 0x7c,
	0x23, 0x70, 0xf4, 0x96, 0xc5, 0xcd, 0x26, 0x71, 0x53, 0xa7, 0xa1, 0xe9, 0x7c, 0x09, 0xb7, 0x87,
	0x7c, 0xb9, 0xe4, 0xec, 0x3f, 0x05, 0xbe, 0x80, 0x4e, 0x51, 0x0d, 0x7a, 0x00, 0xa6, 0x1f, 0x65,
	0xa3, 0x99, 0x3c, 0xda, 0xf2, 0x14, 0x90, 0xd6, 0x9c, 0x96, 0x27, 0x4d, 0x4f, 0x01, 0xe7, 0x27,
	0x81, 0x7b, 0x79, 0x82, 0xf3, 0xcb, 0x43, 0x36, 0xbf, 0xc2, 0xed, 0x55, 0xa6, 0xb6, 0xad, 0xa5,
	0xf6, 0x07, 0x81, 0x63, 0x55, 0xfb, 0x66, 0x45, 0x3e, 0x85, 0xde, 0x48, 0xbc, 0xc9, 0xc4, 0x42,
	0xde, 0x3f, 0xe1, 0xaf, 0x3e, 0x23, 0x93, 0x7d, 0xd0, 0xf1, 0xb6, 0x89, 0x8a, 0x22, 0x53, 0x53,
	0xf4, 0x95, 0xc0, 0xd1, 0x30, 0x60, 0x53, 0x8c, 0x9b, 0xeb, 0x66, 0xe7, 0x3b, 0x81, 0x63, 0x15,
	0x44, 0xb3, 0x69, 0xfd, 0xcb, 0x54, 0x9d, 0xfe, 0x6a, 0xc1, 0x2d, 0x19, 0x96, 0x8f, 0xc9, 0xa7,
	0x70, 0x8a, 0x74, 0x0c, 0xfb, 0x95, 0x75, 0x42, 0xef, 0xaf, 0x7b, 0xad, 0x6e, 0xbf, 0xf6, 0x1f,
	0xd4, 0x93, 0x6a, 0x40, 0x9d, 0x6b, 0x74, 0x04, 0x5d, 0x6d, 0xa9, 0xd0, 0x87, 0xeb, 0x23, 0xf5,
	0xcb, 0xa6, 0x7f, 0xb8, 0xa6, 0xab, 0xb3, 0xae, 0x5c, 0x69, 0x15, 0x2d, 0x5d, 0xd5, 0x57, 0x7a,
	0xb7, 0xab, 0xd7, 0x70, 0x47, 0x9f, 0x68, 0xfa, 0x68, 0x43, 0x49, 0x4d, 0xb9, 0x76, 0x3b, 0xbb,
	0x04, 0xba, 0x3d, 0x3b, 0xd4, 0xae, 0xa8, 0xfc, 0x57, 0x87, 0xdb, 0x5d, 0x53, 0x3a, 0xdc, 0xd9,
	0x50, 0x3b, 0x1d, 0x9e, 0xdd, 0x7d, 0xdf, 0x1b, 0x9c, 0xf0, 0xa5, 0x78, 0x26, 0xdf, 0x4c, 0xf5,
	0x7e, 0x7e, 0x68, 0x4b, 0xf0, 0xfc, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x06, 0x83, 0x61, 0x4c,
	0x55, 0x07, 0x00, 0x00,
}
