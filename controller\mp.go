package controller

import (
	"external-ui/dto"
	"external-ui/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 模拟接单
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-arrange [POST]
func OrderArrange(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟骑手接受订单开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderArrange, model.DeliveryServiceCode)
	glog.Info("模拟骑手接受订单结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}

// @Summary 模拟取货
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-pickup [POST]
func OrderPickup(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟骑手取货开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderPickup, model.DeliveryServiceCode)
	glog.Info("模拟骑手取货结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}

// @Summary 模拟送达
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-deliver [POST]
func OrderDeliver(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟骑手送达开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderDeliver, model.DeliveryServiceCode)
	glog.Info("模拟骑手送达结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}

// @Summary 模拟改派
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-rearrange [POST]
func OrderRearrange(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟改派骑手开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderRearrange, model.DeliveryServiceCode)
	glog.Info("模拟改派骑手结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}

// @Summary 模拟上传异常
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-reportException [POST]
func OrderReportException(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟骑手上传异常开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderReportException, model.DeliveryServiceCode)
	glog.Info("模拟骑手上传异常结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}

// @Summary 模拟获取骑手位置
// @Tags 模拟测试
// @Accept json
// @Produce json
// @Param product body dto.Mpordertest true " "
// @Success 200 {object} dto.ExternalResponse
// @Failure 400 {object} dto.ExternalResponse
// @Router /mp-test/order-location [POST]
func OrderLocation(c echo.Context) error {
	model := new(dto.Mpordertest)
	result := new(dto.ExternalResponse)
	result.Code = 1
	if err := c.Bind(model); err != nil {
		return c.JSON(400, err.Error())
	}
	glog.Info("模拟获取骑手位置开始：", kit.JsonEncode(model))

	res, _ := utils.Mp(kit.JsonEncodeByte(model), utils.MpTestOrderLocation, model.DeliveryServiceCode)
	glog.Info("模拟获取骑手位置结束：", res)
	if res.Code == 200 {
		result.Code = 0
		return c.JSON(200, res)
	}
	return c.JSON(400, res)
}
