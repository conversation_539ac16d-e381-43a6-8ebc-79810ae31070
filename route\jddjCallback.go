package route

import (
	"external-ui/controller"
	myMiddleware "external-ui/middleware"

	"github.com/labstack/echo/v4"
)

func jddjCallbackGroup(e *echo.Group) {
	g := e.Group("/jddj-callback", myMiddleware.JddjAuth())
	// 用户申请取消消息回调
	g.POST("/djsw/applyCancelOrder", controller.JddjApplyCancelOrder)
	// 用户取消消息回调
	g.POST("/djsw/userCancelOrder", controller.JddjUserCancelOrder)
	// 商家审核用户取消申请消息回调
	g.POST("/djsw/venderAuditApplyCancelOrder", controller.JddjVenderAuditApplyCancelOrder)
	// 新建售后单消息回调
	g.POST("/djsw/newAfterSaleBill", controller.JddjNewAfterSaleBill)
	// 新建售后单申请消息回调
	g.POST("/djsw/newApplyAfterSaleBill", controller.JddjNewApplyAfterSaleBill)
	// 售后单状态消息回调
	g.POST("/djsw/afterSaleBillStatus", controller.JddjAfterSaleBillStatus)

	// 查询订单可售后商品金额接口
	g.POST("/jddj/calcMoney", controller.GetJddjOrderCalcMoney)
	// 商家自主发起售后接口
	g.POST("/jddj/merchantInitiateAfterSale", controller.JddjMerchantInitiateAfterSale)
	// 商家审核用户取消申请接口
	g.POST("/jddj/orderCancelOperate", controller.JddjOrderCancelOperate)
	// 修改售后单申请消息
	g.POST("/djsw/updateApplyAfterSaleBill", controller.JddjUpdateApplyAfterSaleBill)

	//创建订单
	g.POST("/djsw/newOrder", controller.JddjNewOrder)
	//订单完成
	g.POST("/djsw/orderFinishCompletely", controller.JddjOrderCompletedCallback)
	//订单妥投
	g.POST("/djsw/orderFinish", controller.JddjFinishOrderCallback)
	//订单拣货完成消息
	g.POST("/djsw/pickFinishOrder", controller.JddjPickFinishOrderCallback)
	//订单运单状态消息
	g.POST("/djsw/pushDeliveryStatus", controller.JddjOrderDeliveryStatusPush)
	//推送已确认订单
	//g.POST("/djsw/orderWaitOutStore", controller.JddkOrderConfirmCallback)
	//推送订单调整消息
	g.POST("/djsw/orderAdjust", controller.JddkOrderAdjustCallback)
	//上架商品可售和非可售状态变更
	g.POST("/djsw/stockIsHave", controller.JdStockIsHaveCallback)
	//回调token
	g.POST("", controller.JddjCallback)

}
