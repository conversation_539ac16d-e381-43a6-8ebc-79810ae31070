package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"sort"
	"strings"
)

//查询切片是否包含值
func IsValueInListStr(value string, list []string) bool {
	if len(list) == 0 {
		return false
	}
	for _, v := range list {
		if v == value {
			return true
		}
	}
	return false
}

// 通过key获取hash的元素值
func HashGet(key, field string) string {
	redisClient := GetRedisConn()
	val, err := redisClient.HGet(key, field).Result()
	if err == redis.Nil {
		return ""
	} else if err != nil {
		return ""
	}
	return val
}

// aes解密
func WxAesDecrypt(msg string) []byte {
	// 秘钥
	key := config.GetString("wx_secret_key")
	byteKey, _ := base64.StdEncoding.DecodeString(key + "=")
	// 密文
	byteMsg, _ := base64.StdEncoding.DecodeString(msg)
	// 偏移量
	iv := byteKey[:16]
	// 解密
	block, err := aes.NewCipher(byteKey)
	if err != nil {
		glog.Error("WxAesDecrypt 微信aes解密错误, err: ", err)
		return nil
	}
	aesDecrypt := cipher.NewCBCDecrypter(block, iv)
	decryptData := make([]byte, len(byteMsg))
	aesDecrypt.CryptBlocks(decryptData, byteMsg)
	decryptData = decryptData[20:]
	// 截取有效部分
	appId := config.GetString("wx_appid")
	data := strings.Split(string(decryptData), appId)[0]
	return []byte(data)
}

// 签名生成规则
func MakeSign(params map[string]string, body string, secret string) string {
	//签名步骤一：按字典序排序参数
	keys := make([]string, len(params))
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys) // 对键进行排序
	var builder strings.Builder
	for _, k := range keys {
		if params[k] != "" {
			builder.WriteString(k)
			builder.WriteString(params[k])
		}
	}

	//签名步骤二：在string后添加请求主体，最后在首尾加上client_secret
	signStr := secret + builder.String() + body + secret

	//签名步骤三：MD5加密,所有字符转为大写
	return strings.ToUpper(kit.GetMd5(signStr))
}
