package utils

import (
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"sort"
	"strings"
)

func Md5ToUUID(str string) string {
	md5str := strings.ToUpper(kit.GetMd5(str))
	return fmt.Sprintf("%s-%s-%s-%s-%s", md5str[0:8], md5str[8:12], md5str[12:16], md5str[16:20], md5str[20:32])
}

//下行返回结果的签名
func RequestSign(data map[string]interface{}) string {
	arr := make(map[string]string)
	for k, v := range data {
		if k == "body" {
			arr[k] = kit.JsonEncode(v)
		} else {
			arr[k] = v.(string)
		}
	}
	arr["secret"] = ElmSecret
	sign := ElmSign(arr)
	return sign
}

func ElmSign(arr map[string]string) string {
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + arr[v]
	}
	return strings.ToUpper(kit.GetMd5(str))
}
