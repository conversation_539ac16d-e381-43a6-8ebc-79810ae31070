package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"external-ui/pkg/app"
	"external-ui/pkg/code"
	"net/url"
	"strings"

	logger "github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// type jddjConfig struct {
// 	lock      sync.RWMutex
// 	appKey    string
// 	appSecret string
// 	token     string
// 	url       string
// }

// var (
// 	jConfig *jddjConfig
// )

// func init() {
// 	jConfig = &jddjConfig{
// 		//appSecret: config.GetString("jddjAppSecret"),
// 		//appKey:    config.GetString("jddjAppKey"),
// 		url: config.GetString("jddjUrl"),
// 	}

// 	jConfig.token = GetRedisConn().Get("channelJddjToken").Val()

// 	//token有有效期，需定时维护
// 	go func() {
// 		for {
// 			<-time.After(5 * time.Minute)
// 			jConfig.lock.Lock()
// 			jConfig.token = GetRedisConn().Get("channelJddjToken").Val()
// 			jConfig.lock.Unlock()
// 		}
// 	}()
// }

type jddjHandle struct {
	requestParams map[string]string
	appSecret     string
}

//京东错误码映射关系
//0 : 操作成功
//-1 : 操作失败
//-10000 : 业务重试
//10005 : 必填项参数未填
//10013 : 无效Token令牌
//10014 : 无效Sign签名
//10015 : API参数异常
//10018 : 不存在的方法名
type JdResponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

func NewJddjHandle(storeMasterId int32) *jddjHandle {
	// jConfig.lock.RLock()
	// defer jConfig.lock.RUnlock()

	appConfig, retCode := app.GetStoreMasterChannelAppConfig(storeMasterId, app.Channel_JDDJ)
	if retCode != code.Success {
		logger.Error("NewJddjHandle-app.GetStoreMasterChannelAppConfig", storeMasterId, app.Channel_JDDJ, retCode, appConfig)
		return &jddjHandle{}
	}
	return &jddjHandle{
		requestParams: map[string]string{
			"app_key":   appConfig.AppId, //jConfig.appKey,
			"format":    "json",
			"timestamp": kit.GetTimeNow(),
			"token":     appConfig.AppToken, //jConfig.token,
			"v":         "1.0",
		},
		appSecret: appConfig.AppSecret,
	}
}

//按app_key,format,jd_param_json,timestamp,token,v排序拼装参数名和参数值
//如：app_keyxxxformatxxxxjd_param_json{xxxx}timestampxxxxxxtokenxxxvx
//把appSecret夹在字符串的两端：appSecretXXXXappSecret
//使用MD5进行加密，再转化成大写
func (jH *jddjHandle) sign() {
	sort := []string{"app_key", "format", "jd_param_json", "timestamp", "token", "v"}

	builder := strings.Builder{}
	builder.WriteString(jH.appSecret)
	for _, v := range sort {
		builder.WriteString(v)
		builder.WriteString(jH.requestParams[v])
	}
	builder.WriteString(jH.appSecret)

	jH.requestParams["sign"] = strings.ToUpper(kit.GetMd5(builder.String()))
}

func (jH *jddjHandle) AuthSign(bodyMap url.Values) *JdResponse {
	if jH.requestParams["token"] != bodyMap.Get("token") {
		return &JdResponse{
			Code: "10013",
			Msg:  "无效Token令牌",
		}
	}

	jH.requestParams["timestamp"] = bodyMap.Get("timestamp")
	jH.requestParams["encrypt_jd_param_json"] = bodyMap.Get("encrypt_jd_param_json")
	jH.requestParams["jd_param_json"] = bodyMap.Get("jd_param_json")
	//如果加密的数据不为空 使用加密的
	if jH.requestParams["encrypt_jd_param_json"] != "" {
		encryptJdParamJson, err := jH.Decrypt(jH.requestParams["encrypt_jd_param_json"])
		if err != nil {
			return &JdResponse{
				Code: "10015",
				Msg:  "加密数据解密失败",
			}
		}
		jH.requestParams["jd_param_json"] = encryptJdParamJson
	}
	jH.requestParams["format"] = bodyMap.Get("format")
	jH.requestParams["v"] = bodyMap.Get("v")
	jH.sign()
	if jH.requestParams["sign"] != bodyMap.Get("sign") {
		return &JdResponse{
			Code: "10014",
			Msg:  "无效Sign签名",
		}
	}
	return nil
}

//解密 规则参考文档 https://openo2o.jddj.com/staticnew/widgets/resources.html?id=2016
func (jH *jddjHandle) Decrypt(text string) (string, error) {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("京东消息密文解码panic-", text, "-err:", err)
		}
	}()
	decodeData, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return "", err
	}
	key, iv := jH.GetAESKeyAndIv()
	//生成密码数据块cipher.Block
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	//解密模式
	blockMode := cipher.NewCBCDecrypter(block, iv)
	//输出到[]byte数组
	originData := make([]byte, len(decodeData))
	blockMode.CryptBlocks(originData, decodeData)
	data := strings.Trim(string(originData), "\x00")
	return data, nil
}

//加解密的key与偏移量iv采用appSecret的前16位和后16位
func (jH *jddjHandle) GetAESKeyAndIv() (key, iv []byte) {
	key = []byte(jH.appSecret[0:16])
	iv = []byte(jH.appSecret[16:32])
	return
}
