package dto

type MpPushShopStatus struct {
	//门店名称
	ShopName string `json:"shop_name" form:"shop_name" query:"shop_name"`
	//取货门店id，即合作方向美团提供的门店id
	ShopId string `json:"shop_id" form:"shop_id" query:"shop_id"`
	//10-审核驳回
	//20-审核通过
	//30-创建成功
	//40-上线可发单
	Status int32`json:"status" form:"status" query:"status"`
	//驳回原因
	RejectMessage string`json:"reject_message" form:"reject_message" query:"reject_message"`
	//开放平台分配的appkey，合作方唯一标识。
	AppKey string `json:"appkey" form:"appkey" query:"appkey"`
	//	时间戳，格式为long，时区为GMT+8，当前距 离Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
	Timestamp int64 `json:"timestamp" form:"timestamp" query:"timestamp"`
	//数据签名
	Sign string `json:"sign" form:"sign" query:"sign"`

}
