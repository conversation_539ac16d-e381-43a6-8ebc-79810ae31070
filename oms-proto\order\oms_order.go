package order

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type Client struct {
	lock    sync.Mutex
	Conn    *grpc.ClientConn
	Ctx     context.Context
	Cf      context.CancelFunc
	WeiMeng WeiMengOrderServiceClient
	Order   OrderServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

func SetTimeoutCtx(timeout time.Duration) {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), timeout)
}

func GetOmsOrderRpcClient() *Client {
	SetTimeoutCtx(time.Second * 30)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.oms-order")

	if url == "" {
		url = "127.0.0.1:8129"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("datacenter，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.WeiMeng = NewWeiMengOrderServiceClient(grpcClient.Conn)
		grpcClient.Order = NewOrderServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}
