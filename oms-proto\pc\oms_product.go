package pc

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type Client struct {
	lock    sync.Mutex
	Conn    *grpc.ClientConn
	Ctx     context.Context
	Cf      context.CancelFunc
	Product ProductServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

func SetTimeoutCtx(timeout time.Duration) {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), timeout)
}

func GetOmsProductClient() *Client {
	SetTimeoutCtx(time.Second * 30)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.oms-product")

	if url == "" {
		url = "127.0.0.1:8131"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("datacenter，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.Product = NewProductServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}
