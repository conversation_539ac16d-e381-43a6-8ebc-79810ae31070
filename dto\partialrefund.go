package dto

type PartialRefund struct {
	//订单号，数据库中请用bigint(20)存储此字段。
	OrderId int64 `json:"order_id" form:"order_id" query:"order_id"`
	//全额退款通知类型，参考值：apply-发起退款；agree-确认退款；reject-驳回退款；cancelRefund-用户取消退款申请；cancelRefundComplaint-用户取消退款申诉。
	//支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
	NotifyType string `json:"notify_type" form:"notify_type" query:"notify_type"`
	//本次退款申请的退款id
	RefundId int64 `json:"refund_id" form:"refund_id" query:"refund_id"`
	//本次退款申请的退款id
	CTime int32 `json:"ctime" form:"ctime" query:"ctime"`
	//申请退款的原因
	Reason string `json:"reason" form:"reason" query:"reason"`
	// 审核退款的原因
	ResReason string `json:"res_reason" form:"res_reason" query:"res_reason"`
	//退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
	//支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
	ResType int32 `json:"res_type" form:"res_type" query:"res_type"`
	//本次申请是否为用户申诉退款，参考值：0-否；1-是。如为1则表示用户第一次申请全额退款时已被商家驳回，本次用户发起的申诉会由美团客服介入处理，商家不能操作。
	IsAppeal int32 `json:"is_appeal" form:"is_appeal" query:"is_appeal"`
	//用户申请退款时上传的退款图片，多个图片url以英文逗号隔开，上限为9张图片。字段信息为json格式数组。
	Pictures string `json:"pictures" form:"pictures" query:"pictures"`
	//退款服务类型, 区分是否已开通退货退款售后业务。
	//未开通的场景：
	//0-退款流程或申诉流程
	//已开通场景：,1-仅退款流程,2-退款退货流程
	ServiceType string `json:"service_type" form:"service_type" query:"service_type"`
	//推送当前售后单的状态类型，仅适用支持退货退款业务的商家：
	//1-已申请,10-初审已同意,11-初审已驳回,16-初审已申诉,17-初审申诉已同意,18-初审申诉已驳回,20-终审已发起（用户已发货）,21-终审已同意
	//22-终审已驳回,26-终审已申诉,27-终审申诉已同意,28-终审申诉已驳回,30-已取消
	Status string `json:"status" form:"status" query:"status"`
	//推送当前仅退款或退货退款流程的发起方，仅适用于支持退货退款的商家。
	//1-用户,2-商家,3-客服,4-BD,5-系统,6-开放平台
	ApplyOpUserType string `json:"apply_op_user_type" form:"apply_op_user_type" query:"apply_op_user_type"`
	//部分退款商品信息，json格式数组。 部分退才有
	Food string `json:"food" form:"food" query:"food"`
	//物流信息集合，仅适用支持退货退款业务的品类；在用户提交物流信息以及商家终审的推送消息中展示。
	LogisticsInfo string `json:"logistics_info" form:"logistics_info" query:"logistics_info"`
	//订单数据状态标记。当订单中部分字段的数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
	//-1：有数据降级
	//0：无数据降级
	IncmpCode int32 `json:"incmp_code" form:"incmp_code" query:"incmp_code"`

	//本次退款的合计金额，单位是元。
	Money float64 `json:"money" form:"money" query:"money"`
}

// 部分退款的商品明细，适用于按件部分退和按克重退差价两种类型。
type Food struct {
	//     APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
	AppFoodCode string `protobuf:"bytes,1,opt,name=app_food_code,json=appFoodCode,proto3" json:"app_food_code"`
	//  商品名称
	FoodName string `protobuf:"bytes,2,opt,name=food_name,json=foodName,proto3" json:"food_name"`
	//  商品sku唯一标识码，字段信息限定长度不超过40个字符。
	SkuId string `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//  商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
	Upc string `protobuf:"bytes,4,opt,name=upc,proto3" json:"upc"`
	// 商品sku的规格名称
	Spec string `protobuf:"bytes,5,opt,name=spec,proto3" json:"spec"`
	// 本次退款的商品数量：(1)如为按件部分退款，此字段信息为本次退款商品sku的数量。(2)如为按克重退差价，此字段信息为0。
	Count int32 `protobuf:"varint,6,opt,name=count,proto3" json:"count"`
	//  商品sku单件需使用打包盒的数量。（商品维度，在创建/更新商品时维护的信息）
	BoxNum float64 `protobuf:"fixed64,7,opt,name=box_num,json=boxNum,proto3" json:"box_num"`
	//  商品sku的单个打包盒的价格，单位是元。
	BoxPrice float64 `protobuf:"fixed64,8,opt,name=box_price,json=boxPrice,proto3" json:"box_price"`
	// 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
	FoodPrice float64 `protobuf:"fixed64,9,opt,name=food_price,json=foodPrice,proto3" json:"food_price"`
	//  商品sku优惠前原价(单价)，单位是元。此字段信息为当前订单中单件商品sku的原价。
	OriginFoodPrice float64 `protobuf:"fixed64,10,opt,name=origin_food_price,json=originFoodPrice,proto3" json:"origin_food_price"`
	//  退款价格（单价），单位是元。此字段信息为当前订单中单件此商品sku的退款价格，是单价。(1)如购买多件商品sku，仅1件享优惠价，计算时商品优惠金额会进行等比分摊。
	// (2)如商品是按克重退差价，refund_price字段信息是计算优惠分摊后，单件商品sku重量差异部分的价格。
	RefundPrice float64 `protobuf:"fixed64,11,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price"`
	//  商品sku的已退重量，单位是克/g。此字段仅适用于退差价类型，为商品sku标价重量与实拣重量的差异重量。
	RefundedWeight float64 `protobuf:"fixed64,12,opt,name=refunded_weight,json=refundedWeight,proto3" json:"refunded_weight"`
}

type JSONData []struct {
	AppFoodCode  string       `json:"app_food_code"`
	AppID        int          `json:"app_id"`
	AppPoiCode   string       `json:"app_poi_code"`
	AppSpuCode   string       `json:"app_spu_code"`
	CategoryName string       `json:"categoryName"`
	Ctime        int          `json:"ctime"`
	DiffContents DiffContents `json:"diffContents"`
	Name         string       `json:"name"`
	OpAppKey     string       `json:"opAppKey"`
	OpName       string       `json:"opName"`
	OpReason     string       `json:"opReason"`
	Timestamp    int          `json:"timestamp"`
	Utime        int          `json:"utime"`
}
type IsSoldOut struct {
	Result int `json:"result"`
	Origin int `json:"origin"`
}
type DiffContentMap struct {
	IsSoldOut IsSoldOut `json:"is_sold_out"`
}
type Skus struct {
	Desc           string         `json:"desc"`
	DiffContentMap DiffContentMap `json:"diffContentMap"`
	ID             string         `json:"id"`
	OpType         int            `json:"opType"`
}
type DiffContents struct {
	SoldOutReason string    `json:"sold_out_reason"`
	Skus          []Skus    `json:"skus"`
	IsSoldOut     IsSoldOut `json:"is_sold_out"`
}
