package controller

import (
	"context"
	"encoding/json"
	"external-ui/dto"
	"external-ui/proto/oc"
	"external-ui/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"io/ioutil"
)

//配送订单状态同步
func MytDeliveryChange(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	MytData := dto.MytData{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	//body1 := `{"signature":"LDWKo7KDz30koAjryIGRJVj6_ZbkpiKMNOj0CfQ4iuM=","app_key":"efhlmqC4","token":"45895398f4cad5dfb1650bdd54709899","timestamp":1742021808860,"data":"{\"order_id\":\"9964128298775837\",\"shop_id\":\"5030012\",\"status\":\"DELIVERING\",\"rider_name\":\"\\u80e1\\u65ed\\u4e1c\",\"rider_phone\":\"19026142348\",\"logistic_tag\":\"ipaotui\",\"longitude\":\"114.027363\",\"latitude\":\"22.530398\",\"logistic_no\":\"2289855899973417\",\"update_time\":1742021808,\"is_transship\":false,\"is_express\":false}","command":"delivery_change","request_id":"686b397778a601b45fd2e1a3b52398a1"}`
	redisConn := utils.GetRedisConn()
	redisConn.Set("zhouTestttt", string(body), 0)
	glog.Info("麦芽田回调参数 Body", string(body))
	err := json.Unmarshal(body, &MytData)
	if err != nil {
		return c.JSON(400, "绑定参数出错")
	}
	//glog.Info("麦芽田回调参数 ", MytData)
	//model := MytData.Data
	model := dto.MytDeliveryStatus{}
	if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
		return c.JSON(400, "解析参数出错")
	}
	glog.Info("麦芽田回调解析成功", model)

	var params oc.DeliveryNodeRequest
	switch model.Status {
	case "PENDING":
		params.Status = 0
	case "GRABBED":
		params.Status = 20
	case "ATSHOP":
		params.Status = 15
	case "PICKUP":
		params.Status = 30
	case "DONE":
		params.Status = 50
	case "CANCEL":
		params.Status = 99
		params.CancelReason = "配送单取消"
	default:
		out.Code = 200
		return c.JSON(200, out)
	}

	params.DeliveryId = cast.ToInt64(model.OrderID)
	params.OrderSn = model.OrderID
	params.MtPeisongId = model.OrderID
	params.CourierName = model.RiderName
	params.CourierPhone = model.RiderPhone
	params.CreateTime = kit.GetTimeNow()
	params.IsMyt = 1

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.MtDeliveryNode(kit.SetTimeoutCtx(context.Background()), &params)
	glog.Info("zx麦芽田订单状态回调结束："+params.OrderSn, r)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	if r.Code != 200 {
		out.Code = 400
		out.Message = r.Error
		return c.JSON(200, out)
	}

	out.Code = 200
	return c.JSON(200, out)
}

//订单确认
func MytOrderConfirm(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	//MytData := dto.MytData{}
	//body, _ := ioutil.ReadAll(c.Request().Body)
	//err := json.Unmarshal(body, &MytData)
	//if err != nil {
	//	return c.JSON(400, "绑定参数出错")
	//}
	//glog.Info("麦芽田回调参数 ", MytData)
	//model := dto.MytDeliveryStatus{}
	//if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
	//	return c.JSON(400, "解析参数出错")
	//}
	out.Code = 200
	return c.JSON(200, out)
}

//订单状态同步
func MytOrderChange(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	//MytData := dto.MytData{}
	//body, _ := ioutil.ReadAll(c.Request().Body)
	//err := json.Unmarshal(body, &MytData)
	//if err != nil {
	//	return c.JSON(400, "绑定参数出错")
	//}
	//glog.Info("麦芽田回调参数 ", MytData)
	//model := dto.MytDeliveryStatus{}
	//if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
	//	return c.JSON(400, "解析参数出错")
	//}
	out.Code = 200
	return c.JSON(200, out)
}

//批量同步骑手位置操作
func MytMultiRiderLocation(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	//MytData := dto.MytData{}
	//body, _ := ioutil.ReadAll(c.Request().Body)
	//err := json.Unmarshal(body, &MytData)
	//if err != nil {
	//	return c.JSON(400, "绑定参数出错")
	//}
	//glog.Info("麦芽田回调参数 ", MytData)
	//model := dto.MytDeliveryStatus{}
	//if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
	//	return c.JSON(400, "解析参数出错")
	//}
	out.Code = 200
	return c.JSON(200, out)
}

//订单列表查询
func MytOrderlist(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	MytData := dto.MytData{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	err := json.Unmarshal(body, &MytData)
	if err != nil {
		return c.JSON(400, "绑定参数出错")
	}
	glog.Info("麦芽田回调参数 ", MytData)

	model := oc.MytOrderListRequest{}

	if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
		return c.JSON(400, "解析参数出错")
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.MytOrderList(kit.SetTimeoutCtx(context.Background()), &model)
	glog.Info("麦芽田获取订单列表 ", MytData, r)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	return c.JSON(200, r)
}

//查询订单详情时，调用此接口
func MytOrderDetail(c echo.Context) error {
	out := dto.MytBaseResponse{}
	out.Code = 400
	MytData := dto.MytData{}
	body, _ := ioutil.ReadAll(c.Request().Body)
	err := json.Unmarshal(body, &MytData)
	if err != nil {
		return c.JSON(400, "绑定参数出错")
	}
	glog.Info("麦芽田回调参数 ", MytData)

	model := oc.MytOrderListRequest{}

	if err = json.Unmarshal([]byte(MytData.Data), &model); err != nil {
		return c.JSON(400, "解析参数出错")
	}

	ocClient := oc.GetOrderServiceClient()
	r, err := ocClient.RPC.MytOrderDetail(kit.SetTimeoutCtx(context.Background()), &model)
	glog.Info("麦芽田获取订单详情： ", MytData, r)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	return c.JSON(200, r)
}
