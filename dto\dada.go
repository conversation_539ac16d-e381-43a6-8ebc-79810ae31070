package dto

type DaDaCallBackMode struct {
	//对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
	Signature string `json:"signature"`
	//达达物流订单号，默认为空
	ClientID string `json:"client_id"`
	//订单ID
	OrderID string `json:"order_id"`
	//订单状态(待接单＝1,待取货＝2,配送中＝3,已完成＝4,已取消＝5, 已追加待接单=8,妥投异常之物品返回中=9, 妥投异常之物品返回完成=10, 骑士到店=100,创建达达运单失败=1000）
	OrderStatus int `json:"order_status"`
	//订单取消原因,其他状态下默认值为空字符串
	CancelReason string `json:"cancel_reason"`
	//订单取消原因来源(1:达达配送员取消；2:商家主动取消；3:系统或客服取消；0:默认值)
	CancelFrom int `json:"cancel_from"`
	//达达配送员id，接单以后会传
	DmID int `json:"dm_id"`
	//配送员姓名，接单以后会传
	DmName string `json:"dm_name"`
	//配送员手机号，接单以后会传
	DmMobile string `json:"dm_mobile"`
	//更新时间，时间戳除了创建达达运单失败=1000的精确毫秒，其他时间戳精确到秒
	UpdateTime   int  `json:"update_time"`
	IsFinishCode bool `json:"is_finish_code"`
}

type AutoGenerated struct {
	Signature    string `json:"signature"`
	ClientID     string `json:"client_id"`
	OrderID      string `json:"order_id"`
	OrderStatus  int    `json:"order_status"`
	CancelReason string `json:"cancel_reason"`
	CancelFrom   int    `json:"cancel_from"`
	DmID         int    `json:"dm_id"`
	DmName       string `json:"dm_name"`
	DmMobile     string `json:"dm_mobile"`
	UpdateTime   int    `json:"update_time"`
	IsFinishCode bool   `json:"is_finish_code"`
}
