package middleware

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"io"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 授权列表
var allowList = map[string]string{
	"r1": "xzFRi8oI2x1IFMQ1QLVtXX7qRomSkZVj",
	"zl": "LjPGmM44zoQrkIqLNbq9YkwTVqekVjO2",
}

type AwenSignatureData struct {
	Uri         string
	Timestamp   int64
	NonceString string
	ContentMd5  string
	Secret      string
}

// AwenSignature 校验签名中间件
// 签名算法 http://192.168.254.35:8089/web/#/2?page_id=377
// 灵感来源 https://help.aliyun.com/document_detail/29012.html#section-5sq-amh-ty6
func AwenSignature(appIds ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
			//测试不加签名验证
			if env != "" && env != "sit1" {
				data, err := parseAwenSignature(c, appIds)
				if err != nil {
					return c.JSON(400, map[string]interface{}{"code": 400, "message:": err.Error()})
				}

				mac := hmac.New(sha1.New, []byte(data.Secret))
				mac.Write([]byte(fmt.Sprintf("%s\n%s\n%d\n%s\n%s\n", c.Request().Method, data.Uri, data.Timestamp, data.NonceString, data.ContentMd5)))
				signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

				if c.Request().Header.Get("a-signature") != signature {
					return c.JSON(400, map[string]interface{}{"code": 400, "message:": "签名错误"})
				}
			}

			return next(c)
		}
	}
}

// 获取验签参数
func parseAwenSignature(c echo.Context, ids []string) (sd *AwenSignatureData, err error) {
	sd = new(AwenSignatureData)
	appId := c.Request().Header.Get("a-id")
	if len(appId) == 0 {
		return nil, errors.New("请求头AppId不能为空")
	}

	if len(ids) > 0 {
		var allow bool
		for _, id := range ids {
			if id == appId {
				allow = true
				break
			}
		}
		if !allow {
			return nil, errors.New("AppId未授权")
		}
	}

	if secret, has := allowList[appId]; !has {
		return nil, errors.New("AppId不存在")
	} else {
		sd.Secret = secret
	}

	sd.Timestamp, _ = strconv.ParseInt(c.Request().Header.Get("a-timestamp"), 0, 0)
	if sd.Timestamp == 0 {
		return nil, errors.New("请求头时间戳不存在或解析错误")
	}
	dffTime := time.Now().Unix() - sd.Timestamp
	// 5分钟以前 或者1分钟以后
	if dffTime > 300 || dffTime < -60 {
		return nil, errors.New("请求头时间戳不在有效范围内")
	}

	sd.NonceString = c.Request().Header.Get("a-nonce-string")
	if len(sd.NonceString) < 16 {
		return nil, errors.New("请求头随机字符串至少16位")
	}

	if len(c.Request().Header.Get("a-signature")) == 0 {
		return nil, errors.New("请求头签名不能为空")
	}

	// 对外提供的是external地址
	sd.Uri = strings.Replace(c.Request().URL.Path, "external-ui", "external", 1)

	queryParams := c.QueryParams()
	if len(queryParams) > 0 {
		var queryKeys []string
		// key升序后
		for k, _ := range queryParams {
			queryKeys = append(queryKeys, k)
		}
		sort.Strings(queryKeys)
		for i, key := range queryKeys {
			if i == 0 {
				sd.Uri += "?" + key + "=" + queryParams.Get(key)
			} else {
				sd.Uri += "&" + key + "=" + queryParams.Get(key)
			}
		}
	}

	// 请求body
	if c.Request().Body != nil { // Read
		reqBody, _ := io.ReadAll(c.Request().Body)
		c.Request().Body = io.NopCloser(bytes.NewBuffer(reqBody)) // Reset
		if len(reqBody) > 0 {
			h := md5.New()
			h.Write(reqBody)
			sd.ContentMd5 = strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
		}
	}

	return
}
